{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/hooks/*": ["./hooks/*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/styles/*": ["./styles/*"], "@/app/*": ["./app/*"]}, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}