import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    // Available Cerebras models with their specifications
    const models = [
      {
        id: 'llama-4-scout-17b-16e-instruct',
        name: 'Llama 4 Scout 17B',
        description: 'Latest reasoning model with enhanced capabilities',
        contextLength: 128000,
        speed: 'Ultra-fast',
        accuracy: 'High',
        useCase: 'General purpose, reasoning, creative writing',
        pricing: 'Premium',
        features: ['Reasoning', 'Code Generation', 'Creative Writing', 'Analysis'],
        createdAt: '2025-01-01T00:00:00Z',
        ownedBy: 'Meta',
      },
      {
        id: 'llama-3.3-70b',
        name: 'Llama 3.3 70B',
        description: 'Most powerful model for complex tasks',
        contextLength: 128000,
        speed: '450 tokens/sec',
        accuracy: 'Highest',
        useCase: 'Complex reasoning, professional content, technical tasks',
        pricing: 'Premium',
        features: ['Advanced Reasoning', 'Technical Writing', 'Code Review', 'Research'],
        createdAt: '2024-12-01T00:00:00Z',
        ownedBy: 'Meta',
      },
      {
        id: 'llama3.1-8b',
        name: 'Llama 3.1 8B',
        description: 'Fast and efficient for everyday tasks',
        contextLength: 128000,
        speed: '1800 tokens/sec',
        accuracy: 'High',
        useCase: 'Quick responses, chat, simple content generation',
        pricing: 'Standard',
        features: ['Fast Response', 'Chat', 'Simple Generation', 'Efficiency'],
        createdAt: '2024-07-01T00:00:00Z',
        ownedBy: 'Meta',
      },
      {
        id: 'qwen-3',
        name: 'Qwen 3',
        description: 'Hybrid reasoning model with thinking tokens',
        contextLength: 128000,
        speed: 'Variable',
        accuracy: 'High',
        useCase: 'Step-by-step reasoning, problem solving',
        pricing: 'Standard',
        features: ['Thinking Tokens', 'Problem Solving', 'Math', 'Logic'],
        createdAt: '2024-11-01T00:00:00Z',
        ownedBy: 'Alibaba',
      },
    ];

    // Add performance metrics
    const modelsWithMetrics = models.map(model => ({
      ...model,
      metrics: {
        averageLatency: model.id.includes('8b') ? '0.5s' : model.id.includes('70b') ? '1.2s' : '0.8s',
        tokensPerSecond: model.id.includes('8b') ? 1800 : model.id.includes('70b') ? 450 : 1200,
        availability: '99.9%',
        costPerToken: model.pricing === 'Premium' ? 0.0008 : 0.0004,
      },
      capabilities: {
        textGeneration: true,
        codeGeneration: true,
        reasoning: model.id.includes('scout') || model.id.includes('qwen'),
        multimodal: false,
        functionCalling: model.id.includes('scout') || model.id.includes('70b'),
        streaming: true,
      },
    }));

    return NextResponse.json({
      models: modelsWithMetrics,
      total: modelsWithMetrics.length,
      timestamp: new Date().toISOString(),
      cerebrasInfo: {
        platform: 'Cerebras Wafer-Scale Engine',
        infrastructure: 'CS-3 Systems',
        advantage: '20x faster than traditional GPU solutions',
        precision: '16-bit native weights for highest accuracy',
        freeQuota: '1M tokens daily',
      },
    });
  } catch (error) {
    console.error('Models API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch models' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { modelId } = await req.json();
    
    if (!modelId) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Simulate model validation/testing
    const isValid = ['llama-4-scout-17b-16e-instruct', 'llama-3.3-70b', 'llama3.1-8b', 'qwen-3'].includes(modelId);
    
    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid model ID' },
        { status: 400 }
      );
    }

    // Return model status
    return NextResponse.json({
      modelId,
      status: 'available',
      health: 'healthy',
      lastChecked: new Date().toISOString(),
      estimatedLatency: modelId.includes('8b') ? '0.5s' : '1.2s',
    });
  } catch (error) {
    console.error('Model validation error:', error);
    return NextResponse.json(
      { error: 'Model validation failed' },
      { status: 500 }
    );
  }
}