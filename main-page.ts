import dynamic from 'next/dynamic';
import { Metadata } from 'next';

// Dynamically import the main component to avoid SSR issues
const CerebrasStudio = dynamic(() => import('../components/CerebrasStudio'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-2xl font-bold text-white mb-2">Loading Cerebras Studio</h2>
        <p className="text-gray-400">Initializing ultra-fast AI inference...</p>
      </div>
    </div>
  ),
});

export const metadata: Metadata = {
  title: 'Cerebras Studio - Ultra-Fast AI Creative Platform',
  description: 'Experience the world\'s fastest AI inference with Cerebras Studio. Generate content, code, and creative writing at lightning speed with 1,800 tokens per second.',
  openGraph: {
    title: 'Cerebras Studio - Ultra-Fast AI Creative Platform',
    description: 'Experience the world\'s fastest AI inference with Cerebras Studio.',
    type: 'website',
    url: 'https://cerebras-studio.vercel.app',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Cerebras Studio Interface',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Cerebras Studio - Ultra-Fast AI Creative Platform',
    description: 'Experience the world\'s fastest AI inference with Cerebras Studio.',
    images: ['/og-image.png'],
  },
};

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <CerebrasStudio />
    </main>
  );
}