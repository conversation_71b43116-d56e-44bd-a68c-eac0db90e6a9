{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1", "sfo1", "fra1"], "functions": {"app/api/**/*.ts": {"runtime": "nodejs20.x", "maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/api/cerebras/(.*)", "destination": "https://api.cerebras.ai/v1/$1"}], "env": {"CEREBRAS_API_KEY": "@cerebras_api_key", "NEXT_PUBLIC_APP_URL": "https://cerebras-studio.vercel.app"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "crons": [], "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true}}