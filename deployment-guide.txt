# 🚀 Deployment Guide

This guide covers deploying Cerebras Studio to various platforms with optimal configurations for production use.

## 📋 Pre-Deployment Checklist

### **Environment Setup**
- [ ] Cerebras API key obtained from [cerebras.ai](https://cerebras.ai)
- [ ] Environment variables configured
- [ ] Dependencies installed locally
- [ ] Application tested in development mode
- [ ] Build process verified (`npm run build`)

### **Performance Optimization**
- [ ] Images optimized and compressed
- [ ] Bundle size analyzed and optimized
- [ ] Core Web Vitals tested
- [ ] Lighthouse audit passed (90+ scores)

## 🎯 Vercel Deployment (Recommended)

Vercel provides the best experience for Next.js applications with edge optimization.

### **Method 1: One-Click Deploy**

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/cerebras-studio)

### **Method 2: Vercel CLI**

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel
   ```

4. **Configure Environment Variables**
   ```bash
   vercel env add CEREBRAS_API_KEY
   ```

### **Method 3: GitHub Integration**

1. **Connect Repository**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure build settings

2. **Environment Variables**
   ```env
   CEREBRAS_API_KEY=your_api_key_here
   NEXT_PUBLIC_APP_URL=https://your-app.vercel.app
   ```

3. **Deploy**
   - Push to main branch
   - Automatic deployment triggered

### **Vercel Configuration**

Optimize your `vercel.json`:

```json
{
  "framework": "nextjs",
  "regions": ["iad1", "sfo1", "fra1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

## 🌐 Netlify Deployment

### **Method 1: Netlify CLI**

1. **Install Netlify CLI**
   ```bash
   npm install -g netlify-cli
   ```

2. **Build Application**
   ```bash
   npm run build
   npm run export
   ```

3. **Deploy**
   ```bash
   netlify deploy --prod --dir=out
   ```

### **Method 2: Git Integration**

1. **Connect Repository**
   - Go to [netlify.com](https://netlify.com)
   - Import from Git
   - Configure build settings

2. **Build Settings**
   ```
   Build command: npm run build && npm run export
   Publish directory: out
   ```

3. **Environment Variables**
   ```env
   CEREBRAS_API_KEY=your_api_key_here
   NEXT_PUBLIC_APP_URL=https://your-app.netlify.app
   ```

## 🚂 Railway Deployment

### **Railway CLI Deployment**

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and Initialize**
   ```bash
   railway login
   railway init
   ```

3. **Add Environment Variables**
   ```bash
   railway variables set CEREBRAS_API_KEY=your_api_key_here
   ```

4. **Deploy**
   ```bash
   railway up
   ```

### **Railway Configuration**

Create `railway.json`:

```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/api/health"
  }
}
```

## ☁️ AWS Amplify

### **Amplify Console Deployment**

1. **Connect Repository**
   - Go to AWS Amplify Console
   - Connect your GitHub/GitLab repository

2. **Build Settings**
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

3. **Environment Variables**
   ```env
   CEREBRAS_API_KEY=your_api_key_here
   NEXT_PUBLIC_APP_URL=https://your-app.amplifyapp.com
   ```

## 🐳 Docker Deployment

### **Dockerfile**

```dockerfile
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

### **Docker Compose**

```yaml
version: '3.8'
services:
  cerebras-studio:
    build: .
    ports:
      - "3000:3000"
    environment:
      - CEREBRAS_API_KEY=${CEREBRAS_API_KEY}
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    restart: unless-stopped
```

### **Deploy Commands**

```bash
# Build image
docker build -t cerebras-studio .

# Run container
docker run -p 3000:3000 -e CEREBRAS_API_KEY=your_key cerebras-studio

# Using Docker Compose
docker-compose up -d
```

## 🔧 Custom Server Deployment

### **VPS/Dedicated Server**

1. **Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2
   sudo npm install -g pm2
   ```

2. **Application Deployment**
   ```bash
   # Clone repository
   git clone https://github.com/yourusername/cerebras-studio.git
   cd cerebras-studio
   
   # Install dependencies
   npm ci --only=production
   
   # Build application
   npm run build
   
   # Start with PM2
   pm2 start npm --name "cerebras-studio" -- start
   pm2 save
   pm2 startup
   ```

3. **Nginx Configuration**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 📊 Performance Optimization

### **Vercel Edge Optimization**

```javascript
// next.config.js
const nextConfig = {
  experimental: {
    runtime: 'edge',
  },
  images: {
    loader: 'custom',
    loaderFile: './lib/image-loader.js',
  },
};
```

### **CDN Configuration**

```javascript
// Enable static optimization
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer({
  trailingSlash: true,
  generateEtags: false,
  poweredByHeader: false,
});
```

### **Caching Strategy**

```javascript
// API route caching
export const config = {
  runtime: 'edge',
  regions: ['iad1', 'sfo1'],
};

// Response caching
const response = new Response(data, {
  headers: {
    'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=43200',
  },
});
```

## 🔐 Security Configuration

### **Environment Variables Security**

```bash
# Production environment variables
CEREBRAS_API_KEY=sk-xxx              # Required
NODE_ENV=production                   # Required
NEXT_PUBLIC_APP_URL=https://...      # Required
NEXT_TELEMETRY_DISABLED=1            # Optional
```

### **Security Headers**

```javascript
// next.config.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
];
```

## 📈 Monitoring & Analytics

### **Error Monitoring**

```bash
# Sentry integration
npm install @sentry/nextjs

# Environment variables
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_SENTRY_DSN=your_public_dsn
```

### **Performance Monitoring**

```javascript
// Web Vitals tracking
export function reportWebVitals(metric) {
  console.log(metric);
  // Send to analytics
}
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **API Key Issues**
   ```bash
   # Verify environment variables
   vercel env ls
   
   # Add missing variables
   vercel env add CEREBRAS_API_KEY
   ```

3. **Performance Issues**
   ```bash
   # Analyze bundle size
   npm run analyze
   
   # Check lighthouse scores
   npx lighthouse https://your-app.com
   ```

### **Health Checks**

```javascript
// API health check endpoint
export async function GET() {
  return Response.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
  });
}
```

## 📞 Support

If you encounter issues during deployment:

- **Documentation**: [Deploy Guide](https://cerebras-studio-docs.vercel.app/deploy)
- **Issues**: [GitHub Issues](https://github.com/yourusername/cerebras-studio/issues)
- **Community**: [Discord Support](https://discord.gg/cerebras-studio)

---

**Happy Deploying! 🚀**