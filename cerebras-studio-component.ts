'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useChat } from 'ai/react';
import toast from 'react-hot-toast';
import { 
  Sparkles, 
  Code, 
  FileText, 
  PenTool, 
  Settings, 
  Download, 
  Upload, 
  Zap, 
  Brain, 
  Palette,
  MessageSquare,
  Play,
  Copy,
  Check,
  Moon,
  Sun,
  Github,
  Twitter,
  Linkedin,
  ChevronRight,
  Layers,
  BarChart3,
  Wand2,
  Send,
  StopCircle,
  RefreshCw
} from 'lucide-react';

interface Model {
  id: string;
  name: string;
  description: string;
  speed?: string;
  contextLength?: number;
}

interface Tab {
  id: string;
  label: string;
  icon: any;
  description: string;
}

const CerebrasStudio: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('text');
  const [isDarkMode, setIsDarkMode] = useState<boolean>(true);
  const [selectedModel, setSelectedModel] = useState<string>('llama-4-scout-17b-16e-instruct');
  const [copied, setCopied] = useState<boolean>(false);
  const [apiKey, setApiKey] = useState<string>('');
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [characterCount, setCharacterCount] = useState<number>(0);
  const [wordCount, setWordCount] = useState<number>(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    stop,
    reload,
    setInput,
  } = useChat({
    api: '/api/generate',
    body: {
      model: selectedModel,
    },
    onError: (error) => {
      toast.error(error.message || 'An error occurred while generating content');
    },
    onFinish: () => {
      toast.success('Content generated successfully!');
    },
  });

  const models: Model[] = [
    { 
      id: 'llama-4-scout-17b-16e-instruct', 
      name: 'Llama 4 Scout 17B', 
      description: 'Latest reasoning model',
      speed: 'Ultra-fast',
      contextLength: 128000
    },
    { 
      id: 'llama-3.3-70b', 
      name: 'Llama 3.3 70B', 
      description: 'Most powerful model',
      speed: '450 tokens/sec',
      contextLength: 128000
    },
    { 
      id: 'llama3.1-8b', 
      name: 'Llama 3.1 8B', 
      description: 'Fast and efficient',
      speed: '1800 tokens/sec',
      contextLength: 128000
    },
  ];

  const tabs: Tab[] = [
    { id: 'text', label: 'Text Studio', icon: PenTool, description: 'Creative writing & content generation' },
    { id: 'code', label: 'Code Generator', icon: Code, description: 'Programming assistance & code generation' },
    { id: 'document', label: 'Document AI', icon: FileText, description: 'Document analysis & summarization' },
    { id: 'creative', label: 'Creative Writer', icon: Palette, description: 'Story & character development' },
  ];

  const prompts: Record<string, string[]> = {
    text: [
      'Write a compelling product description for a new AI-powered productivity app',
      'Create a professional email template for client outreach',
      'Generate a blog post about the future of artificial intelligence',
      'Write a social media campaign for a sustainable fashion brand'
    ],
    code: [
      'Create a React component for a modern dashboard',
      'Write a Python script to analyze CSV data',
      'Build a responsive navigation bar with Tailwind CSS',
      'Generate a REST API endpoint with error handling'
    ],
    document: [
      'Analyze this document and provide key insights',
      'Summarize the main points in bullet format',
      'Extract action items and deadlines',
      'Create an executive summary'
    ],
    creative: [
      'Create a character profile for a sci-fi protagonist',
      'Write the opening scene of a mystery novel',
      'Develop a plot outline for a romantic comedy',
      'Create dialogue between two opposing characters'
    ]
  };

  useEffect(() => {
    setCharacterCount(input.length);
    setWordCount(input.trim() ? input.trim().split(/\s+/).length : 0);
  }, [input]);

  useEffect(() => {
    // Load API key from localStorage
    const savedApiKey = localStorage.getItem('cerebras-api-key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  const handleApiKeyChange = (newApiKey: string) => {
    setApiKey(newApiKey);
    localStorage.setItem('cerebras-api-key', newApiKey);
  };

  const copyToClipboard = () => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.content) {
      navigator.clipboard.writeText(lastMessage.content);
      setCopied(true);
      toast.success('Copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const downloadContent = () => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.content) {
      const element = document.createElement('a');
      const file = new Blob([lastMessage.content], { type: 'text/plain' });
      element.href = URL.createObjectURL(file);
      element.download = `cerebras-studio-${activeTab}-${Date.now()}.txt`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
      toast.success('Downloaded successfully!');
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    if (!apiKey) {
      toast.error('Please enter your Cerebras API key in settings');
      setShowSettings(true);
      return;
    }

    handleSubmit(e);
  };

  const lastMessage = messages[messages.length - 1];
  const hasResponse = lastMessage?.role === 'assistant';

  return (
    <div className={`min-h-screen transition-all duration-500 ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Cerebras Studio
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">AI-Powered Creative Platform</p>
                </div>
              </div>
              <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-green-50 dark:bg-green-900/20 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-700 dark:text-green-400 text-sm font-medium">Ultra-Fast Inference</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </button>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <Settings className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-2">
                <a href="https://github.com" className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <Github className="h-5 w-5" />
                </a>
                <a href="https://twitter.com" className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <Twitter className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="absolute right-0 top-0 h-full w-96 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-800 shadow-2xl">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">Settings</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Cerebras API Key</label>
                  <input
                    type="password"
                    value={apiKey}
                    onChange={(e) => handleApiKeyChange(e.target.value)}
                    placeholder="Enter your Cerebras API key"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-800"
                  />
                  <p className="text-xs text-gray-500 mt-1">Get your free API key from cerebras.ai</p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Model Selection</label>
                  <select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-800"
                  >
                    {models.map(model => (
                      <option key={model.id} value={model.id}>
                        {model.name} - {model.description}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                  <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">Performance Stats</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Speed:</span>
                      <span className="font-medium">1,800 tokens/sec</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Context:</span>
                      <span className="font-medium">128K tokens</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accuracy:</span>
                      <span className="font-medium">16-bit precision</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-4 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group relative px-6 py-4 rounded-2xl transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25'
                    : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`h-5 w-5 ${activeTab === tab.id ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`} />
                  <div className="text-left">
                    <div className={`font-medium ${activeTab === tab.id ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {tab.label}
                    </div>
                    <div className={`text-xs ${activeTab === tab.id ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400'}`}>
                      {tab.description}
                    </div>
                  </div>
                </div>
                {activeTab === tab.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-20 animate-pulse"></div>
                )}
              </button>
            );
          })}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Input</h2>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{characterCount} chars</span>
                    <span className="text-gray-300">•</span>
                    <span className="text-sm text-gray-500">{wordCount} words</span>
                  </div>
                </div>
              </div>
              
              <form onSubmit={handleFormSubmit} className="p-6">
                <textarea
                  ref={textareaRef}
                  value={input}
                  onChange={handleInputChange}
                  placeholder={`What would you like to ${activeTab === 'code' ? 'build' : 'create'} today?`}
                  className="w-full h-64 p-4 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none bg-gray-50 dark:bg-gray-900 transition-all duration-200"
                  disabled={isLoading}
                />
                
                {/* Prompt Suggestions */}
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Prompts:</p>
                  <div className="flex flex-wrap gap-2">
                    {prompts[activeTab]?.slice(0, 2).map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setInput(suggestion)}
                        className="text-xs px-3 py-1 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-full hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors"
                        disabled={isLoading}
                      >
                        {suggestion.length > 40 ? suggestion.slice(0, 40) + '...' : suggestion}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 mt-6">
                  <button
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    className="flex-1 group relative px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg shadow-purple-500/25"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      {isLoading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          <span>Generating...</span>
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4" />
                          <span>Generate with Cerebras</span>
                        </>
                      )}
                    </div>
                  </button>
                  
                  {isLoading && (
                    <button
                      type="button"
                      onClick={stop}
                      className="px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-colors"
                    >
                      <StopCircle className="h-4 w-4" />
                    </button>
                  )}
                  
                  <button 
                    type="button"
                    className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl transition-colors"
                    disabled={isLoading}
                  >
                    <Upload className="h-4 w-4" />
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Output Panel */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Output</h2>
                  <div className="flex items-center space-x-2">
                    {hasResponse && (
                      <>
                        <button
                          onClick={copyToClipboard}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Copy to clipboard"
                        >
                          {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                        </button>
                        <button
                          onClick={downloadContent}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Download"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => reload()}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Regenerate"
                          disabled={isLoading}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <div className="min-h-64 bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
                  {isLoading && (
                    <div className="flex items-center space-x-2 text-purple-600 dark:text-purple-400 mb-4">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                      <span className="text-sm">Cerebras is generating at ultra-speed...</span>
                    </div>
                  )}
                  
                  {messages.length > 0 ? (
                    <div className="space-y-4">
                      {messages.map((message, index) => (
                        <div key={index} className={`p-3 rounded-lg ${
                          message.role === 'user' 
                            ? 'bg-blue-50 dark:bg-blue-900/20 ml-8' 
                            : 'bg-purple-50 dark:bg-purple-900/20 mr-8'
                        }`}>
                          <div className="prose dark:prose-invert max-w-none">
                            <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800 dark:text-gray-200">
                              {message.content}
                              {isLoading && index === messages.length - 1 && (
                                <span className="inline-block w-2 h-5 bg-purple-500 animate-pulse ml-1"></span>
                              )}
                            </pre>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                      <Sparkles className="h-12 w-12 mb-4 opacity-50" />
                      <p className="text-center">Your AI-generated content will appear here</p>
                      <p className="text-sm text-center mt-2">Experience lightning-fast inference with Cerebras</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Stats Panel */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-6">
              <h3 className="font-bold text-gray-900 dark:text-white mb-4">Performance Metrics</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">1,800</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Tokens/Second</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">128K</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Context Length</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-gray-200 dark:border-gray-800 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Powered by Cerebras Inference - The world's fastest AI
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a href="https://www.cerebras.ai" className="text-sm text-gray-600 dark:text-gray-400 hover:text-purple-500 transition-colors">
                Learn more about Cerebras
              </a>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CerebrasStudio;