@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

/* CSS Variables for theming */
:root {
  --background: 255 255 255;
  --foreground: 15 23 42;
  --card: 255 255 255;
  --card-foreground: 15 23 42;
  --popover: 255 255 255;
  --popover-foreground: 15 23 42;
  --primary: 147 51 234;
  --primary-foreground: 248 250 252;
  --secondary: 241 245 249;
  --secondary-foreground: 15 23 42;
  --muted: 241 245 249;
  --muted-foreground: 100 116 139;
  --accent: 241 245 249;
  --accent-foreground: 15 23 42;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 147 51 234;
  --radius: 0.5rem;
  
  /* Toast variables */
  --toast-bg: rgb(255 255 255);
  --toast-color: rgb(15 23 42);
  --toast-border: rgb(226 232 240);
}

.dark {
  --background: 15 23 42;
  --foreground: 248 250 252;
  --card: 30 41 59;
  --card-foreground: 248 250 252;
  --popover: 30 41 59;
  --popover-foreground: 248 250 252;
  --primary: 147 51 234;
  --primary-foreground: 248 250 252;
  --secondary: 51 65 85;
  --secondary-foreground: 248 250 252;
  --muted: 51 65 85;
  --muted-foreground: 148 163 184;
  --accent: 51 65 85;
  --accent-foreground: 248 250 252;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --border: 51 65 85;
  --input: 51 65 85;
  --ring: 147 51 234;
  
  /* Toast variables for dark mode */
  --toast-bg: rgb(30 41 59);
  --toast-color: rgb(248 250 252);
  --toast-border: rgb(51 65 85);
}

/* Base styles */
* {
  border-color: rgb(var(--border));
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--muted-foreground) / 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--muted-foreground) / 0.7);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--muted-foreground) / 0.5) rgb(var(--muted));
}

/* Selection styles */
::selection {
  background-color: rgb(var(--primary) / 0.2);
  color: rgb(var(--foreground));
}

::-moz-selection {
  background-color: rgb(var(--primary) / 0.2);
  color: rgb(var(--foreground));
}

/* Focus styles */
:focus-visible {
  outline: 2px solid rgb(var(--ring));
  outline-offset: 2px;
}

/* Custom component styles */
.gradient-text {
  background: linear-gradient(135deg, rgb(147 51 234), rgb(236 72 153));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation utilities */
.animate-gradient {
  background-size: 400% 400%;
  animation: gradient 8s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Code syntax highlighting */
.prose pre {
  background-color: rgb(var(--muted)) !important;
  border: 1px solid rgb(var(--border)) !important;
  border-radius: 0.75rem !important;
}

.prose code {
  background-color: rgb(var(--muted)) !important;
  color: rgb(var(--foreground)) !important;
  padding: 0.25rem 0.375rem !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
}

.prose pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Loading spinner */
.spinner {
  border: 2px solid rgb(var(--muted));
  border-top: 2px solid rgb(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating animation */
.float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Glow effect */
.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgb(var(--primary) / 0.2), 0 0 20px rgb(var(--primary) / 0.2), 0 0 30px rgb(var(--primary) / 0.2);
  }
  to {
    box-shadow: 0 0 20px rgb(var(--primary) / 0.4), 0 0 30px rgb(var(--primary) / 0.4), 0 0 40px rgb(var(--primary) / 0.4);
  }
}

/* Shimmer effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
  content: '';
}

.dark .shimmer::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Typography improvements */
.text-balance {
  text-wrap: balance;
}

/* Interactive elements */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .interactive-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, rgb(147 51 234), rgb(236 72 153));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(147, 51, 234, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    color: black !important;
    background: white !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0 0;
    --ring: 0 0 0;
  }
  
  .dark {
    --border: 255 255 255;
    --ring: 255 255 255;
  }
}