# 📁 Project Structure

Complete file organization for **Cerebras Studio** - production-ready AI-powered creative platform.

## 🗂️ Root Directory

```
cerebras-studio/
├── 📄 README.md                    # Project documentation
├── 📦 package.json                 # Dependencies & scripts
├── ⚙️ next.config.js               # Next.js configuration
├── 🎨 tailwind.config.js           # Tailwind CSS configuration
├── 📝 tsconfig.json                # TypeScript configuration
├── 🔧 .env.example                 # Environment variables template
├── 🚫 .gitignore                   # Git ignore rules
├── ✅ .eslintrc.json               # ESLint configuration
├── 💄 prettier.config.js           # Prettier configuration
├── 🏗️ postcss.config.js            # PostCSS configuration
├── 🚀 vercel.json                  # Vercel deployment config
├── 📋 DEPLOYMENT.md                # Deployment guide
├── 📁 PROJECT_STRUCTURE.md         # This file
├── 📄 LICENSE                      # MIT License
└── 🔐 .env.local                   # Local environment (git ignored)
```

## 📱 App Directory (Next.js 15 App Router)

```
app/
├── 🌐 globals.css                  # Global styles & CSS variables
├── 🏗️ layout.tsx                   # Root layout component
├── 🏠 page.tsx                     # Homepage component
├── 🔌 api/                         # API routes
│   ├── 🤖 generate/
│   │   └── route.ts                # Main AI generation endpoint
│   ├── 📋 models/
│   │   └── route.ts                # Available models endpoint
│   └── ⚡ health/
│       └── route.ts                # Health check endpoint
└── 🎯 providers/
    └── theme-provider.tsx          # Theme context provider
```

## 🧩 Components Directory

```
components/
├── 🎨 CerebrasStudio.tsx          # Main application component
├── 🎛️ ui/                         # Reusable UI components
│   ├── 🔘 Button.tsx              # Custom button component
│   ├── 📝 Input.tsx               # Input field component
│   ├── 📦 Card.tsx                # Card container component
│   ├── 🔄 Loading.tsx             # Loading spinner component
│   └── 🚨 Toast.tsx               # Notification component
├── ⚡ features/                    # Feature-specific components
│   ├── 🤖 AIChat.tsx              # Chat interface component
│   ├── ⚙️ ModelSelector.tsx       # Model selection component
│   ├── 📊 PerformanceStats.tsx    # Performance metrics display
│   └── 🎨 ThemeToggle.tsx         # Dark/light mode toggle
└── 🏗️ layout/                     # Layout components
    ├── 🎯 Header.tsx              # Application header
    ├── 🦶 Footer.tsx              # Application footer
    ├── 📱 Sidebar.tsx             # Navigation sidebar
    └── 🔧 SettingsPanel.tsx       # Settings configuration panel
```

## 🛠️ Utilities & Libraries

```
lib/
├── 🔧 utils.ts                    # Common utility functions
├── 🗄️ api.ts                      # API helper functions
├── 🎨 styles.ts                   # Style utility functions
├── 🔐 auth.ts                     # Authentication utilities
├── 📊 analytics.ts                # Analytics tracking
├── 🚀 performance.ts              # Performance monitoring
└── 🔍 validation.ts               # Input validation schemas
```

## 🎣 Custom Hooks

```
hooks/
├── 🤖 useChat.ts                  # Chat functionality hook
├── 🎨 useTheme.ts                 # Theme management hook
├── 📱 useLocalStorage.ts          # Local storage hook
├── 📊 usePerformance.ts           # Performance monitoring hook
├── 🔄 useAsync.ts                 # Async operations hook
└── 📋 useClipboard.ts             # Clipboard operations hook
```

## 📝 Type Definitions

```
types/
├── 🤖 ai.ts                       # AI/ML related types
├── 🎨 ui.ts                       # UI component types
├── 🔌 api.ts                      # API request/response types
├── 👤 user.ts                     # User-related types
└── 🌐 global.ts                   # Global type definitions
```

## 🎨 Styles Directory

```
styles/
├── 🧩 components.css              # Component-specific styles
├── 🎛️ utilities.css               # Custom utility classes
├── 📱 responsive.css              # Responsive design styles
└── 🎨 animations.css              # Custom animations & transitions
```

## 📁 Public Assets

```
public/
├── 🖼️ images/                     # Static images
│   ├── 🏠 hero-bg.webp            # Hero section background
│   ├── 🎯 logo.svg                # Application logo
│   ├── 📊 og-image.png            # Open Graph image
│   └── 📱 screenshot.png          # Application screenshot
├── 🎯 icons/                      # PWA & favicon icons
│   ├── 📱 icon-192x192.png        # PWA icon 192px
│   ├── 📱 icon-512x512.png        # PWA icon 512px
│   ├── 🔗 favicon.ico             # Browser favicon
│   ├── 🍎 apple-touch-icon.png    # Apple touch icon
│   └── 📱 manifest-icon.png       # Manifest icon
├── 📱 manifest.json               # PWA manifest file
├── 🤖 robots.txt                  # Search engine rules
└── 🗺️ sitemap.xml                 # Site structure map
```

## 🧪 Testing Directory

```
__tests__/                         # Test files
├── 🧩 components/                 # Component tests
│   ├── CerebrasStudio.test.tsx    # Main component tests
│   └── ui/                        # UI component tests
├── 🔌 api/                        # API endpoint tests
│   ├── generate.test.ts           # Generation API tests
│   └── models.test.ts             # Models API tests
├── 🛠️ utils/                      # Utility function tests
├── 🎣 hooks/                      # Custom hooks tests
└── 🌐 e2e/                        # End-to-end tests
    ├── chat.spec.ts               # Chat functionality tests
    ├── generation.spec.ts         # Content generation tests
    └── navigation.spec.ts         # Navigation tests
```

## 📖 Documentation

```
docs/
├── 🚀 getting-started.md          # Quick start guide
├── 🔌 api-reference.md            # API documentation
├── 🎨 design-system.md            # Design guidelines
├── 🛠️ development.md              # Development guide
├── 🚀 deployment.md               # Deployment instructions
├── 🧪 testing.md                  # Testing guide
├── 🔧 configuration.md            # Configuration options
├── 🚨 troubleshooting.md          # Common issues & solutions
└── 🤝 contributing.md             # Contribution guidelines
```

## 🔧 Configuration Files

```
config/
├── ⚙️ database.ts                 # Database configuration
├── 🔐 auth.ts                     # Authentication configuration
├── 📊 analytics.ts                # Analytics configuration
├── 🚀 deployment.ts               # Deployment settings
└── 🌐 constants.ts                # Application constants
```

## 🚀 Scripts Directory

```
scripts/
├── 🏗️ build.js                   # Custom build script
├── 🚀 deploy.js                   # Deployment automation
├── 🧪 test-setup.js               # Test environment setup
├── 📊 analyze.js                  # Bundle analysis
└── 🧹 cleanup.js                  # Cleanup utilities
```

## 📦 Package Management

```
package.json                       # Primary dependencies
├── dependencies                   # Production dependencies
│   ├── next ^15.0.0              # React framework
│   ├── react ^19.0.0             # UI library
│   ├── @ai-sdk/cerebras ^0.1.0   # Cerebras AI SDK
│   ├── ai ^3.0.0                 # Vercel AI SDK
│   ├── tailwindcss ^3.4.0        # CSS framework
│   └── lucide-react ^0.263.1     # Icon library
└── devDependencies                # Development dependencies
    ├── typescript ^5.0.0         # Type checking
    ├── eslint ^8.0.0             # Code linting
    ├── prettier ^3.0.0           # Code formatting
    ├── @types/react ^18.0.0      # React type definitions
    └── @playwright/test ^1.40.0   # E2E testing
```

## 🌍 Environment Configuration

```
.env.example                       # Environment template
├── CEREBRAS_API_KEY              # Required: Cerebras API key
├── NEXT_PUBLIC_APP_URL           # Public: Application URL
├── NODE_ENV                      # Environment mode
├── NEXT_PUBLIC_ANALYTICS_ID      # Optional: Analytics tracking
└── DATABASE_URL                  # Optional: Database connection
```

## 🔄 Git Configuration

```
.gitignore                        # Git ignore patterns
├── node_modules/                 # Dependencies
├── .next/                        # Build output
├── .env.local                    # Local environment
├── coverage/                     # Test coverage
└── *.log                         # Log files
```

## 🏗️ Build Output

```
.next/                            # Next.js build output (git ignored)
├── static/                       # Static assets
├── server/                       # Server-side code
├── cache/                        # Build cache
└── standalone/                   # Standalone output
```

## 📊 Analytics & Monitoring

```
monitoring/
├── 📈 performance.ts             # Performance metrics
├── 🚨 error-tracking.ts          # Error monitoring
├── 📊 usage-analytics.ts         # Usage statistics
└── 🔍 logging.ts                 # Application logging
```

## 🔒 Security

```
security/
├── 🛡️ csp.ts                     # Content Security Policy
├── 🔐 rate-limiting.ts           # API rate limiting
├── 🚨 validation.ts              # Input validation
└── 🔒 headers.ts                 # Security headers
```

---

## 🎯 Key Features by Directory

### **🚀 Performance Optimized**
- **Edge deployment** with Vercel configuration
- **Bundle optimization** with Next.js 15
- **Image optimization** with WebP/AVIF
- **Static generation** for maximum speed

### **🎨 Design Excellence**
- **Tailwind CSS** with custom design system
- **Dark/Light themes** with smooth transitions
- **Responsive design** for all devices
- **Accessibility-first** approach

### **🤖 AI Integration**
- **Cerebras API** integration with ultra-fast inference
- **Multiple models** support (Llama 4, Llama 3.3, Llama 3.1)
- **Streaming responses** with real-time updates
- **Error handling** and fallbacks

### **🔧 Developer Experience**
- **TypeScript** for type safety
- **ESLint + Prettier** for code quality
- **Hot reload** for fast development
- **Comprehensive testing** setup

### **📦 Production Ready**
- **Environment configuration** for all platforms
- **Docker support** for containerization
- **CI/CD ready** with GitHub Actions
- **Monitoring & analytics** integration

---

This structure ensures a **maintainable**, **scalable**, and **production-ready** application that follows modern best practices for Next.js development.