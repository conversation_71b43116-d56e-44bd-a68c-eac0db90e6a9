{"name": "cerebras-studio", "version": "1.0.0", "description": "AI-powered creative platform using Cerebras ultra-fast inference", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "@ai-sdk/cerebras": "^0.1.0", "ai": "^3.0.0", "lucide-react": "^0.263.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@tailwindcss/typography": "^0.5.10", "framer-motion": "^10.16.0", "react-hot-toast": "^2.4.1", "zustand": "^4.4.0", "prism-react-renderer": "^2.3.0", "react-markdown": "^9.0.0", "remark-gfm": "^4.0.0", "canvas-confetti": "^1.9.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.4.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "keywords": ["ai", "cerebras", "nextjs", "react", "typescript", "tailwindcss", "creative", "platform", "inference"], "author": "Cerebras Studio Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/cerebras-studio"}, "engines": {"node": ">=18.0.0"}}