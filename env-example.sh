# Cerebras API Configuration
CEREBRAS_API_KEY=your_cerebras_api_key_here

# Application Configuration
NEXT_PUBLIC_APP_NAME="Cerebras Studio"
NEXT_PUBLIC_APP_DESCRIPTION="AI-Powered Creative Platform"
NEXT_PUBLIC_APP_URL="https://cerebras-studio.vercel.app"

# Environment
NODE_ENV=development
NEXT_PUBLIC_ENV=development

# Analytics (Optional)
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id_here

# Monitoring (Optional)
SENTRY_DSN=your_sentry_dsn_here
NEXT_PUBLIC_SENTRY_DSN=your_public_sentry_dsn_here

# Database (Optional - for future features)
DATABASE_URL=your_database_url_here

# Authentication (Optional - for future features)
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=https://cerebras-studio.vercel.app

# Rate Limiting (Optional)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url_here
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here

# Feature Flags (Optional)
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_MONITORING=true
NEXT_PUBLIC_ENABLE_PWA=true

# Performance Monitoring
NEXT_PUBLIC_PERFORMANCE_MONITORING=true

# Content Security Policy
NEXT_PUBLIC_CSP_ENABLED=true

# API Configuration
API_RATE_LIMIT=100
API_WINDOW_MS=900000

# Deployment Configuration
VERCEL_ENV=development
VERCEL_URL=cerebras-studio.vercel.app