import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  FileText, 
  PenTool, 
  Settings, 
  Download, 
  Upload, 
  Zap, 
  Brain, 
  Palette,
  MessageSquare,
  Play,
  Copy,
  Check,
  Moon,
  Sun,
  Github,
  Twitter,
  Linkedin,
  ChevronRight,
  Layers,
  BarChart3,
  Wand2
} from 'lucide-react';

const CerebrasStudio = () => {
  const [activeTab, setActiveTab] = useState('text');
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [response, setResponse] = useState('');
  const [selectedModel, setSelectedModel] = useState('llama-4-scout-17b-16e-instruct');
  const [copied, setCopied] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [streamingText, setStreamingText] = useState('');
  const [characterCount, setCharacterCount] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const textareaRef = useRef(null);

  const models = [
    { id: 'llama-4-scout-17b-16e-instruct', name: 'Llama 4 Scout 17B', description: 'Latest reasoning model' },
    { id: 'llama-3.3-70b', name: 'Llama 3.3 70B', description: 'Most powerful model' },
    { id: 'llama3.1-8b', name: 'Llama 3.1 8B', description: 'Fast and efficient' },
  ];

  const tabs = [
    { id: 'text', label: 'Text Studio', icon: PenTool, description: 'Creative writing & content generation' },
    { id: 'code', label: 'Code Generator', icon: Code, description: 'Programming assistance & code generation' },
    { id: 'document', label: 'Document AI', icon: FileText, description: 'Document analysis & summarization' },
    { id: 'creative', label: 'Creative Writer', icon: Palette, description: 'Story & character development' },
  ];

  const prompts = {
    text: [
      'Write a compelling product description for a new AI-powered productivity app',
      'Create a professional email template for client outreach',
      'Generate a blog post about the future of artificial intelligence',
      'Write a social media campaign for a sustainable fashion brand'
    ],
    code: [
      'Create a React component for a modern dashboard',
      'Write a Python script to analyze CSV data',
      'Build a responsive navigation bar with Tailwind CSS',
      'Generate a REST API endpoint with error handling'
    ],
    document: [
      'Analyze this document and provide key insights',
      'Summarize the main points in bullet format',
      'Extract action items and deadlines',
      'Create an executive summary'
    ],
    creative: [
      'Create a character profile for a sci-fi protagonist',
      'Write the opening scene of a mystery novel',
      'Develop a plot outline for a romantic comedy',
      'Create dialogue between two opposing characters'
    ]
  };

  useEffect(() => {
    setCharacterCount(prompt.length);
    setWordCount(prompt.trim() ? prompt.trim().split(/\s+/).length : 0);
  }, [prompt]);

  const simulateStreaming = (text) => {
    setStreamingText('');
    setIsGenerating(true);
    const words = text.split(' ');
    let currentIndex = 0;

    const interval = setInterval(() => {
      if (currentIndex < words.length) {
        setStreamingText(prev => prev + (currentIndex === 0 ? '' : ' ') + words[currentIndex]);
        currentIndex++;
      } else {
        clearInterval(interval);
        setIsGenerating(false);
        setResponse(text);
        setStreamingText('');
      }
    }, 50); // Very fast streaming to simulate Cerebras speed
  };

  const generateContent = async () => {
    if (!prompt.trim()) return;
    
    // Simulate Cerebras API call with realistic responses
    const responses = {
      text: `# ${prompt}

Creating compelling content that resonates with your audience requires understanding their needs, pain points, and aspirations. Here's a comprehensive approach:

## Key Elements
- **Authenticity**: Speak in your brand's genuine voice
- **Value**: Provide actionable insights and solutions
- **Engagement**: Use storytelling and emotional connection
- **Clarity**: Communicate complex ideas simply

## Content Strategy
1. Research your target audience thoroughly
2. Identify their primary challenges and goals
3. Craft messages that address these directly
4. Use data and examples to support your points
5. Include clear calls-to-action

## Best Practices
- Start with a hook that captures attention immediately
- Use short paragraphs and bullet points for readability
- Include relevant keywords naturally
- End with a strong conclusion that reinforces your message

This content framework will help you create materials that not only inform but also inspire action from your audience.`,
      
      code: `// Modern React Dashboard Component
import React, { useState, useEffect } from 'react';
import { BarChart3, Users, TrendingUp, DollarSign } from 'lucide-react';

const Dashboard = () => {
  const [metrics, setMetrics] = useState({
    users: 12540,
    revenue: 45200,
    growth: 23.5,
    orders: 1834
  });

  const MetricCard = ({ title, value, icon: Icon, change }) => (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-500 dark:text-gray-400 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          <p className="text-green-500 text-sm mt-1">+{change}% from last month</p>
        </div>
        <div className="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
          <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Dashboard</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard title="Total Users" value={metrics.users.toLocaleString()} icon={Users} change={12.5} />
          <MetricCard title="Revenue" value={\`$\${metrics.revenue.toLocaleString()}\`} icon={DollarSign} change={8.2} />
          <MetricCard title="Growth Rate" value={\`\${metrics.growth}%\`} icon={TrendingUp} change={15.3} />
          <MetricCard title="Orders" value={metrics.orders.toLocaleString()} icon={BarChart3} change={22.1} />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;`,
      
      document: `# Document Analysis Summary

## Key Insights
- **Primary Focus**: Innovation in AI-driven solutions
- **Target Market**: Enterprise customers seeking automation
- **Timeline**: 18-month implementation roadmap
- **Budget**: $2.5M allocated for development

## Action Items
1. **Immediate (Week 1-2)**
   - Finalize technical specifications
   - Assemble development team
   - Set up project management infrastructure

2. **Short-term (Month 1-3)**
   - Complete market research validation
   - Develop MVP prototype
   - Conduct initial user testing

3. **Medium-term (Month 4-12)**
   - Full product development
   - Beta testing with select customers
   - Iterative improvements based on feedback

## Risk Assessment
- **Technical**: Integration complexity with legacy systems
- **Market**: Competitive landscape evolution
- **Resource**: Potential talent acquisition challenges

## Recommendations
- Prioritize modular architecture for scalability
- Establish strong feedback loops with early adopters
- Consider strategic partnerships for market entry`,
      
      creative: `# Character Profile: Dr. Elena Vasquez

## Basic Information
- **Age**: 34
- **Occupation**: Quantum Computing Researcher
- **Location**: Neo-Francisco, 2087

## Personality
Elena is brilliant but haunted by the weight of her discoveries. She speaks in measured tones, always calculating probabilities in her head. Her dry humor often catches people off guard, and she has a habit of explaining complex concepts through simple analogies.

## Background
Born in Old Mexico City before the Great Migration, Elena witnessed the collapse of traditional computing systems firsthand. This experience drives her obsession with creating quantum networks that can't be corrupted or controlled by any single entity.

## Physical Description
- Medium height with calloused hands from years of laboratory work
- Dark hair streaked with premature silver from radiation exposure
- Wears vintage glasses—a family heirloom in a world of neural implants
- Always carries a worn leather notebook filled with hand-drawn diagrams

## Core Conflict
Elena must choose between sharing her breakthrough quantum algorithm—which could revolutionize communication—or keeping it secret to prevent it from being weaponized by competing factions.

## Character Arc
From isolated researcher → reluctant hero → leader of a new technological revolution

## Signature Dialogue
"The universe doesn't care about our timelines, but it always respects elegant solutions."

## Relationships
- **Dr. Marcus Chen**: Research partner and closest friend, represents the path of caution
- **Zara Al-Rashid**: Corporate spy who becomes an unexpected ally
- **Professor Sarah Okafor**: Mentor figure who taught Elena to question everything, even science`
    };

    simulateStreaming(responses[activeTab]);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(response);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadContent = () => {
    const element = document.createElement('a');
    const file = new Blob([response], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `cerebras-studio-${activeTab}-${Date.now()}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  return (
    <div className={`min-h-screen transition-all duration-500 ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Cerebras Studio
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">AI-Powered Creative Platform</p>
                </div>
              </div>
              <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-green-50 dark:bg-green-900/20 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-700 dark:text-green-400 text-sm font-medium">Ultra-Fast Inference</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </button>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <Settings className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-2">
                <a href="https://github.com" className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <Github className="h-5 w-5" />
                </a>
                <a href="https://twitter.com" className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <Twitter className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="absolute right-0 top-0 h-full w-96 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-800 shadow-2xl">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">Settings</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Cerebras API Key</label>
                  <input
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your Cerebras API key"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-800"
                  />
                  <p className="text-xs text-gray-500 mt-1">Get your free API key from cerebras.ai</p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Model Selection</label>
                  <select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-800"
                  >
                    {models.map(model => (
                      <option key={model.id} value={model.id}>
                        {model.name} - {model.description}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                  <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">Performance Stats</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Speed:</span>
                      <span className="font-medium">1,800 tokens/sec</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Context:</span>
                      <span className="font-medium">128K tokens</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accuracy:</span>
                      <span className="font-medium">16-bit precision</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-4 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group relative px-6 py-4 rounded-2xl transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25'
                    : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`h-5 w-5 ${activeTab === tab.id ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`} />
                  <div className="text-left">
                    <div className={`font-medium ${activeTab === tab.id ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {tab.label}
                    </div>
                    <div className={`text-xs ${activeTab === tab.id ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400'}`}>
                      {tab.description}
                    </div>
                  </div>
                </div>
                {activeTab === tab.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-20 animate-pulse"></div>
                )}
              </button>
            );
          })}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Input</h2>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{characterCount} chars</span>
                    <span className="text-gray-300">•</span>
                    <span className="text-sm text-gray-500">{wordCount} words</span>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <textarea
                  ref={textareaRef}
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder={`What would you like to ${activeTab === 'code' ? 'build' : 'create'} today?`}
                  className="w-full h-64 p-4 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none bg-gray-50 dark:bg-gray-900 transition-all duration-200"
                />
                
                {/* Prompt Suggestions */}
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Prompts:</p>
                  <div className="flex flex-wrap gap-2">
                    {prompts[activeTab].slice(0, 2).map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => setPrompt(suggestion)}
                        className="text-xs px-3 py-1 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-full hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors"
                      >
                        {suggestion.length > 40 ? suggestion.slice(0, 40) + '...' : suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={generateContent}
                disabled={!prompt.trim() || isGenerating}
                className="flex-1 group relative px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg shadow-purple-500/25"
              >
                <div className="flex items-center justify-center space-x-2">
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Generating...</span>
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4" />
                      <span>Generate with Cerebras</span>
                    </>
                  )}
                </div>
                {!isGenerating && (
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                )}
              </button>
              
              <button className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl transition-colors">
                <Upload className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Output Panel */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Output</h2>
                  <div className="flex items-center space-x-2">
                    {response && (
                      <>
                        <button
                          onClick={copyToClipboard}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Copy to clipboard"
                        >
                          {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                        </button>
                        <button
                          onClick={downloadContent}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Download"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <div className="min-h-64 bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
                  {isGenerating && streamingText && (
                    <div className="prose dark:prose-invert max-w-none">
                      <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800 dark:text-gray-200">
                        {streamingText}
                        <span className="inline-block w-2 h-5 bg-purple-500 animate-pulse ml-1"></span>
                      </pre>
                    </div>
                  )}
                  
                  {!isGenerating && response && (
                    <div className="prose dark:prose-invert max-w-none">
                      <pre className="whitespace-pre-wrap font-mono text-sm text-gray-800 dark:text-gray-200">
                        {response}
                      </pre>
                    </div>
                  )}
                  
                  {!isGenerating && !response && (
                    <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                      <Sparkles className="h-12 w-12 mb-4 opacity-50" />
                      <p className="text-center">Your AI-generated content will appear here</p>
                      <p className="text-sm text-center mt-2">Experience lightning-fast inference with Cerebras</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Stats Panel */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-6">
              <h3 className="font-bold text-gray-900 dark:text-white mb-4">Performance Metrics</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">1,800</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Tokens/Second</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">128K</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Context Length</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-gray-200 dark:border-gray-800 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Powered by Cerebras Inference - The world's fastest AI
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a href="https://www.cerebras.ai" className="text-sm text-gray-600 dark:text-gray-400 hover:text-purple-500 transition-colors">
                Learn more about Cerebras
              </a>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CerebrasStudio;