{"version": 3, "sources": ["../src/index.ts", "../src/use-assistant.ts", "../src/use-chat.ts", "../src/throttle.ts", "../src/use-completion.ts", "../src/use-object.ts"], "sourcesContent": ["export * from './use-assistant';\nexport * from './use-chat';\nexport * from './use-completion';\nexport * from './use-object';\n", "import { isAbortError } from '@ai-sdk/provider-utils';\nimport {\n  AssistantStatus,\n  CreateMessage,\n  Message,\n  UseAssistantOptions,\n  generateId,\n  readDataStream,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useRef, useState } from 'react';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport type UseAssistantHelpers = {\n  /**\n   * The current array of chat messages.\n   */\n  messages: Message[];\n\n  /**\n   * Update the message store with a new array of messages.\n   */\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n\n  /**\n   * The current thread ID.\n   */\n  threadId: string | undefined;\n\n  /**\n   * Set the current thread ID. Specifying a thread ID will switch to that thread, if it exists. If set to 'undefined', a new thread will be created. For both cases, `threadId` will be updated with the new value and `messages` will be cleared.\n   */\n  setThreadId: (threadId: string | undefined) => void;\n\n  /**\n   * The current value of the input field.\n   */\n  input: string;\n\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param requestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => Promise<void>;\n\n  /**\nAbort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n\n  /**\n   * setState-powered method to update the input value.\n   */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n\n  /**\n   * Handler for the `onChange` event of the input field to control the input's value.\n   */\n  handleInputChange: (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n\n  /**\n   * Form submission handler that automatically resets the input field and appends a user message.\n   */\n  submitMessage: (\n    event?: React.FormEvent<HTMLFormElement>,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => Promise<void>;\n\n  /**\n   * The current status of the assistant. This can be used to show a loading indicator.\n   */\n  status: AssistantStatus;\n\n  /**\n   * The error thrown during the assistant message processing, if any.\n   */\n  error: undefined | Error;\n};\n\nexport function useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch,\n}: UseAssistantOptions): UseAssistantHelpers {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [currentThreadId, setCurrentThreadId] = useState<string | undefined>(\n    undefined,\n  );\n  const [status, setStatus] = useState<AssistantStatus>('awaiting_message');\n  const [error, setError] = useState<undefined | Error>(undefined);\n\n  const handleInputChange = (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => {\n    setInput(event.target.value);\n  };\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const stop = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const append = async (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    setStatus('in_progress');\n\n    setMessages(messages => [\n      ...messages,\n      {\n        ...message,\n        id: message.id ?? generateId(),\n      },\n    ]);\n\n    setInput('');\n\n    const abortController = new AbortController();\n\n    try {\n      abortControllerRef.current = abortController;\n\n      const actualFetch = fetch ?? getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: 'POST',\n        credentials,\n        signal: abortController.signal,\n        headers: { 'Content-Type': 'application/json', ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: threadIdParam ?? currentThreadId ?? null,\n          message: message.content,\n\n          // optional request data:\n          data: requestOptions?.data,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          (await response.text()) ?? 'Failed to fetch the assistant response.',\n        );\n      }\n\n      if (response.body == null) {\n        throw new Error('The response body is empty.');\n      }\n\n      for await (const { type, value } of readDataStream(\n        response.body.getReader(),\n      )) {\n        switch (type) {\n          case 'assistant_message': {\n            setMessages(messages => [\n              ...messages,\n              {\n                id: value.id,\n                role: value.role,\n                content: value.content[0].text.value,\n              },\n            ]);\n            break;\n          }\n\n          case 'text': {\n            // text delta - add to last message:\n            setMessages(messages => {\n              const lastMessage = messages[messages.length - 1];\n              return [\n                ...messages.slice(0, messages.length - 1),\n                {\n                  id: lastMessage.id,\n                  role: lastMessage.role,\n                  content: lastMessage.content + value,\n                },\n              ];\n            });\n\n            break;\n          }\n\n          case 'data_message': {\n            setMessages(messages => [\n              ...messages,\n              {\n                id: value.id ?? generateId(),\n                role: 'data',\n                content: '',\n                data: value.data,\n              },\n            ]);\n            break;\n          }\n\n          case 'assistant_control_data': {\n            setCurrentThreadId(value.threadId);\n\n            // set id of last message:\n            setMessages(messages => {\n              const lastMessage = messages[messages.length - 1];\n              lastMessage.id = value.messageId;\n              return [...messages.slice(0, messages.length - 1), lastMessage];\n            });\n\n            break;\n          }\n\n          case 'error': {\n            setError(new Error(value));\n            break;\n          }\n        }\n      }\n    } catch (error) {\n      // Ignore abort errors as they are expected when the user cancels the request:\n      if (isAbortError(error) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n\n      if (onError && error instanceof Error) {\n        onError(error);\n      }\n\n      setError(error as Error);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus('awaiting_message');\n    }\n  };\n\n  const submitMessage = async (\n    event?: React.FormEvent<HTMLFormElement>,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    event?.preventDefault?.();\n\n    if (input === '') {\n      return;\n    }\n\n    append({ role: 'user', content: input }, requestOptions);\n  };\n\n  const setThreadId = (threadId: string | undefined) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop,\n  };\n}\n\n/**\n@deprecated Use `useAssistant` instead.\n */\nexport const experimental_useAssistant = useAssistant;\n", "import { FetchFunction } from '@ai-sdk/provider-utils';\nimport type {\n  Attachment,\n  ChatRequest,\n  ChatRequestOptions,\n  CreateMessage,\n  IdGenerator,\n  JSONValue,\n  Message,\n  UseChatOptions,\n} from '@ai-sdk/ui-utils';\nimport {\n  callChatApi,\n  generateId as generateIdFunc,\n  processChatStream,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useEffect, useId, useRef, useState } from 'react';\nimport useSWR, { KeyedMutator } from 'swr';\nimport { throttle } from './throttle';\n\nexport type { CreateMessage, Message, UseChatOptions };\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: Message[];\n  /** The error object of the API request */\n  error: undefined | Error;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param options Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (\n    messages: Message[] | ((messages: Message[]) => Message[]),\n  ) => void;\n  /** The current value of the input */\n  input: string;\n  /** setState-powered method to update the input value */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n  /** An input/textarea-ready onChange handler to control the value of the input */\n  handleInputChange: (\n    e:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n  /** Form submission handler to automatically reset input and append a user message */\n  handleSubmit: (\n    event?: { preventDefault?: () => void },\n    chatRequestOptions?: ChatRequestOptions,\n  ) => void;\n  metadata?: Object;\n  /** Whether the API request is in progress */\n  isLoading: boolean;\n\n  /** Additional data added on the server via StreamData. */\n  data?: JSONValue[];\n  /** Set the data of the chat. You can use this to transform or clear the chat data. */\n  setData: (\n    data:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => void;\n};\n\nconst getStreamedResponse = async (\n  api: string,\n  chatRequest: ChatRequest,\n  mutate: KeyedMutator<Message[]>,\n  mutateStreamData: KeyedMutator<JSONValue[] | undefined>,\n  existingDataRef: React.MutableRefObject<JSONValue[] | undefined>,\n  extraMetadataRef: React.MutableRefObject<any>,\n  messagesRef: React.MutableRefObject<Message[]>,\n  abortControllerRef: React.MutableRefObject<AbortController | null>,\n  generateId: IdGenerator,\n  streamProtocol: UseChatOptions['streamProtocol'],\n  onFinish: UseChatOptions['onFinish'],\n  onResponse: ((response: Response) => void | Promise<void>) | undefined,\n  onToolCall: UseChatOptions['onToolCall'] | undefined,\n  sendExtraMessageFields: boolean | undefined,\n  experimental_prepareRequestBody:\n    | ((options: {\n        messages: Message[];\n        requestData?: JSONValue;\n        requestBody?: object;\n      }) => JSONValue)\n    | undefined,\n  fetch: FetchFunction | undefined,\n  keepLastMessageOnError: boolean,\n) => {\n  // Do an optimistic update to the chat state to show the updated messages immediately:\n  const previousMessages = messagesRef.current;\n  mutate(chatRequest.messages, false);\n\n  const constructedMessagesPayload = sendExtraMessageFields\n    ? chatRequest.messages\n    : chatRequest.messages.map(\n        ({\n          role,\n          content,\n          experimental_attachments,\n          name,\n          data,\n          annotations,\n          toolInvocations,\n          function_call,\n          tool_calls,\n          tool_call_id,\n        }) => ({\n          role,\n          content,\n          ...(experimental_attachments !== undefined && {\n            experimental_attachments,\n          }),\n          ...(name !== undefined && { name }),\n          ...(data !== undefined && { data }),\n          ...(annotations !== undefined && { annotations }),\n          ...(toolInvocations !== undefined && { toolInvocations }),\n          // outdated function/tool call handling (TODO deprecate):\n          tool_call_id,\n          ...(function_call !== undefined && { function_call }),\n          ...(tool_calls !== undefined && { tool_calls }),\n        }),\n      );\n\n  const existingData = existingDataRef.current;\n\n  return await callChatApi({\n    api,\n    body: experimental_prepareRequestBody?.({\n      messages: chatRequest.messages,\n      requestData: chatRequest.data,\n      requestBody: chatRequest.body,\n    }) ?? {\n      messages: constructedMessagesPayload,\n      data: chatRequest.data,\n      ...extraMetadataRef.current.body,\n      ...chatRequest.body,\n      ...(chatRequest.functions !== undefined && {\n        functions: chatRequest.functions,\n      }),\n      ...(chatRequest.function_call !== undefined && {\n        function_call: chatRequest.function_call,\n      }),\n      ...(chatRequest.tools !== undefined && {\n        tools: chatRequest.tools,\n      }),\n      ...(chatRequest.tool_choice !== undefined && {\n        tool_choice: chatRequest.tool_choice,\n      }),\n    },\n    streamProtocol,\n    credentials: extraMetadataRef.current.credentials,\n    headers: {\n      ...extraMetadataRef.current.headers,\n      ...chatRequest.headers,\n    },\n    abortController: () => abortControllerRef.current,\n    restoreMessagesOnFailure() {\n      if (!keepLastMessageOnError) {\n        mutate(previousMessages, false);\n      }\n    },\n    onResponse,\n    onUpdate(merged, data) {\n      mutate([...chatRequest.messages, ...merged], false);\n      mutateStreamData([...(existingData ?? []), ...(data ?? [])], false);\n    },\n    onToolCall,\n    onFinish,\n    generateId,\n    fetch,\n  });\n};\n\nexport function useChat({\n  api = '/api/chat',\n  id,\n  initialMessages,\n  initialInput = '',\n  sendExtraMessageFields,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  onToolCall,\n  experimental_prepareRequestBody,\n  experimental_maxAutomaticRoundtrips = 0,\n  maxAutomaticRoundtrips = experimental_maxAutomaticRoundtrips,\n  maxToolRoundtrips = maxAutomaticRoundtrips,\n  maxSteps = maxToolRoundtrips != null ? maxToolRoundtrips + 1 : 1,\n  streamMode,\n  streamProtocol,\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId = generateIdFunc,\n  fetch,\n  keepLastMessageOnError = false,\n  experimental_throttle: throttleWaitMs,\n}: UseChatOptions & {\n  key?: string;\n\n  /**\n@deprecated Use `maxToolRoundtrips` instead.\n   */\n  experimental_maxAutomaticRoundtrips?: number;\n\n  /**\n@deprecated Use `maxToolRoundtrips` instead.\n   */\n  maxAutomaticRoundtrips?: number;\n\n  /**\n   * Experimental (React only). When a function is provided, it will be used\n   * to prepare the request body for the chat API. This can be useful for\n   * customizing the request body based on the messages and data in the chat.\n   *\n   * @param messages The current messages in the chat.\n   * @param requestData The data object passed in the chat request.\n   * @param requestBody The request body object passed in the chat request.\n   */\n  experimental_prepareRequestBody?: (options: {\n    messages: Message[];\n    requestData?: JSONValue;\n    requestBody?: object;\n  }) => JSONValue;\n\n  /**\nCustom throttle wait in ms for the chat messages and data updates.\nDefault is undefined, which disables throttling.\n   */\n  experimental_throttle?: number;\n\n  /**\nMaximum number of automatic roundtrips for tool calls.\n\nAn automatic tool call roundtrip is a call to the server with the\ntool call results when all tool calls in the last assistant\nmessage have results.\n\nA maximum number is required to prevent infinite loops in the\ncase of misconfigured tools.\n\nBy default, it's set to 0, which will disable the feature.\n\n@deprecated Use `maxSteps` instead (which is `maxToolRoundtrips` + 1).\n     */\n  maxToolRoundtrips?: number;\n\n  /**\nMaximum number of sequential LLM calls (steps), e.g. when you use tool calls. Must be at least 1.\n\nA maximum number is required to prevent infinite loops in the case of misconfigured tools.\n\nBy default, it's set to 1, which means that only a single LLM call is made.\n */\n  maxSteps?: number;\n} = {}): UseChatHelpers & {\n  /**\n   * @deprecated Use `addToolResult` instead.\n   */\n  experimental_addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n  addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n} {\n  // streamMode is deprecated, use streamProtocol instead.\n  if (streamMode) {\n    streamProtocol ??= streamMode === 'text' ? 'text' : undefined;\n  }\n\n  // Generate a unique id for the chat if not provided.\n  const hookId = useId();\n  const idKey = id ?? hookId;\n  const chatKey = typeof api === 'string' ? [api, idKey] : idKey;\n\n  // Store a empty array as the initial messages\n  // (instead of using a default parameter value that gets re-created each time)\n  // to avoid re-renders:\n  const [initialMessagesFallback] = useState([]);\n\n  // Store the chat state in SWR, using the chatId as the key to share states.\n  const { data: messages, mutate } = useSWR<Message[]>(\n    [chatKey, 'messages'],\n    null,\n    { fallbackData: initialMessages ?? initialMessagesFallback },\n  );\n\n  // Keep the latest messages in a ref.\n  const messagesRef = useRef<Message[]>(messages || []);\n  useEffect(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n\n  // stream data\n  const { data: streamData, mutate: mutateStreamData } = useSWR<\n    JSONValue[] | undefined\n  >([chatKey, 'streamData'], null);\n\n  // keep the latest stream data in a ref\n  const streamDataRef = useRef<JSONValue[] | undefined>(streamData);\n  useEffect(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n\n  // We store loading state in another hook to sync loading states across hook invocations\n  const { data: isLoading = false, mutate: mutateLoading } = useSWR<boolean>(\n    [chatKey, 'loading'],\n    null,\n  );\n\n  const { data: error = undefined, mutate: setError } = useSWR<\n    undefined | Error\n  >([chatKey, 'error'], null);\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const extraMetadataRef = useRef({\n    credentials,\n    headers,\n    body,\n  });\n\n  useEffect(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body,\n    };\n  }, [credentials, headers, body]);\n\n  const triggerRequest = useCallback(\n    async (chatRequest: ChatRequest) => {\n      const messageCount = messagesRef.current.length;\n\n      try {\n        mutateLoading(true);\n        setError(undefined);\n\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n\n        await processChatStream({\n          getStreamedResponse: () =>\n            getStreamedResponse(\n              api,\n              chatRequest,\n              // throttle streamed ui updates:\n              throttle(mutate, throttleWaitMs),\n              throttle(mutateStreamData, throttleWaitMs),\n              streamDataRef,\n              extraMetadataRef,\n              messagesRef,\n              abortControllerRef,\n              generateId,\n              streamProtocol,\n              onFinish,\n              onResponse,\n              onToolCall,\n              sendExtraMessageFields,\n              experimental_prepareRequestBody,\n              fetch,\n              keepLastMessageOnError,\n            ),\n          experimental_onFunctionCall,\n          experimental_onToolCall,\n          updateChatRequest: chatRequestParam => {\n            chatRequest = chatRequestParam;\n          },\n          getCurrentMessages: () => messagesRef.current,\n        });\n\n        abortControllerRef.current = null;\n      } catch (err) {\n        // Ignore abort errors as they are expected.\n        if ((err as any).name === 'AbortError') {\n          abortControllerRef.current = null;\n          return null;\n        }\n\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n\n        setError(err as Error);\n      } finally {\n        mutateLoading(false);\n      }\n\n      // auto-submit when all tool calls in the last assistant message have results:\n      const messages = messagesRef.current;\n      const lastMessage = messages[messages.length - 1];\n      if (\n        // ensure we actually have new messages (to prevent infinite loops in case of errors):\n        messages.length > messageCount &&\n        // ensure there is a last message:\n        lastMessage != null &&\n        // check if the feature is enabled:\n        maxSteps > 1 &&\n        // check that next step is possible:\n        isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n        // limit the number of automatic steps:\n        countTrailingAssistantMessages(messages) < maxSteps\n      ) {\n        await triggerRequest({ messages });\n      }\n    },\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_onFunctionCall,\n      experimental_onToolCall,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId,\n      fetch,\n      keepLastMessageOnError,\n      throttleWaitMs,\n    ],\n  );\n\n  const append = useCallback(\n    async (\n      message: Message | CreateMessage,\n      {\n        options,\n        functions,\n        function_call,\n        tools,\n        tool_choice,\n        data,\n        headers,\n        body,\n        experimental_attachments,\n      }: ChatRequestOptions = {},\n    ) => {\n      if (!message.id) {\n        message.id = generateId();\n      }\n\n      const attachmentsForRequest = await prepareAttachmentsForRequest(\n        experimental_attachments,\n      );\n\n      const requestOptions = {\n        headers: headers ?? options?.headers,\n        body: body ?? options?.body,\n      };\n\n      const messages = messagesRef.current.concat({\n        ...message,\n        id: message.id ?? generateId(),\n        createdAt: message.createdAt ?? new Date(),\n        experimental_attachments:\n          attachmentsForRequest.length > 0 ? attachmentsForRequest : undefined,\n      });\n\n      const chatRequest: ChatRequest = {\n        messages,\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n        ...(functions !== undefined && { functions }),\n        ...(function_call !== undefined && { function_call }),\n        ...(tools !== undefined && { tools }),\n        ...(tool_choice !== undefined && { tool_choice }),\n      };\n\n      return triggerRequest(chatRequest);\n    },\n    [triggerRequest, generateId],\n  );\n\n  const reload = useCallback(\n    async ({\n      options,\n      functions,\n      function_call,\n      tools,\n      tool_choice,\n      data,\n      headers,\n      body,\n    }: ChatRequestOptions = {}) => {\n      if (messagesRef.current.length === 0) return null;\n\n      const requestOptions = {\n        headers: headers ?? options?.headers,\n        body: body ?? options?.body,\n      };\n\n      // Remove last assistant message and retry last user message.\n      const lastMessage = messagesRef.current[messagesRef.current.length - 1];\n      if (lastMessage.role === 'assistant') {\n        const chatRequest: ChatRequest = {\n          messages: messagesRef.current.slice(0, -1),\n          options: requestOptions,\n          headers: requestOptions.headers,\n          body: requestOptions.body,\n          data,\n          ...(functions !== undefined && { functions }),\n          ...(function_call !== undefined && { function_call }),\n          ...(tools !== undefined && { tools }),\n          ...(tool_choice !== undefined && { tool_choice }),\n        };\n\n        return triggerRequest(chatRequest);\n      }\n\n      const chatRequest: ChatRequest = {\n        messages: messagesRef.current,\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n        ...(functions !== undefined && { functions }),\n        ...(function_call !== undefined && { function_call }),\n        ...(tools !== undefined && { tools }),\n        ...(tool_choice !== undefined && { tool_choice }),\n      };\n\n      return triggerRequest(chatRequest);\n    },\n    [triggerRequest],\n  );\n\n  const stop = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const setMessages = useCallback(\n    (messages: Message[] | ((messages: Message[]) => Message[])) => {\n      if (typeof messages === 'function') {\n        messages = messages(messagesRef.current);\n      }\n\n      mutate(messages, false);\n      messagesRef.current = messages;\n    },\n    [mutate],\n  );\n\n  const setData = useCallback(\n    (\n      data:\n        | JSONValue[]\n        | undefined\n        | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n    ) => {\n      if (typeof data === 'function') {\n        data = data(streamDataRef.current);\n      }\n\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData],\n  );\n\n  // Input state and handlers.\n  const [input, setInput] = useState(initialInput);\n\n  const handleSubmit = useCallback(\n    async (\n      event?: { preventDefault?: () => void },\n      options: ChatRequestOptions = {},\n      metadata?: Object,\n    ) => {\n      event?.preventDefault?.();\n\n      if (!input && !options.allowEmptySubmit) return;\n\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata,\n        };\n      }\n\n      const attachmentsForRequest = await prepareAttachmentsForRequest(\n        options.experimental_attachments,\n      );\n\n      const requestOptions = {\n        headers: options.headers ?? options.options?.headers,\n        body: options.body ?? options.options?.body,\n      };\n\n      const messages =\n        !input && !attachmentsForRequest.length && options.allowEmptySubmit\n          ? messagesRef.current\n          : messagesRef.current.concat({\n              id: generateId(),\n              createdAt: new Date(),\n              role: 'user',\n              content: input,\n              experimental_attachments:\n                attachmentsForRequest.length > 0\n                  ? attachmentsForRequest\n                  : undefined,\n            });\n\n      const chatRequest: ChatRequest = {\n        messages,\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data: options.data,\n      };\n\n      triggerRequest(chatRequest);\n\n      setInput('');\n    },\n    [input, generateId, triggerRequest],\n  );\n\n  const handleInputChange = (e: any) => {\n    setInput(e.target.value);\n  };\n\n  const addToolResult = ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => {\n    const updatedMessages = messagesRef.current.map((message, index, arr) =>\n      // update the tool calls in the last assistant message:\n      index === arr.length - 1 &&\n      message.role === 'assistant' &&\n      message.toolInvocations\n        ? {\n            ...message,\n            toolInvocations: message.toolInvocations.map(toolInvocation =>\n              toolInvocation.toolCallId === toolCallId\n                ? { ...toolInvocation, result }\n                : toolInvocation,\n            ),\n          }\n        : message,\n    );\n\n    mutate(updatedMessages, false);\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const lastMessage = updatedMessages[updatedMessages.length - 1];\n    if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n      triggerRequest({ messages: updatedMessages });\n    }\n  };\n\n  return {\n    messages: messages || [],\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    addToolResult,\n    experimental_addToolResult: addToolResult,\n  };\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe message must have at least one tool invocation and all tool invocations\nmust have a result.\n */\nfunction isAssistantMessageWithCompletedToolCalls(message: Message) {\n  return (\n    message.role === 'assistant' &&\n    message.toolInvocations &&\n    message.toolInvocations.length > 0 &&\n    message.toolInvocations.every(toolInvocation => 'result' in toolInvocation)\n  );\n}\n\n/**\nReturns the number of trailing assistant messages in the array.\n */\nfunction countTrailingAssistantMessages(messages: Message[]) {\n  let count = 0;\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === 'assistant') {\n      count++;\n    } else {\n      break;\n    }\n  }\n  return count;\n}\n\nasync function prepareAttachmentsForRequest(\n  attachmentsFromOptions: FileList | Array<Attachment> | undefined,\n) {\n  if (attachmentsFromOptions == null) {\n    return [];\n  }\n\n  if (attachmentsFromOptions instanceof FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async attachment => {\n        const { name, type } = attachment;\n\n        const dataUrl = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = readerEvent => {\n            resolve(readerEvent.target?.result as string);\n          };\n          reader.onerror = error => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n\n        return {\n          name,\n          contentType: type,\n          url: dataUrl,\n        };\n      }),\n    );\n  }\n\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n\n  throw new Error('Invalid attachments type');\n}\n", "import throttleFunction from 'throttleit';\n\nexport function throttle<T extends (...args: any[]) => any>(\n  fn: T,\n  waitMs: number | undefined,\n): T {\n  return waitMs != null ? throttleFunction(fn, waitMs) : fn;\n}\n", "import {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n  callCompletionApi,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useEffect, useId, useRef, useState } from 'react';\nimport useSWR from 'swr';\nimport { throttle } from './throttle';\n\nexport type { UseCompletionOptions };\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: string;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /** The error object of the API request */\n  error: undefined | Error;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: string;\n  /** setState-powered method to update the input value */\n  setInput: React.Dispatch<React.SetStateAction<string>>;\n  /**\n   * An input/textarea-ready onChange handler to control the value of the input\n   * @example\n   * ```jsx\n   * <input onChange={handleInputChange} value={input} />\n   * ```\n   */\n  handleInputChange: (\n    event:\n      | React.ChangeEvent<HTMLInputElement>\n      | React.ChangeEvent<HTMLTextAreaElement>,\n  ) => void;\n\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form onSubmit={handleSubmit}>\n   *  <input onChange={handleInputChange} value={input} />\n   * </form>\n   * ```\n   */\n  handleSubmit: (event?: { preventDefault?: () => void }) => void;\n\n  /** Whether the API request is in progress */\n  isLoading: boolean;\n  /** Additional data added on the server via StreamData */\n  data?: JSONValue[];\n};\n\nexport function useCompletion({\n  api = '/api/completion',\n  id,\n  initialCompletion = '',\n  initialInput = '',\n  credentials,\n  headers,\n  body,\n  streamMode,\n  streamProtocol,\n  fetch,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs,\n}: UseCompletionOptions & {\n  /**\n   * Custom throttle wait in ms for the completion and data updates.\n   * Default is undefined, which disables throttling.\n   */\n  experimental_throttle?: number;\n} = {}): UseCompletionHelpers {\n  // streamMode is deprecated, use streamProtocol instead.\n  if (streamMode) {\n    streamProtocol ??= streamMode === 'text' ? 'text' : undefined;\n  }\n\n  // Generate an unique id for the completion if not provided.\n  const hookId = useId();\n  const completionId = id || hookId;\n\n  // Store the completion state in SWR, using the completionId as the key to share states.\n  const { data, mutate } = useSWR<string>([api, completionId], null, {\n    fallbackData: initialCompletion,\n  });\n\n  const { data: isLoading = false, mutate: mutateLoading } = useSWR<boolean>(\n    [completionId, 'loading'],\n    null,\n  );\n\n  const { data: streamData, mutate: mutateStreamData } = useSWR<\n    JSONValue[] | undefined\n  >([completionId, 'streamData'], null);\n\n  const [error, setError] = useState<undefined | Error>(undefined);\n  const completion = data!;\n\n  // Abort controller to cancel the current API call.\n  const [abortController, setAbortController] =\n    useState<AbortController | null>(null);\n\n  const extraMetadataRef = useRef({\n    credentials,\n    headers,\n    body,\n  });\n\n  useEffect(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body,\n    };\n  }, [credentials, headers, body]);\n\n  const triggerRequest = useCallback(\n    async (prompt: string, options?: RequestOptions) =>\n      callCompletionApi({\n        api,\n        prompt,\n        credentials: extraMetadataRef.current.credentials,\n        headers: { ...extraMetadataRef.current.headers, ...options?.headers },\n        body: {\n          ...extraMetadataRef.current.body,\n          ...options?.body,\n        },\n        streamProtocol,\n        fetch,\n        // throttle streamed ui updates:\n        setCompletion: throttle(\n          (completion: string) => mutate(completion, false),\n          throttleWaitMs,\n        ),\n        onData: throttle(\n          (data: JSONValue[]) =>\n            mutateStreamData([...(streamData ?? []), ...(data ?? [])], false),\n          throttleWaitMs,\n        ),\n        setLoading: mutateLoading,\n        setError,\n        setAbortController,\n        onResponse,\n        onFinish,\n        onError,\n      }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch,\n      mutateStreamData,\n      throttleWaitMs,\n    ],\n  );\n\n  const stop = useCallback(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n\n  const setCompletion = useCallback(\n    (completion: string) => {\n      mutate(completion, false);\n    },\n    [mutate],\n  );\n\n  const complete = useCallback<UseCompletionHelpers['complete']>(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest],\n  );\n\n  const [input, setInput] = useState(initialInput);\n\n  const handleSubmit = useCallback(\n    (event?: { preventDefault?: () => void }) => {\n      event?.preventDefault?.();\n      return input ? complete(input) : undefined;\n    },\n    [input, complete],\n  );\n\n  const handleInputChange = useCallback(\n    (e: any) => {\n      setInput(e.target.value);\n    },\n    [setInput],\n  );\n\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "import {\n  FetchFunction,\n  isAbortError,\n  safeValidateTypes,\n} from '@ai-sdk/provider-utils';\nimport {\n  asSchema,\n  DeepPartial,\n  isDeepEqualData,\n  parsePartialJson,\n  Schema,\n} from '@ai-sdk/ui-utils';\nimport { useCallback, useId, useRef, useState } from 'react';\nimport useSWR from 'swr';\nimport z from 'zod';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport type Experimental_UseObjectOptions<RESULT> = {\n  /**\n   * The API endpoint. It should stream JSON that matches the schema as chunked text.\n   */\n  api: string;\n\n  /**\n   * A Zod schema that defines the shape of the complete object.\n   */\n  schema: z.Schema<RESULT, z.ZodTypeDef, any> | Schema<RESULT>;\n\n  /**\n   * An unique identifier. If not provided, a random one will be\n   * generated. When provided, the `useObject` hook with the same `id` will\n   * have shared states across components.\n   */\n  id?: string;\n\n  /**\n   * An optional value for the initial object.\n   */\n  initialValue?: DeepPartial<RESULT>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  /**\nCallback that is called when the stream has finished.\n     */\n  onFinish?: (event: {\n    /**\nThe generated object (typed according to the schema).\nCan be undefined if the final object does not match the schema.\n   */\n    object: RESULT | undefined;\n\n    /**\nOptional error object. This is e.g. a TypeValidationError when the final object does not match the schema.\n */\n    error: Error | undefined;\n  }) => Promise<void> | void;\n\n  /**\n   * Callback function to be called when an error is encountered.\n   */\n  onError?: (error: Error) => void;\n\n  /**\n   * Additional HTTP headers to be included in the request.\n   */\n  headers?: Record<string, string> | Headers;\n};\n\nexport type Experimental_UseObjectHelpers<RESULT, INPUT> = {\n  /**\n   * @deprecated Use `submit` instead.\n   */\n  setInput: (input: INPUT) => void;\n\n  /**\n   * Calls the API with the provided input as JSON body.\n   */\n  submit: (input: INPUT) => void;\n\n  /**\n   * The current value for the generated object. Updated as the API streams JSON chunks.\n   */\n  object: DeepPartial<RESULT> | undefined;\n\n  /**\n   * The error object of the API request if any.\n   */\n  error: Error | undefined;\n\n  /**\n   * Flag that indicates whether an API request is in progress.\n   */\n  isLoading: boolean;\n\n  /**\n   * Abort the current request immediately, keep the current partial object if any.\n   */\n  stop: () => void;\n};\n\nfunction useObject<RESULT, INPUT = any>({\n  api,\n  id,\n  schema, // required, in the future we will use it for validation\n  initialValue,\n  fetch,\n  onError,\n  onFinish,\n  headers,\n}: Experimental_UseObjectOptions<RESULT>): Experimental_UseObjectHelpers<\n  RESULT,\n  INPUT\n> {\n  // Generate an unique id if not provided.\n  const hookId = useId();\n  const completionId = id ?? hookId;\n\n  // Store the completion state in SWR, using the completionId as the key to share states.\n  const { data, mutate } = useSWR<DeepPartial<RESULT>>(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue },\n  );\n\n  const [error, setError] = useState<undefined | Error>(undefined);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Abort controller to cancel the current API call.\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const stop = useCallback(() => {\n    try {\n      abortControllerRef.current?.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  const submit = async (input: INPUT) => {\n    try {\n      mutate(undefined); // reset the data\n      setIsLoading(true);\n      setError(undefined);\n\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n\n      const actualFetch = fetch ?? getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...headers,\n        },\n        signal: abortController.signal,\n        body: JSON.stringify(input),\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          (await response.text()) ?? 'Failed to fetch the response.',\n        );\n      }\n\n      if (response.body == null) {\n        throw new Error('The response body is empty.');\n      }\n\n      let accumulatedText = '';\n      let latestObject: DeepPartial<RESULT> | undefined = undefined;\n\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream<string>({\n          write(chunk) {\n            accumulatedText += chunk;\n\n            const { value } = parsePartialJson(accumulatedText);\n            const currentObject = value as DeepPartial<RESULT>;\n\n            if (!isDeepEqualData(latestObject, currentObject)) {\n              latestObject = currentObject;\n\n              mutate(currentObject);\n            }\n          },\n\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n\n            if (onFinish != null) {\n              const validationResult = safeValidateTypes({\n                value: latestObject,\n                schema: asSchema(schema),\n              });\n\n              onFinish(\n                validationResult.success\n                  ? { object: validationResult.value, error: undefined }\n                  : { object: undefined, error: validationResult.error },\n              );\n            }\n          },\n        }),\n      );\n    } catch (error) {\n      if (isAbortError(error)) {\n        return;\n      }\n\n      if (onError && error instanceof Error) {\n        onError(error);\n      }\n\n      setIsLoading(false);\n      setError(error instanceof Error ? error : new Error(String(error)));\n    }\n  };\n\n  return {\n    setInput: submit, // Deprecated\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop,\n  };\n}\n\nexport const experimental_useObject = useObject;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,4BAA6B;AAC7B,sBAOO;AACP,mBAA8C;AAG9C,IAAM,mBAAmB,MAAM;AAiFxB,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAA;AACF,GAA6C;AAC3C,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAoB,CAAC,CAAC;AACtD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,EAAE;AACrC,QAAM,CAAC,iBAAiB,kBAAkB,QAAI;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAA0B,kBAAkB;AACxE,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAA4B,MAAS;AAE/D,QAAM,oBAAoB,CACxB,UAGG;AACH,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AAGA,QAAM,yBAAqB,qBAA+B,IAAI;AAE9D,QAAM,WAAO,0BAAY,MAAM;AAC7B,QAAI,mBAAmB,SAAS;AAC9B,yBAAmB,QAAQ,MAAM;AACjC,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,SAAS,OACb,SACA,mBAGG;AArIP;AAsII,cAAU,aAAa;AAEvB,gBAAY,CAAAC,cAAS;AAxIzB,UAAAC;AAwI4B;AAAA,QACtB,GAAGD;AAAA,QACH;AAAA,UACE,GAAG;AAAA,UACH,KAAIC,MAAA,QAAQ,OAAR,OAAAA,UAAc,4BAAW;AAAA,QAC/B;AAAA,MACF;AAAA,KAAC;AAED,aAAS,EAAE;AAEX,UAAM,kBAAkB,IAAI,gBAAgB;AAE5C,QAAI;AACF,yBAAmB,UAAU;AAE7B,YAAM,cAAcF,UAAA,OAAAA,SAAS,iBAAiB;AAC9C,YAAM,WAAW,MAAM,YAAY,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,SAAS,EAAE,gBAAgB,oBAAoB,GAAG,QAAQ;AAAA,QAC1D,MAAM,KAAK,UAAU;AAAA,UACnB,GAAG;AAAA;AAAA,UAEH,WAAU,6CAAiB,oBAAjB,YAAoC;AAAA,UAC9C,SAAS,QAAQ;AAAA;AAAA,UAGjB,MAAM,iDAAgB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,MAAM;AACzB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,uBAAiB,EAAE,MAAM,MAAM,SAAK;AAAA,QAClC,SAAS,KAAK,UAAU;AAAA,MAC1B,GAAG;AACD,gBAAQ,MAAM;AAAA,UACZ,KAAK,qBAAqB;AACxB,wBAAY,CAAAC,cAAY;AAAA,cACtB,GAAGA;AAAA,cACH;AAAA,gBACE,IAAI,MAAM;AAAA,gBACV,MAAM,MAAM;AAAA,gBACZ,SAAS,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,cACjC;AAAA,YACF,CAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,QAAQ;AAEX,wBAAY,CAAAA,cAAY;AACtB,oBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,qBAAO;AAAA,gBACL,GAAGA,UAAS,MAAM,GAAGA,UAAS,SAAS,CAAC;AAAA,gBACxC;AAAA,kBACE,IAAI,YAAY;AAAA,kBAChB,MAAM,YAAY;AAAA,kBAClB,SAAS,YAAY,UAAU;AAAA,gBACjC;AAAA,cACF;AAAA,YACF,CAAC;AAED;AAAA,UACF;AAAA,UAEA,KAAK,gBAAgB;AACnB,wBAAY,CAAAA,cAAS;AApNjC,kBAAAC;AAoNoC;AAAA,gBACtB,GAAGD;AAAA,gBACH;AAAA,kBACE,KAAIC,MAAA,MAAM,OAAN,OAAAA,UAAY,4BAAW;AAAA,kBAC3B,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,MAAM,MAAM;AAAA,gBACd;AAAA,cACF;AAAA,aAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,0BAA0B;AAC7B,+BAAmB,MAAM,QAAQ;AAGjC,wBAAY,CAAAD,cAAY;AACtB,oBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,0BAAY,KAAK,MAAM;AACvB,qBAAO,CAAC,GAAGA,UAAS,MAAM,GAAGA,UAAS,SAAS,CAAC,GAAG,WAAW;AAAA,YAChE,CAAC;AAED;AAAA,UACF;AAAA,UAEA,KAAK,SAAS;AACZ,qBAAS,IAAI,MAAM,KAAK,CAAC;AACzB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAASE,QAAO;AAEd,cAAI,oCAAaA,MAAK,KAAK,gBAAgB,OAAO,SAAS;AACzD,2BAAmB,UAAU;AAC7B;AAAA,MACF;AAEA,UAAI,WAAWA,kBAAiB,OAAO;AACrC,gBAAQA,MAAK;AAAA,MACf;AAEA,eAASA,MAAc;AAAA,IACzB,UAAE;AACA,yBAAmB,UAAU;AAC7B,gBAAU,kBAAkB;AAAA,IAC9B;AAAA,EACF;AAEA,QAAM,gBAAgB,OACpB,OACA,mBAGG;AA1QP;AA2QI,yCAAO,mBAAP;AAEA,QAAI,UAAU,IAAI;AAChB;AAAA,IACF;AAEA,WAAO,EAAE,MAAM,QAAQ,SAAS,MAAM,GAAG,cAAc;AAAA,EACzD;AAEA,QAAM,cAAc,CAAC,aAAiC;AACpD,uBAAmB,QAAQ;AAC3B,gBAAY,CAAC,CAAC;AAAA,EAChB;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKO,IAAM,4BAA4B;;;ACjSzC,IAAAC,mBAIO;AACP,IAAAC,gBAAgE;AAChE,iBAAqC;;;ACjBrC,wBAA6B;AAEtB,SAAS,SACd,IACA,QACG;AACH,SAAO,UAAU,WAAO,kBAAAC,SAAiB,IAAI,MAAM,IAAI;AACzD;;;ADgFA,IAAM,sBAAsB,OAC1B,KACA,aACA,QACA,kBACA,iBACA,kBACA,aACA,oBACAC,aACA,gBACA,UACA,YACA,YACA,wBACA,iCAOAC,QACA,2BACG;AA/GL;AAiHE,QAAM,mBAAmB,YAAY;AACrC,SAAO,YAAY,UAAU,KAAK;AAElC,QAAM,6BAA6B,yBAC/B,YAAY,WACZ,YAAY,SAAS;AAAA,IACnB,CAAC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,GAAI,6BAA6B,UAAa;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MAC/C,GAAI,oBAAoB,UAAa,EAAE,gBAAgB;AAAA;AAAA,MAEvD;AAAA,MACA,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,MACnD,GAAI,eAAe,UAAa,EAAE,WAAW;AAAA,IAC/C;AAAA,EACF;AAEJ,QAAM,eAAe,gBAAgB;AAErC,SAAO,UAAM,8BAAY;AAAA,IACvB;AAAA,IACA,OAAM,wFAAkC;AAAA,MACtC,UAAU,YAAY;AAAA,MACtB,aAAa,YAAY;AAAA,MACzB,aAAa,YAAY;AAAA,IAC3B,OAJM,YAIA;AAAA,MACJ,UAAU;AAAA,MACV,MAAM,YAAY;AAAA,MAClB,GAAG,iBAAiB,QAAQ;AAAA,MAC5B,GAAG,YAAY;AAAA,MACf,GAAI,YAAY,cAAc,UAAa;AAAA,QACzC,WAAW,YAAY;AAAA,MACzB;AAAA,MACA,GAAI,YAAY,kBAAkB,UAAa;AAAA,QAC7C,eAAe,YAAY;AAAA,MAC7B;AAAA,MACA,GAAI,YAAY,UAAU,UAAa;AAAA,QACrC,OAAO,YAAY;AAAA,MACrB;AAAA,MACA,GAAI,YAAY,gBAAgB,UAAa;AAAA,QAC3C,aAAa,YAAY;AAAA,MAC3B;AAAA,IACF;AAAA,IACA;AAAA,IACA,aAAa,iBAAiB,QAAQ;AAAA,IACtC,SAAS;AAAA,MACP,GAAG,iBAAiB,QAAQ;AAAA,MAC5B,GAAG,YAAY;AAAA,IACjB;AAAA,IACA,iBAAiB,MAAM,mBAAmB;AAAA,IAC1C,2BAA2B;AACzB,UAAI,CAAC,wBAAwB;AAC3B,eAAO,kBAAkB,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,MAAM;AACrB,aAAO,CAAC,GAAG,YAAY,UAAU,GAAG,MAAM,GAAG,KAAK;AAClD,uBAAiB,CAAC,GAAI,sCAAgB,CAAC,GAAI,GAAI,sBAAQ,CAAC,CAAE,GAAG,KAAK;AAAA,IACpE;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAAD;AAAA,IACA,OAAAC;AAAA,EACF,CAAC;AACH;AAEO,SAAS,QAAQ;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sCAAsC;AAAA,EACtC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,WAAW,qBAAqB,OAAO,oBAAoB,IAAI;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAD,cAAa,iBAAAE;AAAA,EACb,OAAAD;AAAA,EACA,yBAAyB;AAAA,EACzB,uBAAuB;AACzB,IA0DI,CAAC,GAkBH;AAEA,MAAI,YAAY;AACd,+DAAmB,eAAe,SAAS,SAAS;AAAA,EACtD;AAGA,QAAM,aAAS,qBAAM;AACrB,QAAM,QAAQ,kBAAM;AACpB,QAAM,UAAU,OAAO,QAAQ,WAAW,CAAC,KAAK,KAAK,IAAI;AAKzD,QAAM,CAAC,uBAAuB,QAAI,wBAAS,CAAC,CAAC;AAG7C,QAAM,EAAE,MAAM,UAAU,OAAO,QAAI,WAAAE;AAAA,IACjC,CAAC,SAAS,UAAU;AAAA,IACpB;AAAA,IACA,EAAE,cAAc,4CAAmB,wBAAwB;AAAA,EAC7D;AAGA,QAAM,kBAAc,sBAAkB,YAAY,CAAC,CAAC;AACpD,+BAAU,MAAM;AACd,gBAAY,UAAU,YAAY,CAAC;AAAA,EACrC,GAAG,CAAC,QAAQ,CAAC;AAGb,QAAM,EAAE,MAAM,YAAY,QAAQ,iBAAiB,QAAI,WAAAA,SAErD,CAAC,SAAS,YAAY,GAAG,IAAI;AAG/B,QAAM,oBAAgB,sBAAgC,UAAU;AAChE,+BAAU,MAAM;AACd,kBAAc,UAAU;AAAA,EAC1B,GAAG,CAAC,UAAU,CAAC;AAGf,QAAM,EAAE,MAAM,YAAY,OAAO,QAAQ,cAAc,QAAI,WAAAA;AAAA,IACzD,CAAC,SAAS,SAAS;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,EAAE,MAAM,QAAQ,QAAW,QAAQ,SAAS,QAAI,WAAAA,SAEpD,CAAC,SAAS,OAAO,GAAG,IAAI;AAG1B,QAAM,yBAAqB,sBAA+B,IAAI;AAE9D,QAAM,uBAAmB,sBAAO;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,+BAAU,MAAM;AACd,qBAAiB,UAAU;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC;AAE/B,QAAM,qBAAiB;AAAA,IACrB,OAAO,gBAA6B;AAClC,YAAM,eAAe,YAAY,QAAQ;AAEzC,UAAI;AACF,sBAAc,IAAI;AAClB,iBAAS,MAAS;AAElB,cAAM,kBAAkB,IAAI,gBAAgB;AAC5C,2BAAmB,UAAU;AAE7B,kBAAM,oCAAkB;AAAA,UACtB,qBAAqB,MACnB;AAAA,YACE;AAAA,YACA;AAAA;AAAA,YAEA,SAAS,QAAQ,cAAc;AAAA,YAC/B,SAAS,kBAAkB,cAAc;AAAA,YACzC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACAH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACAC;AAAA,YACA;AAAA,UACF;AAAA,UACF;AAAA,UACA;AAAA,UACA,mBAAmB,sBAAoB;AACrC,0BAAc;AAAA,UAChB;AAAA,UACA,oBAAoB,MAAM,YAAY;AAAA,QACxC,CAAC;AAED,2BAAmB,UAAU;AAAA,MAC/B,SAAS,KAAK;AAEZ,YAAK,IAAY,SAAS,cAAc;AACtC,6BAAmB,UAAU;AAC7B,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,eAAe,OAAO;AACnC,kBAAQ,GAAG;AAAA,QACb;AAEA,iBAAS,GAAY;AAAA,MACvB,UAAE;AACA,sBAAc,KAAK;AAAA,MACrB;AAGA,YAAMG,YAAW,YAAY;AAC7B,YAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD;AAAA;AAAA,QAEEA,UAAS,SAAS;AAAA,QAElB,eAAe;AAAA,QAEf,WAAW;AAAA,QAEX,yCAAyC,WAAW;AAAA,QAEpD,+BAA+BA,SAAQ,IAAI;AAAA,QAC3C;AACA,cAAM,eAAe,EAAE,UAAAA,UAAS,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACAJ;AAAA,MACAC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAS;AAAA,IACb,OACE,SACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAAI;AAAA,MACA,MAAAC;AAAA,MACA;AAAA,IACF,IAAwB,CAAC,MACtB;AAneT;AAoeM,UAAI,CAAC,QAAQ,IAAI;AACf,gBAAQ,KAAKN,YAAW;AAAA,MAC1B;AAEA,YAAM,wBAAwB,MAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB,SAASK,YAAA,OAAAA,WAAW,mCAAS;AAAA,QAC7B,MAAMC,SAAA,OAAAA,QAAQ,mCAAS;AAAA,MACzB;AAEA,YAAMF,YAAW,YAAY,QAAQ,OAAO;AAAA,QAC1C,GAAG;AAAA,QACH,KAAI,aAAQ,OAAR,YAAcJ,YAAW;AAAA,QAC7B,YAAW,aAAQ,cAAR,YAAqB,oBAAI,KAAK;AAAA,QACzC,0BACE,sBAAsB,SAAS,IAAI,wBAAwB;AAAA,MAC/D,CAAC;AAED,YAAM,cAA2B;AAAA,QAC/B,UAAAI;AAAA,QACA,SAAS;AAAA,QACT,SAAS,eAAe;AAAA,QACxB,MAAM,eAAe;AAAA,QACrB;AAAA,QACA,GAAI,cAAc,UAAa,EAAE,UAAU;AAAA,QAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,QACnD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,QACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MACjD;AAEA,aAAO,eAAe,WAAW;AAAA,IACnC;AAAA,IACA,CAAC,gBAAgBJ,WAAU;AAAA,EAC7B;AAEA,QAAM,aAAS;AAAA,IACb,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAAK;AAAA,MACA,MAAAC;AAAA,IACF,IAAwB,CAAC,MAAM;AAC7B,UAAI,YAAY,QAAQ,WAAW;AAAG,eAAO;AAE7C,YAAM,iBAAiB;AAAA,QACrB,SAASD,YAAA,OAAAA,WAAW,mCAAS;AAAA,QAC7B,MAAMC,SAAA,OAAAA,QAAQ,mCAAS;AAAA,MACzB;AAGA,YAAM,cAAc,YAAY,QAAQ,YAAY,QAAQ,SAAS,CAAC;AACtE,UAAI,YAAY,SAAS,aAAa;AACpC,cAAMC,eAA2B;AAAA,UAC/B,UAAU,YAAY,QAAQ,MAAM,GAAG,EAAE;AAAA,UACzC,SAAS;AAAA,UACT,SAAS,eAAe;AAAA,UACxB,MAAM,eAAe;AAAA,UACrB;AAAA,UACA,GAAI,cAAc,UAAa,EAAE,UAAU;AAAA,UAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,UACnD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,UACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,QACjD;AAEA,eAAO,eAAeA,YAAW;AAAA,MACnC;AAEA,YAAM,cAA2B;AAAA,QAC/B,UAAU,YAAY;AAAA,QACtB,SAAS;AAAA,QACT,SAAS,eAAe;AAAA,QACxB,MAAM,eAAe;AAAA,QACrB;AAAA,QACA,GAAI,cAAc,UAAa,EAAE,UAAU;AAAA,QAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,QACnD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,QACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MACjD;AAEA,aAAO,eAAe,WAAW;AAAA,IACnC;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,WAAO,2BAAY,MAAM;AAC7B,QAAI,mBAAmB,SAAS;AAC9B,yBAAmB,QAAQ,MAAM;AACjC,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc;AAAA,IAClB,CAACH,cAA+D;AAC9D,UAAI,OAAOA,cAAa,YAAY;AAClC,QAAAA,YAAWA,UAAS,YAAY,OAAO;AAAA,MACzC;AAEA,aAAOA,WAAU,KAAK;AACtB,kBAAY,UAAUA;AAAA,IACxB;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,cAAU;AAAA,IACd,CACE,SAIG;AACH,UAAI,OAAO,SAAS,YAAY;AAC9B,eAAO,KAAK,cAAc,OAAO;AAAA,MACnC;AAEA,uBAAiB,MAAM,KAAK;AAC5B,oBAAc,UAAU;AAAA,IAC1B;AAAA,IACA,CAAC,gBAAgB;AAAA,EACnB;AAGA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AAE/C,QAAM,mBAAe;AAAA,IACnB,OACE,OACA,UAA8B,CAAC,GAC/B,aACG;AA3mBT;AA4mBM,2CAAO,mBAAP;AAEA,UAAI,CAAC,SAAS,CAAC,QAAQ;AAAkB;AAEzC,UAAI,UAAU;AACZ,yBAAiB,UAAU;AAAA,UACzB,GAAG,iBAAiB;AAAA,UACpB,GAAG;AAAA,QACL;AAAA,MACF;AAEA,YAAM,wBAAwB,MAAM;AAAA,QAClC,QAAQ;AAAA,MACV;AAEA,YAAM,iBAAiB;AAAA,QACrB,UAAS,aAAQ,YAAR,aAAmB,aAAQ,YAAR,mBAAiB;AAAA,QAC7C,OAAM,aAAQ,SAAR,aAAgB,aAAQ,YAAR,mBAAiB;AAAA,MACzC;AAEA,YAAMA,YACJ,CAAC,SAAS,CAAC,sBAAsB,UAAU,QAAQ,mBAC/C,YAAY,UACZ,YAAY,QAAQ,OAAO;AAAA,QACzB,IAAIJ,YAAW;AAAA,QACf,WAAW,oBAAI,KAAK;AAAA,QACpB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,0BACE,sBAAsB,SAAS,IAC3B,wBACA;AAAA,MACR,CAAC;AAEP,YAAM,cAA2B;AAAA,QAC/B,UAAAI;AAAA,QACA,SAAS;AAAA,QACT,SAAS,eAAe;AAAA,QACxB,MAAM,eAAe;AAAA,QACrB,MAAM,QAAQ;AAAA,MAChB;AAEA,qBAAe,WAAW;AAE1B,eAAS,EAAE;AAAA,IACb;AAAA,IACA,CAAC,OAAOJ,aAAY,cAAc;AAAA,EACpC;AAEA,QAAM,oBAAoB,CAAC,MAAW;AACpC,aAAS,EAAE,OAAO,KAAK;AAAA,EACzB;AAEA,QAAM,gBAAgB,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF,MAGM;AACJ,UAAM,kBAAkB,YAAY,QAAQ;AAAA,MAAI,CAAC,SAAS,OAAO;AAAA;AAAA,QAE/D,UAAU,IAAI,SAAS,KACvB,QAAQ,SAAS,eACjB,QAAQ,kBACJ;AAAA,UACE,GAAG;AAAA,UACH,iBAAiB,QAAQ,gBAAgB;AAAA,YAAI,oBAC3C,eAAe,eAAe,aAC1B,EAAE,GAAG,gBAAgB,OAAO,IAC5B;AAAA,UACN;AAAA,QACF,IACA;AAAA;AAAA,IACN;AAEA,WAAO,iBAAiB,KAAK;AAG7B,UAAM,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAC9D,QAAI,yCAAyC,WAAW,GAAG;AACzD,qBAAe,EAAE,UAAU,gBAAgB,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,SAAO;AAAA,IACL,UAAU,YAAY,CAAC;AAAA,IACvB;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,4BAA4B;AAAA,EAC9B;AACF;AAOA,SAAS,yCAAyC,SAAkB;AAClE,SACE,QAAQ,SAAS,eACjB,QAAQ,mBACR,QAAQ,gBAAgB,SAAS,KACjC,QAAQ,gBAAgB,MAAM,oBAAkB,YAAY,cAAc;AAE9E;AAKA,SAAS,+BAA+B,UAAqB;AAC3D,MAAI,QAAQ;AACZ,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AACpC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,eAAe,6BACb,wBACA;AACA,MAAI,0BAA0B,MAAM;AAClC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,kCAAkC,UAAU;AAC9C,WAAO,QAAQ;AAAA,MACb,MAAM,KAAK,sBAAsB,EAAE,IAAI,OAAM,eAAc;AACzD,cAAM,EAAE,MAAM,KAAK,IAAI;AAEvB,cAAM,UAAU,MAAM,IAAI,QAAgB,CAAC,SAAS,WAAW;AAC7D,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,SAAS,iBAAe;AA/vBzC;AAgwBY,qBAAQ,iBAAY,WAAZ,mBAAoB,MAAgB;AAAA,UAC9C;AACA,iBAAO,UAAU,WAAS,OAAO,KAAK;AACtC,iBAAO,cAAc,UAAU;AAAA,QACjC,CAAC;AAED,eAAO;AAAA,UACL;AAAA,UACA,aAAa;AAAA,UACb,KAAK;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,sBAAsB,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,MAAM,0BAA0B;AAC5C;;;AEpxBA,IAAAQ,mBAKO;AACP,IAAAC,gBAAgE;AAChE,IAAAC,cAAmB;AA2DZ,SAAS,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN;AAAA,EACA,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AACzB,IAMI,CAAC,GAAyB;AAE5B,MAAI,YAAY;AACd,+DAAmB,eAAe,SAAS,SAAS;AAAA,EACtD;AAGA,QAAM,aAAS,qBAAM;AACrB,QAAM,eAAe,MAAM;AAG3B,QAAM,EAAE,MAAM,OAAO,QAAI,YAAAC,SAAe,CAAC,KAAK,YAAY,GAAG,MAAM;AAAA,IACjE,cAAc;AAAA,EAChB,CAAC;AAED,QAAM,EAAE,MAAM,YAAY,OAAO,QAAQ,cAAc,QAAI,YAAAA;AAAA,IACzD,CAAC,cAAc,SAAS;AAAA,IACxB;AAAA,EACF;AAEA,QAAM,EAAE,MAAM,YAAY,QAAQ,iBAAiB,QAAI,YAAAA,SAErD,CAAC,cAAc,YAAY,GAAG,IAAI;AAEpC,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAA4B,MAAS;AAC/D,QAAM,aAAa;AAGnB,QAAM,CAAC,iBAAiB,kBAAkB,QACxC,wBAAiC,IAAI;AAEvC,QAAM,uBAAmB,sBAAO;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,+BAAU,MAAM;AACd,qBAAiB,UAAU;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,SAAS,IAAI,CAAC;AAE/B,QAAM,qBAAiB;AAAA,IACrB,OAAO,QAAgB,gBACrB,oCAAkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,aAAa,iBAAiB,QAAQ;AAAA,MACtC,SAAS,EAAE,GAAG,iBAAiB,QAAQ,SAAS,GAAG,mCAAS,QAAQ;AAAA,MACpE,MAAM;AAAA,QACJ,GAAG,iBAAiB,QAAQ;AAAA,QAC5B,GAAG,mCAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA,OAAAD;AAAA;AAAA,MAEA,eAAe;AAAA,QACb,CAACE,gBAAuB,OAAOA,aAAY,KAAK;AAAA,QAChD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,CAACC,UACC,iBAAiB,CAAC,GAAI,kCAAc,CAAC,GAAI,GAAIA,SAAA,OAAAA,QAAQ,CAAC,CAAE,GAAG,KAAK;AAAA,QAClE;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACH;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACAH;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,WAAO,2BAAY,MAAM;AAC7B,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,yBAAmB,IAAI;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,QAAM,oBAAgB;AAAA,IACpB,CAACE,gBAAuB;AACtB,aAAOA,aAAY,KAAK;AAAA,IAC1B;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,eAAW;AAAA,IACf,OAAO,QAAQ,YAAY;AACzB,aAAO,eAAe,QAAQ,OAAO;AAAA,IACvC;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AAE/C,QAAM,mBAAe;AAAA,IACnB,CAAC,UAA4C;AA5MjD;AA6MM,2CAAO,mBAAP;AACA,aAAO,QAAQ,SAAS,KAAK,IAAI;AAAA,IACnC;AAAA,IACA,CAAC,OAAO,QAAQ;AAAA,EAClB;AAEA,QAAM,wBAAoB;AAAA,IACxB,CAAC,MAAW;AACV,eAAS,EAAE,OAAO,KAAK;AAAA,IACzB;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;;;ACvOA,IAAAE,yBAIO;AACP,IAAAC,mBAMO;AACP,IAAAC,gBAAqD;AACrD,IAAAC,cAAmB;AAInB,IAAMC,oBAAmB,MAAM;AA0F/B,SAAS,UAA+B;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAGE;AAEA,QAAM,aAAS,qBAAM;AACrB,QAAM,eAAe,kBAAM;AAG3B,QAAM,EAAE,MAAM,OAAO,QAAI,YAAAC;AAAA,IACvB,CAAC,KAAK,YAAY;AAAA,IAClB;AAAA,IACA,EAAE,cAAc,aAAa;AAAA,EAC/B;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAA4B,MAAS;AAC/D,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAGhD,QAAM,yBAAqB,sBAA+B,IAAI;AAE9D,QAAM,WAAO,2BAAY,MAAM;AAzIjC;AA0II,QAAI;AACF,+BAAmB,YAAnB,mBAA4B;AAAA,IAC9B,SAAS,SAAS;AAAA,IAClB,UAAE;AACA,mBAAa,KAAK;AAClB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,SAAS,OAAO,UAAiB;AAnJzC;AAoJI,QAAI;AACF,aAAO,MAAS;AAChB,mBAAa,IAAI;AACjB,eAAS,MAAS;AAElB,YAAM,kBAAkB,IAAI,gBAAgB;AAC5C,yBAAmB,UAAU;AAE7B,YAAM,cAAcD,UAAA,OAAAA,SAASD,kBAAiB;AAC9C,YAAM,WAAW,MAAM,YAAY,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,GAAG;AAAA,QACL;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,MAAM,KAAK,UAAU,KAAK;AAAA,MAC5B,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,MAAM;AACzB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,UAAI,kBAAkB;AACtB,UAAI,eAAgD;AAEpD,YAAM,SAAS,KAAK,YAAY,IAAI,kBAAkB,CAAC,EAAE;AAAA,QACvD,IAAI,eAAuB;AAAA,UACzB,MAAM,OAAO;AACX,+BAAmB;AAEnB,kBAAM,EAAE,MAAM,QAAI,mCAAiB,eAAe;AAClD,kBAAM,gBAAgB;AAEtB,gBAAI,KAAC,kCAAgB,cAAc,aAAa,GAAG;AACjD,6BAAe;AAEf,qBAAO,aAAa;AAAA,YACtB;AAAA,UACF;AAAA,UAEA,QAAQ;AACN,yBAAa,KAAK;AAClB,+BAAmB,UAAU;AAE7B,gBAAI,YAAY,MAAM;AACpB,oBAAM,uBAAmB,0CAAkB;AAAA,gBACzC,OAAO;AAAA,gBACP,YAAQ,2BAAS,MAAM;AAAA,cACzB,CAAC;AAED;AAAA,gBACE,iBAAiB,UACb,EAAE,QAAQ,iBAAiB,OAAO,OAAO,OAAU,IACnD,EAAE,QAAQ,QAAW,OAAO,iBAAiB,MAAM;AAAA,cACzD;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,SAASG,QAAO;AACd,cAAI,qCAAaA,MAAK,GAAG;AACvB;AAAA,MACF;AAEA,UAAI,WAAWA,kBAAiB,OAAO;AACrC,gBAAQA,MAAK;AAAA,MACf;AAEA,mBAAa,KAAK;AAClB,eAASA,kBAAiB,QAAQA,SAAQ,IAAI,MAAM,OAAOA,MAAK,CAAC,CAAC;AAAA,IACpE;AAAA,EACF;AAEA,SAAO;AAAA,IACL,UAAU;AAAA;AAAA,IACV;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAM,yBAAyB;", "names": ["fetch", "messages", "_a", "error", "import_ui_utils", "import_react", "throttleFunction", "generateId", "fetch", "generateIdFunc", "useSWR", "messages", "headers", "body", "chatRequest", "import_ui_utils", "import_react", "import_swr", "fetch", "useSWR", "completion", "data", "import_provider_utils", "import_ui_utils", "import_react", "import_swr", "getOriginalFetch", "fetch", "useSWR", "error"]}