{"version": 3, "sources": ["../../src/test/index.ts", "../../src/test/convert-array-to-async-iterable.ts", "../../src/test/convert-array-to-readable-stream.ts", "../../src/test/convert-async-iterable-to-array.ts", "../../src/test/convert-readable-stream-to-array.ts", "../../src/test/convert-response-stream-to-array.ts", "../../../../node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/format.ts", "../../../../node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/invariant.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/devUtils.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/checkGlobals.ts", "../../../../node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/MemoryLeakError.ts", "../../../../node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/Emitter.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/pipeEvents.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/toReadonlyArray.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/Disposable.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/SetupApi.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/getCallFrame.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/isIterable.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/handlers/RequestHandler.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/isStringEqual.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/logging/getStatusCodeColor.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/logging/getTimestamp.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/logging/serializeRequest.ts", "../../../../node_modules/.pnpm/@bundled-es-modules+statuses@1.0.1/node_modules/@bundled-es-modules/statuses/index-esm.js", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/logging/serializeResponse.ts", "../../../../node_modules/.pnpm/path-to-regexp@6.2.2/node_modules/path-to-regexp/src/index.ts", "../../../../node_modules/.pnpm/is-node-process@1.2.0/node_modules/is-node-process/src/index.ts", "../../../../node_modules/.pnpm/outvariant@1.4.2/node_modules/outvariant/src/format.ts", "../../../../node_modules/.pnpm/outvariant@1.4.2/node_modules/outvariant/src/invariant.ts", "../../../../node_modules/.pnpm/@open-draft+logger@0.3.0/node_modules/@open-draft/logger/lib/index.mjs", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/Interceptor.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/createRequestId.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/isPropertyAccessible.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/responseUtils.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/BatchInterceptor.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/bufferUtils.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/glossary.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/getCleanUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/url/cleanUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/url/isAbsoluteUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/url/getAbsoluteUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/matching/normalizePath.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/matching/matchRequestUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/request/toPublicUrl.ts", "../../../../node_modules/.pnpm/@bundled-es-modules+cookie@2.0.0/node_modules/@bundled-es-modules/cookie/index-esm.js", "../../../../node_modules/.pnpm/@mswjs+cookies@1.1.1/node_modules/@mswjs/cookies/node_modules/set-cookie-parser/lib/set-cookie.js", "../../../../node_modules/.pnpm/@mswjs+cookies@1.1.1/node_modules/@mswjs/cookies/src/store.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/request/getRequestCookies.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/handlers/HttpHandler.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/http.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/node_modules/set-cookie-parser/lib/set-cookie.js", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/Headers.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/utils/normalizeHeaderName.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/utils/normalizeHeaderValue.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/utils/isValidHeaderName.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/utils/isValidHeaderValue.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/getRawHeaders.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/headersToList.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/headersToString.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/headersToObject.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/stringToHeaders.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/listToHeaders.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/reduceHeadersObject.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/objectToHeaders.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/flattenHeadersList.ts", "../../../../node_modules/.pnpm/headers-polyfill@4.0.3/node_modules/headers-polyfill/src/transformers/flattenHeadersObject.ts", "../../../../node_modules/.pnpm/@open-draft+until@2.1.0/node_modules/@open-draft/until/src/until.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/executeHandlers.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/request/onUnhandledRequest.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/request/readResponseCookies.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/handleRequest.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/HttpResponse/decorators.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/HttpResponse.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/index.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/node/SetupServerApi.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/node/SetupServerCommonApi.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/node/setupServer.ts", "../../../../node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/createDeferredExecutor.ts", "../../../../node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/DeferredPromise.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/RequestController.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/toInteractiveRequest.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/emitAsync.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/index.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/NodeClientRequest.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/normalizeClientRequestEndArgs.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/normalizeClientRequestWriteArgs.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/cloneIncomingMessage.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/createResponse.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/createRequest.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/getValueBySymbol.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/isObject.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/getRawFetchHeaders.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/isNodeLikeError.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/utils/normalizeClientRequestArgs.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/getRequestOptionsByUrl.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/getUrlByRequestOptions.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/cloneObject.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/http.get.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/ClientRequest/http.request.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/index.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/concatArrayBuffer.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/EventPolyfill.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/ProgressEventPolyfill.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createEvent.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/findPropertySource.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/createProxy.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/isDomParserSupportedType.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/parseJson.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createResponse.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/index.ts", "../../../../node_modules/.pnpm/@mswjs+interceptors@0.29.1/node_modules/@mswjs/interceptors/src/utils/canParseUrl.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/isObject.ts", "../../../../node_modules/.pnpm/msw@2.3.1_typescript@5.5.4/node_modules/msw/src/core/utils/internal/mergeRight.ts", "../../src/test/json-test-server.ts", "../../src/test/streaming-test-server.ts", "../../src/test/test-server.ts"], "sourcesContent": ["export * from './convert-array-to-async-iterable';\nexport * from './convert-array-to-readable-stream';\nexport * from './convert-async-iterable-to-array';\nexport * from './convert-readable-stream-to-array';\nexport * from './convert-response-stream-to-array';\nexport * from './json-test-server';\nexport * from './streaming-test-server';\nexport * from './test-server';\n\nimport { convertReadableStreamToArray } from './convert-readable-stream-to-array';\n\n/**\n * @deprecated Use `convertReadableStreamToArray` instead.\n */\nexport const convertStreamToArray = convertReadableStreamToArray;\n", "export function convertArrayToAsyncIterable<T>(values: T[]): AsyncIterable<T> {\n  return {\n    async *[Symbol.asyncIterator]() {\n      for (const value of values) {\n        yield value;\n      }\n    },\n  };\n}\n", "export function convertArrayToReadableStream<T>(\n  values: T[],\n): ReadableStream<T> {\n  return new ReadableStream({\n    start(controller) {\n      try {\n        for (const value of values) {\n          controller.enqueue(value);\n        }\n      } finally {\n        controller.close();\n      }\n    },\n  });\n}\n", "export async function convertAsyncIterableToArray<T>(\n  iterable: AsyncIterable<T>,\n): Promise<T[]> {\n  const result: T[] = [];\n  for await (const item of iterable) {\n    result.push(item);\n  }\n  return result;\n}\n", "export async function convertReadableStreamToArray<T>(\n  stream: ReadableStream<T>,\n): Promise<T[]> {\n  const reader = stream.getReader();\n  const result: T[] = [];\n\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) break;\n    result.push(value);\n  }\n\n  return result;\n}\n", "import { convertReadableStreamToArray } from './convert-readable-stream-to-array';\n\nexport async function convertResponseStreamToArray(\n  response: Response,\n): Promise<string[]> {\n  return convertReadableStreamToArray(\n    response.body!.pipeThrough(new TextDecoderStream()),\n  );\n}\n", "const POSITIONALS_EXP = /(%?)(%([sdijo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage =\n      positionals.length === 0 ? message : format(message, ...positionals)\n    let error: Error\n\n    try {\n      error = Reflect.construct(ErrorConstructor as CustomErrorConstructor, [\n        formatMessage,\n      ])\n    } catch (err) {\n      error = (ErrorConstructor as CustomErrorFactory)(formatMessage)\n    }\n\n    throw error\n  }\n}\n", "import { format } from 'outvariant'\n\nconst LIBRARY_PREFIX = '[MSW]'\n\n/**\n * Formats a given message by appending the library's prefix string.\n */\nfunction formatMessage(message: string, ...positionals: any[]): string {\n  const interpolatedMessage = format(message, ...positionals)\n  return `${LIBRARY_PREFIX} ${interpolatedMessage}`\n}\n\n/**\n * Prints a library-specific warning.\n */\nfunction warn(message: string, ...positionals: any[]): void {\n  console.warn(formatMessage(message, ...positionals))\n}\n\n/**\n * Prints a library-specific error.\n */\nfunction error(message: string, ...positionals: any[]): void {\n  console.error(formatMessage(message, ...positionals))\n}\n\nexport const devUtils = {\n  formatMessage,\n  warn,\n  error,\n}\n\n/**\n * Internal error instance.\n * Used to differentiate the library errors that must be forwarded\n * to the user from the unhandled exceptions. Use this if you don't\n * wish for the error to be coerced to a 500 fallback response.\n */\nexport class InternalError extends Error {\n  constructor(message: string) {\n    super(message)\n    this.name = 'InternalError'\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { devUtils } from './devUtils'\n\nexport function checkGlobals() {\n  /**\n   * MSW expects the \"URL\" constructor to be defined.\n   * It's not present in React Native so suggest a polyfill\n   * instead of failing silently.\n   * @see https://github.com/mswjs/msw/issues/1408\n   */\n  invariant(\n    typeof URL !== 'undefined',\n    devUtils.formatMessage(\n      `Global \"URL\" class is not defined. This likely means that you're running MSW in an environment that doesn't support all Node.js standard API (e.g. React Native). If that's the case, please use an appropriate polyfill for the \"URL\" class, like \"react-native-url-polyfill\".`,\n    ),\n  )\n}\n", "import type { Emitter } from './Emitter'\n\nexport class MemoryLeakError extends Error {\n  constructor(\n    public readonly emitter: Emitter<any>,\n    public readonly type: string | number | symbol,\n    public readonly count: number\n  ) {\n    super(\n      `Possible EventEmitter memory leak detected. ${count} ${type.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`\n    )\n    this.name = 'MaxListenersExceededWarning'\n  }\n}\n", "import { MemoryLeakError } from './MemoryLeakError'\n\nexport type EventMap = {\n  [eventName: string]: Array<unknown>\n}\n\nexport type InternalEventNames = 'newListener' | 'removeListener'\n\nexport type InternalListener<Events extends EventMap> = Listener<\n  [eventName: keyof Events, listener: Listener<Array<unknown>>]\n>\n\nexport type Listener<Data extends Array<unknown>> = (...data: Data) => void\n\n/**\n * Node.js-compatible implementation of `EventEmitter`.\n *\n * @example\n * const emitter = new Emitter<{ hello: [string] }>()\n * emitter.on('hello', (name) => console.log(name))\n * emitter.emit('hello', 'John')\n */\nexport class Emitter<Events extends EventMap> {\n  private events: Map<keyof Events, Array<Listener<any>>>\n  private maxListeners: number\n  private hasWarnedAboutPotentialMemoryLeak: boolean\n\n  static defaultMaxListeners = 10\n\n  static listenerCount<Events extends EventMap>(\n    emitter: Emitter<EventMap>,\n    eventName: keyof Events\n  ): number {\n    return emitter.listenerCount<any>(eventName)\n  }\n\n  constructor() {\n    this.events = new Map()\n    this.maxListeners = Emitter.defaultMaxListeners\n    this.hasWarnedAboutPotentialMemoryLeak = false\n  }\n\n  private _emitInternalEvent(\n    internalEventName: InternalEventNames,\n    eventName: keyof Events,\n    listener: Listener<Array<unknown>>\n  ): void {\n    this.emit(\n      internalEventName,\n      // Anything to make TypeScript happy.\n      ...([eventName, listener] as Events['newListener'] &\n        Events['removeListener'])\n    )\n  }\n\n  private _getListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Array<unknown>>> {\n    // Always return a copy of the listeners array\n    // so they are fixed at the time of the \"_getListeners\" call.\n    return Array.prototype.concat.apply([], this.events.get(eventName)) || []\n  }\n\n  private _removeListener<EventName extends keyof Events>(\n    listeners: Array<Listener<Events[EventName]>>,\n    listener: Listener<Events[EventName]>\n  ): Array<Listener<Events[EventName]>> {\n    const index = listeners.indexOf(listener)\n\n    if (index > -1) {\n      listeners.splice(index, 1)\n    }\n\n    return []\n  }\n\n  private _wrapOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): Listener<Events[EventName]> {\n    const onceListener = (...data: Events[keyof Events]) => {\n      this.removeListener(eventName, onceListener)\n\n      /**\n       * @note Return the result of the original listener.\n       * This way this wrapped preserves listeners that are async.\n       */\n      return listener.apply(this, data)\n    }\n\n    // Inherit the name of the original listener.\n    Object.defineProperty(onceListener, 'name', { value: listener.name })\n\n    return onceListener\n  }\n\n  public setMaxListeners(maxListeners: number): this {\n    this.maxListeners = maxListeners\n    return this\n  }\n\n  /**\n   * Returns the current max listener value for the `Emitter` which is\n   * either set by `emitter.setMaxListeners(n)` or defaults to\n   * `Emitter.defaultMaxListeners`.\n   */\n  public getMaxListeners(): number {\n    return this.maxListeners\n  }\n\n  /**\n   * Returns an array listing the events for which the emitter has registered listeners.\n   * The values in the array will be strings or Symbols.\n   */\n  public eventNames(): Array<keyof Events> {\n    return Array.from(this.events.keys())\n  }\n\n  /**\n   * Synchronously calls each of the listeners registered for the event named `eventName`,\n   * in the order they were registered, passing the supplied arguments to each.\n   * Returns `true` if the event has listeners, `false` otherwise.\n   *\n   * @example\n   * const emitter = new Emitter<{ hello: [string] }>()\n   * emitter.emit('hello', 'John')\n   */\n  public emit<EventName extends keyof Events>(\n    eventName: EventName,\n    ...data: Events[EventName]\n  ): boolean {\n    const listeners = this._getListeners(eventName)\n    listeners.forEach((listener) => {\n      listener.apply(this, data)\n    })\n\n    return listeners.length > 0\n  }\n\n  public addListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public addListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public addListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: InternalListener<Events> | Listener<Events[any]>\n  ): this {\n    // Emit the `newListener` event before adding the listener.\n    this._emitInternalEvent('newListener', eventName, listener)\n\n    const nextListeners = this._getListeners(eventName).concat(listener)\n    this.events.set(eventName, nextListeners)\n\n    if (\n      this.maxListeners > 0 &&\n      this.listenerCount(eventName) > this.maxListeners &&\n      !this.hasWarnedAboutPotentialMemoryLeak\n    ) {\n      this.hasWarnedAboutPotentialMemoryLeak = true\n\n      const memoryLeakWarning = new MemoryLeakError(\n        this,\n        eventName,\n        this.listenerCount(eventName)\n      )\n      console.warn(memoryLeakWarning)\n    }\n\n    return this\n  }\n\n  public on(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: 'removeListener' | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(eventName, listener)\n  }\n\n  public once(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: InternalEventNames | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public prependListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      const nextListeners = [listener].concat(listeners)\n      this.events.set(eventName, nextListeners)\n    } else {\n      this.events.set(eventName, listeners.concat(listener))\n    }\n\n    return this\n  }\n\n  public prependOnceListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependOnceListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.prependListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public removeListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public removeListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public removeListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      this._removeListener(listeners, listener)\n      this.events.set(eventName, listeners)\n\n      // Emit the `removeListener` event after removing the listener.\n      this._emitInternalEvent('removeListener', eventName, listener)\n    }\n\n    return this\n  }\n\n  public off(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public off<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  /**\n   * Alias for `emitter.removeListener()`.\n   *\n   * @example\n   * emitter.off('hello', listener)\n   */\n  public off(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.removeListener(eventName, listener)\n  }\n\n  public removeAllListeners(eventName?: InternalEventNames): this\n  public removeAllListeners<EventName extends keyof Events>(\n    eventName?: EventName\n  ): this\n  public removeAllListeners(\n    eventName?: InternalEventNames | keyof Events\n  ): this {\n    if (eventName) {\n      this.events.delete(eventName)\n    } else {\n      this.events.clear()\n    }\n\n    return this\n  }\n\n  public listeners(eventName: InternalEventNames): Array<Listener<any>>\n  public listeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>>\n  /**\n   * Returns a copy of the array of listeners for the event named `eventName`.\n   */\n  public listeners(eventName: InternalEventNames | keyof Events) {\n    return Array.from(this._getListeners(eventName))\n  }\n\n  public listenerCount(eventName: InternalEventNames): number\n  public listenerCount<EventName extends keyof Events>(\n    eventName: EventName\n  ): number\n  /**\n   * Returns the number of listeners listening to the event named `eventName`.\n   */\n  public listenerCount(eventName: InternalEventNames | keyof Events): number {\n    return this._getListeners(eventName).length\n  }\n\n  public rawListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>> {\n    return this.listeners(eventName)\n  }\n}\n", "import { Emitter, EventMap } from 'strict-event-emitter'\n\n/**\n * Pipes all emitted events from one emitter to another.\n */\nexport function pipeEvents<Events extends EventMap>(\n  source: Emitter<Events>,\n  destination: Emitter<Events>,\n): void {\n  const rawEmit: typeof source.emit & { _isPiped?: boolean } = source.emit\n\n  if (rawEmit._isPiped) {\n    return\n  }\n\n  const sourceEmit: typeof source.emit & { _isPiped?: boolean } =\n    function sourceEmit(this: typeof source, event, ...data) {\n      destination.emit(event, ...data)\n      return rawEmit.call(this, event, ...data)\n    }\n\n  sourceEmit._isPiped = true\n\n  source.emit = sourceEmit\n}\n", "/**\n * Creates an immutable copy of the given array.\n */\nexport function toReadonlyArray<T>(source: Array<T>): ReadonlyArray<T> {\n  const clone = [...source] as Array<T>\n  Object.freeze(clone)\n  return clone\n}\n", "export type DisposableSubscription = () => void\n\nexport class Disposable {\n  protected subscriptions: Array<DisposableSubscription> = []\n\n  public dispose() {\n    let subscription: DisposableSubscription | undefined\n    while ((subscription = this.subscriptions.shift())) {\n      subscription()\n    }\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { EventMap, Emitter } from 'strict-event-emitter'\nimport {\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n} from './handlers/RequestHandler'\nimport { LifeCycleEventEmitter } from './sharedOptions'\nimport { devUtils } from './utils/internal/devUtils'\nimport { pipeEvents } from './utils/internal/pipeEvents'\nimport { toReadonlyArray } from './utils/internal/toReadonlyArray'\nimport { Disposable } from './utils/internal/Disposable'\n\nexport abstract class HandlersController {\n  abstract prepend(runtimeHandlers: Array<RequestHandler>): void\n  abstract reset(nextHandles: Array<RequestHandler>): void\n  abstract currentHandlers(): Array<RequestHandler>\n}\n\nexport class InMemoryHandlersController implements HandlersController {\n  private handlers: Array<RequestHandler>\n\n  constructor(private initialHandlers: Array<RequestHandler>) {\n    this.handlers = [...initialHandlers]\n  }\n\n  public prepend(runtimeHandles: Array<RequestHandler>): void {\n    this.handlers.unshift(...runtimeHandles)\n  }\n\n  public reset(nextHandlers: Array<RequestHandler>): void {\n    this.handlers =\n      nextHandlers.length > 0 ? [...nextHandlers] : [...this.initialHandlers]\n  }\n\n  public currentHandlers(): Array<RequestHandler> {\n    return this.handlers\n  }\n}\n\n/**\n * Generic class for the mock API setup.\n */\nexport abstract class SetupApi<EventsMap extends EventMap> extends Disposable {\n  protected handlersController: HandlersController\n  protected readonly emitter: Emitter<EventsMap>\n  protected readonly publicEmitter: Emitter<EventsMap>\n\n  public readonly events: LifeCycleEventEmitter<EventsMap>\n\n  constructor(...initialHandlers: Array<RequestHandler>) {\n    super()\n\n    invariant(\n      this.validateHandlers(initialHandlers),\n      devUtils.formatMessage(\n        `Failed to apply given request handlers: invalid input. Did you forget to spread the request handlers Array?`,\n      ),\n    )\n\n    this.handlersController = new InMemoryHandlersController(initialHandlers)\n\n    this.emitter = new Emitter<EventsMap>()\n    this.publicEmitter = new Emitter<EventsMap>()\n    pipeEvents(this.emitter, this.publicEmitter)\n\n    this.events = this.createLifeCycleEvents()\n\n    this.subscriptions.push(() => {\n      this.emitter.removeAllListeners()\n      this.publicEmitter.removeAllListeners()\n    })\n  }\n\n  private validateHandlers(handlers: ReadonlyArray<RequestHandler>): boolean {\n    // Guard against incorrect call signature of the setup API.\n    return handlers.every((handler) => !Array.isArray(handler))\n  }\n\n  public use(...runtimeHandlers: Array<RequestHandler>): void {\n    invariant(\n      this.validateHandlers(runtimeHandlers),\n      devUtils.formatMessage(\n        `Failed to call \"use()\" with the given request handlers: invalid input. Did you forget to spread the array of request handlers?`,\n      ),\n    )\n\n    this.handlersController.prepend(runtimeHandlers)\n  }\n\n  public restoreHandlers(): void {\n    this.handlersController.currentHandlers().forEach((handler) => {\n      handler.isUsed = false\n    })\n  }\n\n  public resetHandlers(...nextHandlers: Array<RequestHandler>): void {\n    this.handlersController.reset(nextHandlers)\n  }\n\n  public listHandlers(): ReadonlyArray<\n    RequestHandler<RequestHandlerDefaultInfo, any, any>\n  > {\n    return toReadonlyArray(this.handlersController.currentHandlers())\n  }\n\n  private createLifeCycleEvents(): LifeCycleEventEmitter<EventsMap> {\n    return {\n      on: (...args: any[]) => {\n        return (this.publicEmitter.on as any)(...args)\n      },\n      removeListener: (...args: any[]) => {\n        return (this.publicEmitter.removeListener as any)(...args)\n      },\n      removeAllListeners: (...args: any[]) => {\n        return this.publicEmitter.removeAllListeners(...args)\n      },\n    }\n  }\n}\n", "// Ignore the source files traces for local testing.\nconst SOURCE_FRAME = /[\\/\\\\]msw[\\/\\\\]src[\\/\\\\](.+)/\n\nconst BUILD_FRAME =\n  /(node_modules)?[\\/\\\\]lib[\\/\\\\](core|browser|node|native|iife)[\\/\\\\]|^[^\\/\\\\]*$/\n\n/**\n * Return the stack trace frame of a function's invocation.\n */\nexport function getCallFrame(error: Error) {\n  // In <IE11, new Error may return an undefined stack\n  const stack = error.stack\n\n  if (!stack) {\n    return\n  }\n\n  const frames: string[] = stack.split('\\n').slice(1)\n\n  // Get the first frame that doesn't reference the library's internal trace.\n  // Assume that frame is the invocation frame.\n  const declarationFrame = frames.find((frame) => {\n    return !(SOURCE_FRAME.test(frame) || BUILD_FRAME.test(frame))\n  })\n\n  if (!declarationFrame) {\n    return\n  }\n\n  // Extract file reference from the stack frame.\n  const declarationPath = declarationFrame\n    .replace(/\\s*at [^()]*\\(([^)]+)\\)/, '$1')\n    .replace(/^@/, '')\n  return declarationPath\n}\n", "/**\n * Determines if the given function is an iterator.\n */\nexport function isIterable<IteratorType>(\n  fn: any,\n): fn is Generator<IteratorType, IteratorType, IteratorType> {\n  if (!fn) {\n    return false\n  }\n\n  return typeof (fn as Generator<unknown>)[Symbol.iterator] == 'function'\n}\n", "import { invariant } from 'outvariant'\nimport { getCallFrame } from '../utils/internal/getCallFrame'\nimport { isIterable } from '../utils/internal/isIterable'\nimport type { ResponseResolutionContext } from '../utils/executeHandlers'\nimport type { MaybePromise } from '../typeUtils'\nimport { StrictRequest, StrictResponse } from '..//HttpResponse'\n\nexport type DefaultRequestMultipartBody = Record<\n  string,\n  string | File | Array<string | File>\n>\n\nexport type DefaultBodyType =\n  | Record<string, any>\n  | DefaultRequestMultipartBody\n  | string\n  | number\n  | boolean\n  | null\n  | undefined\n\nexport type JsonBodyType =\n  | Record<string, any>\n  | string\n  | number\n  | boolean\n  | null\n  | undefined\n\nexport interface RequestHandlerDefaultInfo {\n  header: string\n}\n\nexport interface RequestHandlerInternalInfo {\n  callFrame?: string\n}\n\nexport type ResponseResolverReturnType<\n  ResponseBodyType extends DefaultBodyType = undefined,\n> =\n  | ([ResponseBodyType] extends [undefined]\n      ? Response\n      : StrictResponse<ResponseBodyType>)\n  | undefined\n  | void\n\nexport type MaybeAsyncResponseResolverReturnType<\n  ResponseBodyType extends DefaultBodyType,\n> = MaybePromise<ResponseResolverReturnType<ResponseBodyType>>\n\nexport type AsyncResponseResolverReturnType<\n  ResponseBodyType extends DefaultBodyType,\n> = MaybePromise<\n  | ResponseResolverReturnType<ResponseBodyType>\n  | Generator<\n      MaybeAsyncResponseResolverReturnType<ResponseBodyType>,\n      MaybeAsyncResponseResolverReturnType<ResponseBodyType>,\n      MaybeAsyncResponseResolverReturnType<ResponseBodyType>\n    >\n>\n\nexport type ResponseResolverInfo<\n  ResolverExtraInfo extends Record<string, unknown>,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n> = {\n  request: StrictRequest<RequestBodyType>\n  requestId: string\n} & ResolverExtraInfo\n\nexport type ResponseResolver<\n  ResolverExtraInfo extends Record<string, unknown> = Record<string, unknown>,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  ResponseBodyType extends DefaultBodyType = undefined,\n> = (\n  info: ResponseResolverInfo<ResolverExtraInfo, RequestBodyType>,\n) => AsyncResponseResolverReturnType<ResponseBodyType>\n\nexport interface RequestHandlerArgs<\n  HandlerInfo,\n  HandlerOptions extends RequestHandlerOptions,\n> {\n  info: HandlerInfo\n  resolver: ResponseResolver<any>\n  options?: HandlerOptions\n}\n\nexport interface RequestHandlerOptions {\n  once?: boolean\n}\n\nexport interface RequestHandlerExecutionResult<\n  ParsedResult extends Record<string, unknown> | undefined,\n> {\n  handler: RequestHandler\n  parsedResult?: ParsedResult\n  request: Request\n  requestId: string\n  response?: Response\n}\n\nexport abstract class RequestHandler<\n  HandlerInfo extends RequestHandlerDefaultInfo = RequestHandlerDefaultInfo,\n  ParsedResult extends Record<string, any> | undefined = any,\n  ResolverExtras extends Record<string, unknown> = any,\n  HandlerOptions extends RequestHandlerOptions = RequestHandlerOptions,\n> {\n  static cache = new WeakMap<\n    StrictRequest<DefaultBodyType>,\n    StrictRequest<DefaultBodyType>\n  >()\n\n  public info: HandlerInfo & RequestHandlerInternalInfo\n  /**\n   * Indicates whether this request handler has been used\n   * (its resolver has successfully executed).\n   */\n  public isUsed: boolean\n\n  protected resolver: ResponseResolver<ResolverExtras, any, any>\n  private resolverGenerator?: Generator<\n    MaybeAsyncResponseResolverReturnType<any>,\n    MaybeAsyncResponseResolverReturnType<any>,\n    MaybeAsyncResponseResolverReturnType<any>\n  >\n  private resolverGeneratorResult?: Response | StrictResponse<any>\n  private options?: HandlerOptions\n\n  constructor(args: RequestHandlerArgs<HandlerInfo, HandlerOptions>) {\n    this.resolver = args.resolver\n    this.options = args.options\n\n    const callFrame = getCallFrame(new Error())\n\n    this.info = {\n      ...args.info,\n      callFrame,\n    }\n\n    this.isUsed = false\n  }\n\n  /**\n   * Determine if the intercepted request should be mocked.\n   */\n  abstract predicate(args: {\n    request: Request\n    parsedResult: ParsedResult\n    resolutionContext?: ResponseResolutionContext\n  }): boolean\n\n  /**\n   * Print out the successfully handled request.\n   */\n  abstract log(args: {\n    request: Request\n    response: Response\n    parsedResult: ParsedResult\n  }): void\n\n  /**\n   * Parse the intercepted request to extract additional information from it.\n   * Parsed result is then exposed to other methods of this request handler.\n   */\n  async parse(_args: {\n    request: Request\n    resolutionContext?: ResponseResolutionContext\n  }): Promise<ParsedResult> {\n    return {} as ParsedResult\n  }\n\n  /**\n   * Test if this handler matches the given request.\n   *\n   * This method is not used internally but is exposed\n   * as a convenience method for consumers writing custom\n   * handlers.\n   */\n  public async test(args: {\n    request: Request\n    resolutionContext?: ResponseResolutionContext\n  }): Promise<boolean> {\n    const parsedResult = await this.parse({\n      request: args.request,\n      resolutionContext: args.resolutionContext,\n    })\n\n    return this.predicate({\n      request: args.request,\n      parsedResult,\n      resolutionContext: args.resolutionContext,\n    })\n  }\n\n  protected extendResolverArgs(_args: {\n    request: Request\n    parsedResult: ParsedResult\n  }): ResolverExtras {\n    return {} as ResolverExtras\n  }\n\n  // Clone the request instance before it's passed to the handler phases\n  // and the response resolver so we can always read it for logging.\n  // We only clone it once per request to avoid unnecessary overhead.\n  private cloneRequestOrGetFromCache(\n    request: StrictRequest<DefaultBodyType>,\n  ): StrictRequest<DefaultBodyType> {\n    const existingClone = RequestHandler.cache.get(request)\n\n    if (typeof existingClone !== 'undefined') {\n      return existingClone\n    }\n\n    const clonedRequest = request.clone()\n    RequestHandler.cache.set(request, clonedRequest)\n\n    return clonedRequest\n  }\n\n  /**\n   * Execute this request handler and produce a mocked response\n   * using the given resolver function.\n   */\n  public async run(args: {\n    request: StrictRequest<any>\n    requestId: string\n    resolutionContext?: ResponseResolutionContext\n  }): Promise<RequestHandlerExecutionResult<ParsedResult> | null> {\n    if (this.isUsed && this.options?.once) {\n      return null\n    }\n\n    // Clone the request.\n    // If this is the first time MSW handles this request, a fresh clone\n    // will be created and cached. Upon further handling of the same request,\n    // the request clone from the cache will be reused to prevent abundant\n    // \"abort\" listeners and save up resources on cloning.\n    const requestClone = this.cloneRequestOrGetFromCache(args.request)\n\n    const parsedResult = await this.parse({\n      request: args.request,\n      resolutionContext: args.resolutionContext,\n    })\n    const shouldInterceptRequest = this.predicate({\n      request: args.request,\n      parsedResult,\n      resolutionContext: args.resolutionContext,\n    })\n\n    if (!shouldInterceptRequest) {\n      return null\n    }\n\n    // Re-check isUsed, in case another request hit this handler while we were\n    // asynchronously parsing the request.\n    if (this.isUsed && this.options?.once) {\n      return null\n    }\n\n    this.isUsed = true\n\n    // Create a response extraction wrapper around the resolver\n    // since it can be both an async function and a generator.\n    const executeResolver = this.wrapResolver(this.resolver)\n\n    const resolverExtras = this.extendResolverArgs({\n      request: args.request,\n      parsedResult,\n    })\n\n    const mockedResponsePromise = (\n      executeResolver({\n        ...resolverExtras,\n        requestId: args.requestId,\n        request: args.request,\n      }) as Promise<Response>\n    ).catch((errorOrResponse) => {\n      // Allow throwing a Response instance in a response resolver.\n      if (errorOrResponse instanceof Response) {\n        return errorOrResponse\n      }\n\n      // Otherwise, throw the error as-is.\n      throw errorOrResponse\n    })\n\n    const mockedResponse = await mockedResponsePromise\n\n    const executionResult = this.createExecutionResult({\n      // Pass the cloned request to the result so that logging\n      // and other consumers could read its body once more.\n      request: requestClone,\n      requestId: args.requestId,\n      response: mockedResponse,\n      parsedResult,\n    })\n\n    return executionResult\n  }\n\n  private wrapResolver(\n    resolver: ResponseResolver<ResolverExtras>,\n  ): ResponseResolver<ResolverExtras> {\n    return async (info): Promise<ResponseResolverReturnType<any>> => {\n      const result = this.resolverGenerator || (await resolver(info))\n\n      if (isIterable<AsyncResponseResolverReturnType<any>>(result)) {\n        // Immediately mark this handler as unused.\n        // Only when the generator is done, the handler will be\n        // considered used.\n        this.isUsed = false\n\n        const { value, done } = result[Symbol.iterator]().next()\n        const nextResponse = await value\n\n        if (done) {\n          this.isUsed = true\n        }\n\n        // If the generator is done and there is no next value,\n        // return the previous generator's value.\n        if (!nextResponse && done) {\n          invariant(\n            this.resolverGeneratorResult,\n            'Failed to returned a previously stored generator response: the value is not a valid Response.',\n          )\n\n          // Clone the previously stored response from the generator\n          // so that it could be read again.\n          return this.resolverGeneratorResult.clone() as StrictResponse<any>\n        }\n\n        if (!this.resolverGenerator) {\n          this.resolverGenerator = result\n        }\n\n        if (nextResponse) {\n          // Also clone the response before storing it\n          // so it could be read again.\n          this.resolverGeneratorResult = nextResponse?.clone()\n        }\n\n        return nextResponse\n      }\n\n      return result\n    }\n  }\n\n  private createExecutionResult(args: {\n    request: Request\n    requestId: string\n    parsedResult: ParsedResult\n    response?: Response\n  }): RequestHandlerExecutionResult<ParsedResult> {\n    return {\n      handler: this,\n      request: args.request,\n      requestId: args.requestId,\n      response: args.response,\n      parsedResult: args.parsedResult,\n    }\n  }\n}\n", "/**\n * Performs a case-insensitive comparison of two given strings.\n */\nexport function isStringEqual(actual: string, expected: string): boolean {\n  return actual.toLowerCase() === expected.toLowerCase()\n}\n", "export enum StatusCodeColor {\n  Success = '#69AB32',\n  Warning = '#F0BB4B',\n  Danger = '#E95F5D',\n}\n\n/**\n * Returns a HEX color for a given response status code number.\n */\nexport function getStatusCodeColor(status: number): StatusCodeColor {\n  if (status < 300) {\n    return StatusCodeColor.Success\n  }\n\n  if (status < 400) {\n    return StatusCodeColor.Warning\n  }\n\n  return StatusCodeColor.Danger\n}\n", "/**\n * Returns a timestamp string in a \"HH:MM:SS\" format.\n */\nexport function getTimestamp(): string {\n  const now = new Date()\n\n  return [now.getHours(), now.getMinutes(), now.getSeconds()]\n    .map(String)\n    .map((chunk) => chunk.slice(0, 2))\n    .map((chunk) => chunk.padStart(2, '0'))\n    .join(':')\n}\n", "export interface LoggedRequest {\n  url: URL\n  method: string\n  headers: Record<string, string>\n  body: string\n}\n\n/**\n * Formats a mocked request for introspection in browser's console.\n */\nexport async function serializeRequest(\n  request: Request,\n): Promise<LoggedRequest> {\n  const requestClone = request.clone()\n  const requestText = await requestClone.text()\n\n  return {\n    url: new URL(request.url),\n    method: request.method,\n    headers: Object.fromEntries(request.headers.entries()),\n    body: requestText,\n  }\n}\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// node_modules/statuses/codes.json\nvar require_codes = __commonJS({\n  \"node_modules/statuses/codes.json\"(exports, module) {\n    module.exports = {\n      \"100\": \"Continue\",\n      \"101\": \"Switching Protocols\",\n      \"102\": \"Processing\",\n      \"103\": \"Early Hints\",\n      \"200\": \"OK\",\n      \"201\": \"Created\",\n      \"202\": \"Accepted\",\n      \"203\": \"Non-Authoritative Information\",\n      \"204\": \"No Content\",\n      \"205\": \"Reset Content\",\n      \"206\": \"Partial Content\",\n      \"207\": \"Multi-Status\",\n      \"208\": \"Already Reported\",\n      \"226\": \"IM Used\",\n      \"300\": \"Multiple Choices\",\n      \"301\": \"Moved Permanently\",\n      \"302\": \"Found\",\n      \"303\": \"See Other\",\n      \"304\": \"Not Modified\",\n      \"305\": \"Use Proxy\",\n      \"307\": \"Temporary Redirect\",\n      \"308\": \"Permanent Redirect\",\n      \"400\": \"Bad Request\",\n      \"401\": \"Unauthorized\",\n      \"402\": \"Payment Required\",\n      \"403\": \"Forbidden\",\n      \"404\": \"Not Found\",\n      \"405\": \"Method Not Allowed\",\n      \"406\": \"Not Acceptable\",\n      \"407\": \"Proxy Authentication Required\",\n      \"408\": \"Request Timeout\",\n      \"409\": \"Conflict\",\n      \"410\": \"Gone\",\n      \"411\": \"Length Required\",\n      \"412\": \"Precondition Failed\",\n      \"413\": \"Payload Too Large\",\n      \"414\": \"URI Too Long\",\n      \"415\": \"Unsupported Media Type\",\n      \"416\": \"Range Not Satisfiable\",\n      \"417\": \"Expectation Failed\",\n      \"418\": \"I'm a Teapot\",\n      \"421\": \"Misdirected Request\",\n      \"422\": \"Unprocessable Entity\",\n      \"423\": \"Locked\",\n      \"424\": \"Failed Dependency\",\n      \"425\": \"Too Early\",\n      \"426\": \"Upgrade Required\",\n      \"428\": \"Precondition Required\",\n      \"429\": \"Too Many Requests\",\n      \"431\": \"Request Header Fields Too Large\",\n      \"451\": \"Unavailable For Legal Reasons\",\n      \"500\": \"Internal Server Error\",\n      \"501\": \"Not Implemented\",\n      \"502\": \"Bad Gateway\",\n      \"503\": \"Service Unavailable\",\n      \"504\": \"Gateway Timeout\",\n      \"505\": \"HTTP Version Not Supported\",\n      \"506\": \"Variant Also Negotiates\",\n      \"507\": \"Insufficient Storage\",\n      \"508\": \"Loop Detected\",\n      \"509\": \"Bandwidth Limit Exceeded\",\n      \"510\": \"Not Extended\",\n      \"511\": \"Network Authentication Required\"\n    };\n  }\n});\n\n// node_modules/statuses/index.js\nvar require_statuses = __commonJS({\n  \"node_modules/statuses/index.js\"(exports, module) {\n    \"use strict\";\n    var codes = require_codes();\n    module.exports = status2;\n    status2.message = codes;\n    status2.code = createMessageToStatusCodeMap(codes);\n    status2.codes = createStatusCodeList(codes);\n    status2.redirect = {\n      300: true,\n      301: true,\n      302: true,\n      303: true,\n      305: true,\n      307: true,\n      308: true\n    };\n    status2.empty = {\n      204: true,\n      205: true,\n      304: true\n    };\n    status2.retry = {\n      502: true,\n      503: true,\n      504: true\n    };\n    function createMessageToStatusCodeMap(codes2) {\n      var map = {};\n      Object.keys(codes2).forEach(function forEachCode(code) {\n        var message = codes2[code];\n        var status3 = Number(code);\n        map[message.toLowerCase()] = status3;\n      });\n      return map;\n    }\n    function createStatusCodeList(codes2) {\n      return Object.keys(codes2).map(function mapCode(code) {\n        return Number(code);\n      });\n    }\n    function getStatusCode(message) {\n      var msg = message.toLowerCase();\n      if (!Object.prototype.hasOwnProperty.call(status2.code, msg)) {\n        throw new Error('invalid status message: \"' + message + '\"');\n      }\n      return status2.code[msg];\n    }\n    function getStatusMessage(code) {\n      if (!Object.prototype.hasOwnProperty.call(status2.message, code)) {\n        throw new Error(\"invalid status code: \" + code);\n      }\n      return status2.message[code];\n    }\n    function status2(code) {\n      if (typeof code === \"number\") {\n        return getStatusMessage(code);\n      }\n      if (typeof code !== \"string\") {\n        throw new TypeError(\"code must be a number or string\");\n      }\n      var n = parseInt(code, 10);\n      if (!isNaN(n)) {\n        return getStatusMessage(n);\n      }\n      return getStatusCode(code);\n    }\n  }\n});\n\n// source.js\nvar import_statuses = __toESM(require_statuses(), 1);\nvar source_default = import_statuses.default;\nexport {\n  source_default as default\n};\n/*! Bundled license information:\n\nstatuses/index.js:\n  (*!\n   * statuses\n   * Copyright(c) 2014 Jonathan Ong\n   * Copyright(c) 2016 Douglas Christopher Wilson\n   * MIT Licensed\n   *)\n*/\n", "import statuses from '@bundled-es-modules/statuses'\n\nconst { message } = statuses\n\nexport interface SerializedResponse {\n  status: number\n  statusText: string\n  headers: Record<string, any>\n  body: string\n}\n\nexport async function serializeResponse(\n  response: Response,\n): Promise<SerializedResponse> {\n  const responseClone = response.clone()\n  const responseText = await responseClone.text()\n\n  // Normalize the response status and status text when logging\n  // since the default Response instance doesn't infer status texts\n  // from status codes. This has no effect on the actual response instance.\n  const responseStatus = responseClone.status || 200\n  const responseStatusText =\n    responseClone.statusText || message[responseStatus] || 'OK'\n\n  return {\n    status: responseStatus,\n    statusText: responseStatusText,\n    headers: Object.fromEntries(responseClone.headers.entries()),\n    body: responseText,\n  }\n}\n", "/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type:\n    | \"OPEN\"\n    | \"CLOSE\"\n    | \"PATTERN\"\n    | \"NAME\"\n    | \"CHAR\"\n    | \"ESCAPED_CHAR\"\n    | \"MODIFIER\"\n    | \"END\";\n  index: number;\n  value: string;\n}\n\n/**\n * Tokenize input string.\n */\nfunction lexer(str: string): LexToken[] {\n  const tokens: LexToken[] = [];\n  let i = 0;\n\n  while (i < str.length) {\n    const char = str[i];\n\n    if (char === \"*\" || char === \"+\" || char === \"?\") {\n      tokens.push({ type: \"MODIFIER\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"\\\\\") {\n      tokens.push({ type: \"ESCAPED_CHAR\", index: i++, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"{\") {\n      tokens.push({ type: \"OPEN\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"}\") {\n      tokens.push({ type: \"CLOSE\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \":\") {\n      let name = \"\";\n      let j = i + 1;\n\n      while (j < str.length) {\n        const code = str.charCodeAt(j);\n\n        if (\n          // `0-9`\n          (code >= 48 && code <= 57) ||\n          // `A-Z`\n          (code >= 65 && code <= 90) ||\n          // `a-z`\n          (code >= 97 && code <= 122) ||\n          // `_`\n          code === 95\n        ) {\n          name += str[j++];\n          continue;\n        }\n\n        break;\n      }\n\n      if (!name) throw new TypeError(`Missing parameter name at ${i}`);\n\n      tokens.push({ type: \"NAME\", index: i, value: name });\n      i = j;\n      continue;\n    }\n\n    if (char === \"(\") {\n      let count = 1;\n      let pattern = \"\";\n      let j = i + 1;\n\n      if (str[j] === \"?\") {\n        throw new TypeError(`Pattern cannot start with \"?\" at ${j}`);\n      }\n\n      while (j < str.length) {\n        if (str[j] === \"\\\\\") {\n          pattern += str[j++] + str[j++];\n          continue;\n        }\n\n        if (str[j] === \")\") {\n          count--;\n          if (count === 0) {\n            j++;\n            break;\n          }\n        } else if (str[j] === \"(\") {\n          count++;\n          if (str[j + 1] !== \"?\") {\n            throw new TypeError(`Capturing groups are not allowed at ${j}`);\n          }\n        }\n\n        pattern += str[j++];\n      }\n\n      if (count) throw new TypeError(`Unbalanced pattern at ${i}`);\n      if (!pattern) throw new TypeError(`Missing pattern at ${i}`);\n\n      tokens.push({ type: \"PATTERN\", index: i, value: pattern });\n      i = j;\n      continue;\n    }\n\n    tokens.push({ type: \"CHAR\", index: i, value: str[i++] });\n  }\n\n  tokens.push({ type: \"END\", index: i, value: \"\" });\n\n  return tokens;\n}\n\nexport interface ParseOptions {\n  /**\n   * Set the default delimiter for repeat parameters. (default: `'/'`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters to automatically consider prefixes when parsing.\n   */\n  prefixes?: string;\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): Token[] {\n  const tokens = lexer(str);\n  const { prefixes = \"./\" } = options;\n  const defaultPattern = `[^${escapeString(options.delimiter || \"/#?\")}]+?`;\n  const result: Token[] = [];\n  let key = 0;\n  let i = 0;\n  let path = \"\";\n\n  const tryConsume = (type: LexToken[\"type\"]): string | undefined => {\n    if (i < tokens.length && tokens[i].type === type) return tokens[i++].value;\n  };\n\n  const mustConsume = (type: LexToken[\"type\"]): string => {\n    const value = tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = tokens[i];\n    throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}`);\n  };\n\n  const consumeText = (): string => {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = tryConsume(\"CHAR\") || tryConsume(\"ESCAPED_CHAR\"))) {\n      result += value;\n    }\n    return result;\n  };\n\n  while (i < tokens.length) {\n    const char = tryConsume(\"CHAR\");\n    const name = tryConsume(\"NAME\");\n    const pattern = tryConsume(\"PATTERN\");\n\n    if (name || pattern) {\n      let prefix = char || \"\";\n\n      if (prefixes.indexOf(prefix) === -1) {\n        path += prefix;\n        prefix = \"\";\n      }\n\n      if (path) {\n        result.push(path);\n        path = \"\";\n      }\n\n      result.push({\n        name: name || key++,\n        prefix,\n        suffix: \"\",\n        pattern: pattern || defaultPattern,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    const value = char || tryConsume(\"ESCAPED_CHAR\");\n    if (value) {\n      path += value;\n      continue;\n    }\n\n    if (path) {\n      result.push(path);\n      path = \"\";\n    }\n\n    const open = tryConsume(\"OPEN\");\n    if (open) {\n      const prefix = consumeText();\n      const name = tryConsume(\"NAME\") || \"\";\n      const pattern = tryConsume(\"PATTERN\") || \"\";\n      const suffix = consumeText();\n\n      mustConsume(\"CLOSE\");\n\n      result.push({\n        name: name || (pattern ? key++ : \"\"),\n        pattern: name && !pattern ? defaultPattern : pattern,\n        prefix,\n        suffix,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    mustConsume(\"END\");\n  }\n\n  return result;\n}\n\nexport interface TokensToFunctionOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * Function for encoding input strings for output.\n   */\n  encode?: (value: string, token: Key) => string;\n  /**\n   * When `false` the function can produce an invalid (unmatched) path. (default: `true`)\n   */\n  validate?: boolean;\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends object = object>(\n  str: string,\n  options?: ParseOptions & TokensToFunctionOptions,\n) {\n  return tokensToFunction<P>(parse(str, options), options);\n}\n\nexport type PathFunction<P extends object = object> = (data?: P) => string;\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nexport function tokensToFunction<P extends object = object>(\n  tokens: Token[],\n  options: TokensToFunctionOptions = {},\n): PathFunction<P> {\n  const reFlags = flags(options);\n  const { encode = (x: string) => x, validate = true } = options;\n\n  // Compile all the tokens into regexps.\n  const matches = tokens.map((token) => {\n    if (typeof token === \"object\") {\n      return new RegExp(`^(?:${token.pattern})$`, reFlags);\n    }\n  });\n\n  return (data: Record<string, any> | null | undefined) => {\n    let path = \"\";\n\n    for (let i = 0; i < tokens.length; i++) {\n      const token = tokens[i];\n\n      if (typeof token === \"string\") {\n        path += token;\n        continue;\n      }\n\n      const value = data ? data[token.name] : undefined;\n      const optional = token.modifier === \"?\" || token.modifier === \"*\";\n      const repeat = token.modifier === \"*\" || token.modifier === \"+\";\n\n      if (Array.isArray(value)) {\n        if (!repeat) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to not repeat, but got an array`,\n          );\n        }\n\n        if (value.length === 0) {\n          if (optional) continue;\n\n          throw new TypeError(`Expected \"${token.name}\" to not be empty`);\n        }\n\n        for (let j = 0; j < value.length; j++) {\n          const segment = encode(value[j], token);\n\n          if (validate && !(matches[i] as RegExp).test(segment)) {\n            throw new TypeError(\n              `Expected all \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`,\n            );\n          }\n\n          path += token.prefix + segment + token.suffix;\n        }\n\n        continue;\n      }\n\n      if (typeof value === \"string\" || typeof value === \"number\") {\n        const segment = encode(String(value), token);\n\n        if (validate && !(matches[i] as RegExp).test(segment)) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`,\n          );\n        }\n\n        path += token.prefix + segment + token.suffix;\n        continue;\n      }\n\n      if (optional) continue;\n\n      const typeOfMessage = repeat ? \"an array\" : \"a string\";\n      throw new TypeError(`Expected \"${token.name}\" to be ${typeOfMessage}`);\n    }\n\n    return path;\n  };\n}\n\nexport interface RegexpToFunctionOptions {\n  /**\n   * Function for decoding strings for params.\n   */\n  decode?: (value: string, token: Key) => string;\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends object = object> {\n  path: string;\n  index: number;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends object = object> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends object = object> = (\n  path: string,\n) => Match<P>;\n\n/**\n * Create path match function from `path-to-regexp` spec.\n */\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n) {\n  const keys: Key[] = [];\n  const re = pathToRegexp(str, keys, options);\n  return regexpToFunction<P>(re, keys, options);\n}\n\n/**\n * Create a path match function from `path-to-regexp` output.\n */\nexport function regexpToFunction<P extends object = object>(\n  re: RegExp,\n  keys: Key[],\n  options: RegexpToFunctionOptions = {},\n): MatchFunction<P> {\n  const { decode = (x: string) => x } = options;\n\n  return function (pathname: string) {\n    const m = re.exec(pathname);\n    if (!m) return false;\n\n    const { 0: path, index } = m;\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n\n      if (key.modifier === \"*\" || key.modifier === \"+\") {\n        params[key.name] = m[i].split(key.prefix + key.suffix).map((value) => {\n          return decode(value, key);\n        });\n      } else {\n        params[key.name] = decode(m[i], key);\n      }\n    }\n\n    return { path, index, params };\n  };\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escapeString(str: string) {\n  return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n\n/**\n * Get the flags for a regexp from the options.\n */\nfunction flags(options?: { sensitive?: boolean }) {\n  return options && options.sensitive ? \"\" : \"i\";\n}\n\n/**\n * Metadata about a key.\n */\nexport interface Key {\n  name: string | number;\n  prefix: string;\n  suffix: string;\n  pattern: string;\n  modifier: string;\n}\n\n/**\n * A token is a string (nothing special) or key metadata (capture group).\n */\nexport type Token = string | Key;\n\n/**\n * Pull out keys from a regexp.\n */\nfunction regexpToRegexp(path: RegExp, keys?: Key[]): RegExp {\n  if (!keys) return path;\n\n  const groupsRegex = /\\((?:\\?<(.*?)>)?(?!\\?)/g;\n\n  let index = 0;\n  let execResult = groupsRegex.exec(path.source);\n  while (execResult) {\n    keys.push({\n      // Use parenthesized substring match if available, index otherwise\n      name: execResult[1] || index++,\n      prefix: \"\",\n      suffix: \"\",\n      modifier: \"\",\n      pattern: \"\",\n    });\n    execResult = groupsRegex.exec(path.source);\n  }\n\n  return path;\n}\n\n/**\n * Transform an array into a regexp.\n */\nfunction arrayToRegexp(\n  paths: Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n): RegExp {\n  const parts = paths.map((path) => pathToRegexp(path, keys, options).source);\n  return new RegExp(`(?:${parts.join(\"|\")})`, flags(options));\n}\n\n/**\n * Create a path regexp from string input.\n */\nfunction stringToRegexp(\n  path: string,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n) {\n  return tokensToRegexp(parse(path, options), keys, options);\n}\n\nexport interface TokensToRegexpOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * When `true` the regexp won't allow an optional trailing delimiter to match. (default: `false`)\n   */\n  strict?: boolean;\n  /**\n   * When `true` the regexp will match to the end of the string. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * When `true` the regexp will match from the beginning of the string. (default: `true`)\n   */\n  start?: boolean;\n  /**\n   * Sets the final character for non-ending optimistic matches. (default: `/`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters that can also be \"end\" characters.\n   */\n  endsWith?: string;\n  /**\n   * Encode path tokens for use in the `RegExp`.\n   */\n  encode?: (value: string) => string;\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\nexport function tokensToRegexp(\n  tokens: Token[],\n  keys?: Key[],\n  options: TokensToRegexpOptions = {},\n) {\n  const {\n    strict = false,\n    start = true,\n    end = true,\n    encode = (x: string) => x,\n    delimiter = \"/#?\",\n    endsWith = \"\",\n  } = options;\n  const endsWithRe = `[${escapeString(endsWith)}]|$`;\n  const delimiterRe = `[${escapeString(delimiter)}]`;\n  let route = start ? \"^\" : \"\";\n\n  // Iterate over the tokens and create our regexp string.\n  for (const token of tokens) {\n    if (typeof token === \"string\") {\n      route += escapeString(encode(token));\n    } else {\n      const prefix = escapeString(encode(token.prefix));\n      const suffix = escapeString(encode(token.suffix));\n\n      if (token.pattern) {\n        if (keys) keys.push(token);\n\n        if (prefix || suffix) {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            const mod = token.modifier === \"*\" ? \"?\" : \"\";\n            route += `(?:${prefix}((?:${token.pattern})(?:${suffix}${prefix}(?:${token.pattern}))*)${suffix})${mod}`;\n          } else {\n            route += `(?:${prefix}(${token.pattern})${suffix})${token.modifier}`;\n          }\n        } else {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            route += `((?:${token.pattern})${token.modifier})`;\n          } else {\n            route += `(${token.pattern})${token.modifier}`;\n          }\n        }\n      } else {\n        route += `(?:${prefix}${suffix})${token.modifier}`;\n      }\n    }\n  }\n\n  if (end) {\n    if (!strict) route += `${delimiterRe}?`;\n\n    route += !options.endsWith ? \"$\" : `(?=${endsWithRe})`;\n  } else {\n    const endToken = tokens[tokens.length - 1];\n    const isEndDelimited =\n      typeof endToken === \"string\"\n        ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1\n        : endToken === undefined;\n\n    if (!strict) {\n      route += `(?:${delimiterRe}(?=${endsWithRe}))?`;\n    }\n\n    if (!isEndDelimited) {\n      route += `(?=${delimiterRe}|${endsWithRe})`;\n    }\n  }\n\n  return new RegExp(route, flags(options));\n}\n\n/**\n * Supported `path-to-regexp` input types.\n */\nexport type Path = string | RegExp | Array<string | RegExp>;\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\nexport function pathToRegexp(\n  path: Path,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n) {\n  if (path instanceof RegExp) return regexpToRegexp(path, keys);\n  if (Array.isArray(path)) return arrayToRegexp(path, keys, options);\n  return stringToRegexp(path, keys, options);\n}\n", "/**\n * Determines if the current process is a Node.js process.\n */\nexport function isNodeProcess(): boolean {\n  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    return true\n  }\n\n  if (typeof process !== 'undefined') {\n    // Electron (https://www.electronjs.org/docs/latest/api/process#processtype-readonly)\n    const type = (process as any).type\n    if (type === 'renderer' || type === 'worker') {\n      return false\n    }\n\n\n    return !!(\n      process.versions &&\n      process.versions.node\n    )\n  }\n\n  return false\n}\n", "const POSITIONALS_EXP = /(%?)(%([sdijo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage = positionals.length === 0 ? message : format(message, positionals);\n    let error: Error;\n\n    try {\n      error = Reflect.construct(ErrorConstructor as CustomErrorConstructor, [formatMessage]);\n    } catch(err) {\n      error = (ErrorConstructor as CustomErrorFactory)(formatMessage);\n    }\n\n    throw error\n  }\n}\n", "var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/index.ts\nimport { isNodeProcess } from \"is-node-process\";\nimport { format } from \"outvariant\";\n\n// src/colors.ts\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\n\n// src/index.ts\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */ new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write(\n      [colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"),\n      ...positionals.map(serializeInput)\n    );\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\n      \"en-GB\"\n    )}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\": {\n        return log;\n      }\n      case \"warning\": {\n        return warn;\n      }\n      case \"error\": {\n        return error;\n      }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\nexport {\n  Logger\n};\n", "import { Logger } from '@open-draft/logger'\nimport { Emitter, Listener } from 'strict-event-emitter'\n\nexport type InterceptorEventMap = Record<string, any>\nexport type InterceptorSubscription = () => void\n\n/**\n * Request header name to detect when a single request\n * is being handled by nested interceptors (XHR -> ClientRequest).\n * Obscure by design to prevent collisions with user-defined headers.\n * Ideally, come up with the Interceptor-level mechanism for this.\n * @see https://github.com/mswjs/interceptors/issues/378\n */\nexport const INTERNAL_REQUEST_ID_HEADER_NAME =\n  'x-interceptors-internal-request-id'\n\nexport function getGlobalSymbol<V>(symbol: Symbol): V | undefined {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || undefined\n  )\n}\n\nfunction setGlobalSymbol(symbol: Symbol, value: any): void {\n  // @ts-ignore\n  globalThis[symbol] = value\n}\n\nexport function deleteGlobalSymbol(symbol: Symbol): void {\n  // @ts-ignore\n  delete globalThis[symbol]\n}\n\nexport enum InterceptorReadyState {\n  INACTIVE = 'INACTIVE',\n  APPLYING = 'APPLYING',\n  APPLIED = 'APPLIED',\n  DISPOSING = 'DISPOSING',\n  DISPOSED = 'DISPOSED',\n}\n\nexport type ExtractEventNames<Events extends Record<string, any>> =\n  Events extends Record<infer EventName, any> ? EventName : never\n\nexport class Interceptor<Events extends InterceptorEventMap> {\n  protected emitter: Emitter<Events>\n  protected subscriptions: Array<InterceptorSubscription>\n  protected logger: Logger\n\n  public readyState: InterceptorReadyState\n\n  constructor(private readonly symbol: symbol) {\n    this.readyState = InterceptorReadyState.INACTIVE\n\n    this.emitter = new Emitter()\n    this.subscriptions = []\n    this.logger = new Logger(symbol.description!)\n\n    // Do not limit the maximum number of listeners\n    // so not to limit the maximum amount of parallel events emitted.\n    this.emitter.setMaxListeners(0)\n\n    this.logger.info('constructing the interceptor...')\n  }\n\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  protected checkEnvironment(): boolean {\n    return true\n  }\n\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  public apply(): void {\n    const logger = this.logger.extend('apply')\n    logger.info('applying the interceptor...')\n\n    if (this.readyState === InterceptorReadyState.APPLIED) {\n      logger.info('intercepted already applied!')\n      return\n    }\n\n    const shouldApply = this.checkEnvironment()\n\n    if (!shouldApply) {\n      logger.info('the interceptor cannot be applied in this environment!')\n      return\n    }\n\n    this.readyState = InterceptorReadyState.APPLYING\n\n    // Whenever applying a new interceptor, check if it hasn't been applied already.\n    // This enables to apply the same interceptor multiple times, for example from a different\n    // interceptor, only proxying events but keeping the stubs in a single place.\n    const runningInstance = this.getInstance()\n\n    if (runningInstance) {\n      logger.info('found a running instance, reusing...')\n\n      // Proxy any listeners you set on this instance to the running instance.\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event)\n\n        // Add listeners to the running instance so they appear\n        // at the top of the event listeners list and are executed first.\n        runningInstance.emitter.addListener(event, listener)\n\n        // Ensure that once this interceptor instance is disposed,\n        // it removes all listeners it has appended to the running interceptor instance.\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener)\n          logger.info('removed proxied \"%s\" listener!', event)\n        })\n\n        return this\n      }\n\n      this.readyState = InterceptorReadyState.APPLIED\n\n      return\n    }\n\n    logger.info('no running instance found, setting up a new instance...')\n\n    // Setup the interceptor.\n    this.setup()\n\n    // Store the newly applied interceptor instance globally.\n    this.setInstance()\n\n    this.readyState = InterceptorReadyState.APPLIED\n  }\n\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  protected setup(): void {}\n\n  /**\n   * Listen to the interceptor's public events.\n   */\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    const logger = this.logger.extend('on')\n\n    if (\n      this.readyState === InterceptorReadyState.DISPOSING ||\n      this.readyState === InterceptorReadyState.DISPOSED\n    ) {\n      logger.info('cannot listen to events, already disposed!')\n      return this\n    }\n\n    logger.info('adding \"%s\" event listener:', event, listener)\n\n    this.emitter.on(event, listener)\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.once(event, listener)\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.off(event, listener)\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName\n  ): this {\n    this.emitter.removeAllListeners(event)\n    return this\n  }\n\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  public dispose(): void {\n    const logger = this.logger.extend('dispose')\n\n    if (this.readyState === InterceptorReadyState.DISPOSED) {\n      logger.info('cannot dispose, already disposed!')\n      return\n    }\n\n    logger.info('disposing the interceptor...')\n    this.readyState = InterceptorReadyState.DISPOSING\n\n    if (!this.getInstance()) {\n      logger.info('no interceptors running, skipping dispose...')\n      return\n    }\n\n    // Delete the global symbol as soon as possible,\n    // indicating that the interceptor is no longer running.\n    this.clearInstance()\n\n    logger.info('global symbol deleted:', getGlobalSymbol(this.symbol))\n\n    if (this.subscriptions.length > 0) {\n      logger.info('disposing of %d subscriptions...', this.subscriptions.length)\n\n      for (const dispose of this.subscriptions) {\n        dispose()\n      }\n\n      this.subscriptions = []\n\n      logger.info('disposed of all subscriptions!', this.subscriptions.length)\n    }\n\n    this.emitter.removeAllListeners()\n    logger.info('destroyed the listener!')\n\n    this.readyState = InterceptorReadyState.DISPOSED\n  }\n\n  private getInstance(): this | undefined {\n    const instance = getGlobalSymbol<this>(this.symbol)\n    this.logger.info('retrieved global instance:', instance?.constructor?.name)\n    return instance\n  }\n\n  private setInstance(): void {\n    setGlobalSymbol(this.symbol, this)\n    this.logger.info('set global instance!', this.symbol.description)\n  }\n\n  private clearInstance(): void {\n    deleteGlobalSymbol(this.symbol)\n    this.logger.info('cleared global instance!', this.symbol.description)\n  }\n}\n", "/**\n * Generate a random ID string to represent a request.\n * @example\n * createRequestId()\n * // \"f774b6c9c600f\"\n */\nexport function createRequestId(): string {\n  return Math.random().toString(16).slice(2)\n}\n", "/**\n * A function that validates if property access is possible on an object\n * without throwing. It returns `true` if the property access is possible\n * and `false` otherwise.\n *\n * Environments like miniflare will throw on property access on certain objects\n * like Request and Response, for unimplemented properties.\n */\nexport function isPropertyAccessible<Obj extends Record<string, any>>(\n  obj: Obj,\n  key: keyof Obj\n) {\n  try {\n    obj[key]\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { isPropertyAccessible } from './isPropertyAccessible'\n\n/**\n * Response status codes for responses that cannot have body.\n * @see https://fetch.spec.whatwg.org/#statuses\n */\nexport const RESPONSE_STATUS_CODES_WITHOUT_BODY = new Set([\n  101, 103, 204, 205, 304,\n])\n\n/**\n * Returns a boolean indicating whether the given response status\n * code represents a response that cannot have a body.\n */\nexport function isResponseWithoutBody(status: number): boolean {\n  return RESPONSE_STATUS_CODES_WITHOUT_BODY.has(status)\n}\n\n/**\n * Creates a generic 500 Unhandled Exception response.\n */\nexport function createServerErrorResponse(body: unknown): Response {\n  return new Response(\n    JSON.stringify(\n      body instanceof Error\n        ? {\n            name: body.name,\n            message: body.message,\n            stack: body.stack,\n          }\n        : body\n    ),\n    {\n      status: 500,\n      statusText: 'Unhandled Exception',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    }\n  )\n}\n\n/**\n * Checks if the given response is a `Response.error()`.\n *\n * @note Some environments, like Miniflare (Cloudflare) do not\n * implement the \"Response.type\" property and throw on its access.\n * Safely check if we can access \"type\" on \"Response\" before continuing.\n * @see https://github.com/mswjs/msw/issues/1834\n */\nexport function isResponseError(\n  response: Response\n): response is Response & { type: 'error' } {\n  return isPropertyAccessible(response, 'type') && response.type === 'error'\n}\n", "import { EventMap, Listener } from 'strict-event-emitter'\nimport { Interceptor, ExtractEventNames } from './Interceptor'\n\nexport interface BatchInterceptorOptions<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> {\n  name: string\n  interceptors: InterceptorList\n}\n\nexport type ExtractEventMapType<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> = InterceptorList extends ReadonlyArray<infer InterceptorType>\n  ? InterceptorType extends Interceptor<infer EventMap>\n    ? EventMap\n    : never\n  : never\n\n/**\n * A batch interceptor that exposes a single interface\n * to apply and operate with multiple interceptors at once.\n */\nexport class BatchInterceptor<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>,\n  Events extends EventMap = ExtractEventMapType<InterceptorList>\n> extends Interceptor<Events> {\n  static symbol: symbol\n\n  private interceptors: InterceptorList\n\n  constructor(options: BatchInterceptorOptions<InterceptorList>) {\n    BatchInterceptor.symbol = Symbol(options.name)\n    super(BatchInterceptor.symbol)\n    this.interceptors = options.interceptors\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('applying all %d interceptors...', this.interceptors.length)\n\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name)\n      interceptor.apply()\n\n      logger.info('adding interceptor dispose subscription')\n      this.subscriptions.push(() => interceptor.dispose())\n    }\n  }\n\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    // Instead of adding a listener to the batch interceptor,\n    // propagate the listener to each of the individual interceptors.\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener)\n    }\n\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener)\n    }\n\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener)\n    }\n\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName | undefined\n  ): this {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event)\n    }\n\n    return this\n  }\n}\n", "const encoder = new TextEncoder()\n\nexport function encodeBuffer(text: string): Uint8Array {\n  return encoder.encode(text)\n}\n\nexport function decodeBuffer(buffer: <PERSON>rrayBuffer, encoding?: string): string {\n  const decoder = new TextDecoder(encoding)\n  return decoder.decode(buffer)\n}\n\n/**\n * Create an `ArrayBuffer` from the given `Uint8Array`.\n * Takes the byte offset into account to produce the right buffer\n * in the case when the buffer is bigger than the data view.\n */\nexport function toArrayBuffer(array: Uint8Array): ArrayBuffer {\n  return array.buffer.slice(\n    array.byteOffset,\n    array.byteOffset + array.byteLength\n  )\n}\n", "import type { InteractiveRequest } from './utils/toInteractiveRequest'\n\nexport const IS_PATCHED_MODULE: unique symbol = Symbol('isPatchedModule')\n\nexport type RequestCredentials = 'omit' | 'include' | 'same-origin'\n\nexport type HttpRequestEventMap = {\n  request: [\n    args: {\n      request: InteractiveRequest\n      requestId: string\n    }\n  ]\n  response: [\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ]\n  unhandledException: [\n    args: {\n      error: unknown\n      request: Request\n      requestId: string\n      controller: {\n        respondWith(response: Response): void\n        errorWith(error?: Error): void\n      }\n    }\n  ]\n}\n", "/**\n * Removes query parameters and hashes from a given URL.\n */\nexport function getCleanUrl(url: URL, isAbsolute: boolean = true): string {\n  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join('')\n}\n", "const REDUNDANT_CHARACTERS_EXP = /[\\?|#].*$/g\n\nexport function getSearchParams(path: string) {\n  return new URL(`/${path}`, 'http://localhost').searchParams\n}\n\n/**\n * Removes search parameters and the fragment\n * from a given URL string.\n */\nexport function cleanUrl(path: string): string {\n  // If the path ends with an optional path parameter,\n  // return it as-is.\n  if (path.endsWith('?')) {\n    return path\n  }\n\n  // Otherwise, remove the search and fragment from it.\n  return path.replace(REDUNDANT_CHARACTERS_EXP, '')\n}\n", "/**\n * Determines if the given URL string is an absolute URL.\n */\nexport function isAbsoluteUrl(url: string): boolean {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url)\n}\n", "import { isAbsoluteUrl } from './isAbsoluteUrl'\n\n/**\n * Returns an absolute URL based on the given path.\n */\nexport function getAbsoluteUrl(path: string, baseUrl?: string): string {\n  // already absolute URL\n  if (isAbsoluteUrl(path)) {\n    return path\n  }\n\n  // Ignore path with pattern start with *\n  if (path.startsWith('*')) {\n    return path\n  }\n\n  // Resolve a relative request URL against a given custom \"baseUrl\"\n  // or the document baseURI (in the case of browser/browser-like environments).\n  const origin =\n    baseUrl || (typeof document !== 'undefined' && document.baseURI)\n\n  return origin\n    ? // Encode and decode the path to preserve escaped characters.\n      decodeURI(new URL(encodeURI(path), origin).href)\n    : path\n}\n", "import type { Path } from './matchRequestUrl'\nimport { cleanUrl } from '../url/cleanUrl'\nimport { getAbsoluteUrl } from '../url/getAbsoluteUrl'\n\n/**\n * Normalizes a given request handler path:\n * - Preserves RegExp.\n * - Removes query parameters and hashes.\n * - Rebases relative URLs against the \"baseUrl\" or the current location.\n * - Preserves relative URLs in Node.js, unless specified otherwise.\n * - Preserves optional path parameters.\n */\nexport function normalizePath(path: Path, baseUrl?: string): Path {\n  // RegExp paths do not need normalization.\n  if (path instanceof RegExp) {\n    return path\n  }\n\n  const maybeAbsoluteUrl = getAbsoluteUrl(path, baseUrl)\n\n  return cleanUrl(maybeAbsoluteUrl)\n}\n", "import { match } from 'path-to-regexp'\nimport { getCleanUrl } from '@mswjs/interceptors'\nimport { normalizePath } from './normalizePath'\n\nexport type Path = string | RegExp\nexport type PathParams<KeyType extends keyof any = string> = {\n  [ParamName in KeyType]: string | ReadonlyArray<string>\n}\n\nexport interface Match {\n  matches: boolean\n  params?: PathParams\n}\n\n/**\n * Coerce a path supported by MSW into a path\n * supported by \"path-to-regexp\".\n */\nexport function coercePath(path: string): string {\n  return (\n    path\n      /**\n       * Replace wildcards (\"*\") with unnamed capturing groups\n       * because \"path-to-regexp\" doesn't support wildcards.\n       * Ignore path parameter' modifiers (i.e. \":name*\").\n       */\n      .replace(\n        /([:a-zA-Z_-]*)(\\*{1,2})+/g,\n        (_, parameterName: string | undefined, wildcard: string) => {\n          const expression = '(.*)'\n\n          if (!parameterName) {\n            return expression\n          }\n\n          return parameterName.startsWith(':')\n            ? `${parameterName}${wildcard}`\n            : `${parameterName}${expression}`\n        },\n      )\n      /**\n       * Escape the port so that \"path-to-regexp\" can match\n       * absolute URLs including port numbers.\n       */\n      .replace(/([^\\/])(:)(?=\\d+)/, '$1\\\\$2')\n      /**\n       * Escape the protocol so that \"path-to-regexp\" could match\n       * absolute URL.\n       * @see https://github.com/pillarjs/path-to-regexp/issues/259\n       */\n      .replace(/^([^\\/]+)(:)(?=\\/\\/)/, '$1\\\\$2')\n  )\n}\n\n/**\n * Returns the result of matching given request URL against a mask.\n */\nexport function matchRequestUrl(url: URL, path: Path, baseUrl?: string): Match {\n  const normalizedPath = normalizePath(path, baseUrl)\n  const cleanPath =\n    typeof normalizedPath === 'string'\n      ? coercePath(normalizedPath)\n      : normalizedPath\n\n  const cleanUrl = getCleanUrl(url)\n  const result = match(cleanPath, { decode: decodeURIComponent })(cleanUrl)\n  const params = (result && (result.params as PathParams)) || {}\n\n  return {\n    matches: result !== false,\n    params,\n  }\n}\n", "/**\n * Returns a relative URL if the given request URL is relative\n * to the current origin. Otherwise returns an absolute URL.\n */\nexport function toPublicUrl(url: string | URL): string {\n  if (typeof location === 'undefined') {\n    return url.toString()\n  }\n\n  const urlInstance = url instanceof URL ? url : new URL(url)\n\n  return urlInstance.origin === location.origin\n    ? urlInstance.pathname\n    : urlInstance.origin + urlInstance.pathname\n}\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// node_modules/cookie/index.js\nvar require_cookie = __commonJS({\n  \"node_modules/cookie/index.js\"(exports) {\n    \"use strict\";\n    exports.parse = parse;\n    exports.serialize = serialize;\n    var __toString = Object.prototype.toString;\n    var fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n    function parse(str, options) {\n      if (typeof str !== \"string\") {\n        throw new TypeError(\"argument str must be a string\");\n      }\n      var obj = {};\n      var opt = options || {};\n      var dec = opt.decode || decode;\n      var index = 0;\n      while (index < str.length) {\n        var eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1) {\n          break;\n        }\n        var endIdx = str.indexOf(\";\", index);\n        if (endIdx === -1) {\n          endIdx = str.length;\n        } else if (endIdx < eqIdx) {\n          index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n          continue;\n        }\n        var key = str.slice(index, eqIdx).trim();\n        if (void 0 === obj[key]) {\n          var val = str.slice(eqIdx + 1, endIdx).trim();\n          if (val.charCodeAt(0) === 34) {\n            val = val.slice(1, -1);\n          }\n          obj[key] = tryDecode(val, dec);\n        }\n        index = endIdx + 1;\n      }\n      return obj;\n    }\n    function serialize(name, val, options) {\n      var opt = options || {};\n      var enc = opt.encode || encode;\n      if (typeof enc !== \"function\") {\n        throw new TypeError(\"option encode is invalid\");\n      }\n      if (!fieldContentRegExp.test(name)) {\n        throw new TypeError(\"argument name is invalid\");\n      }\n      var value = enc(val);\n      if (value && !fieldContentRegExp.test(value)) {\n        throw new TypeError(\"argument val is invalid\");\n      }\n      var str = name + \"=\" + value;\n      if (null != opt.maxAge) {\n        var maxAge = opt.maxAge - 0;\n        if (isNaN(maxAge) || !isFinite(maxAge)) {\n          throw new TypeError(\"option maxAge is invalid\");\n        }\n        str += \"; Max-Age=\" + Math.floor(maxAge);\n      }\n      if (opt.domain) {\n        if (!fieldContentRegExp.test(opt.domain)) {\n          throw new TypeError(\"option domain is invalid\");\n        }\n        str += \"; Domain=\" + opt.domain;\n      }\n      if (opt.path) {\n        if (!fieldContentRegExp.test(opt.path)) {\n          throw new TypeError(\"option path is invalid\");\n        }\n        str += \"; Path=\" + opt.path;\n      }\n      if (opt.expires) {\n        var expires = opt.expires;\n        if (!isDate(expires) || isNaN(expires.valueOf())) {\n          throw new TypeError(\"option expires is invalid\");\n        }\n        str += \"; Expires=\" + expires.toUTCString();\n      }\n      if (opt.httpOnly) {\n        str += \"; HttpOnly\";\n      }\n      if (opt.secure) {\n        str += \"; Secure\";\n      }\n      if (opt.priority) {\n        var priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n        switch (priority) {\n          case \"low\":\n            str += \"; Priority=Low\";\n            break;\n          case \"medium\":\n            str += \"; Priority=Medium\";\n            break;\n          case \"high\":\n            str += \"; Priority=High\";\n            break;\n          default:\n            throw new TypeError(\"option priority is invalid\");\n        }\n      }\n      if (opt.sameSite) {\n        var sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n        switch (sameSite) {\n          case true:\n            str += \"; SameSite=Strict\";\n            break;\n          case \"lax\":\n            str += \"; SameSite=Lax\";\n            break;\n          case \"strict\":\n            str += \"; SameSite=Strict\";\n            break;\n          case \"none\":\n            str += \"; SameSite=None\";\n            break;\n          default:\n            throw new TypeError(\"option sameSite is invalid\");\n        }\n      }\n      return str;\n    }\n    function decode(str) {\n      return str.indexOf(\"%\") !== -1 ? decodeURIComponent(str) : str;\n    }\n    function encode(val) {\n      return encodeURIComponent(val);\n    }\n    function isDate(val) {\n      return __toString.call(val) === \"[object Date]\" || val instanceof Date;\n    }\n    function tryDecode(str, decode2) {\n      try {\n        return decode2(str);\n      } catch (e) {\n        return str;\n      }\n    }\n  }\n});\n\n// source.js\nvar import_cookie = __toESM(require_cookie(), 1);\nvar source_default = import_cookie.default;\nexport {\n  source_default as default\n};\n/*! Bundled license information:\n\ncookie/index.js:\n  (*!\n   * cookie\n   * Copyright(c) 2012-2014 Roman Shtylman\n   * Copyright(c) 2015 Douglas Christopher Wilson\n   * MIT Licensed\n   *)\n*/\n", "\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "import { <PERSON><PERSON>, parse as parse<PERSON><PERSON><PERSON> } from 'set-cookie-parser'\n\ninterface RequestLike {\n  credentials: Request['credentials']\n  url: string\n}\n\ninterface HeadersLike {\n  get(name: string): string | null\n}\n\ninterface ResponseLike {\n  headers: HeadersLike\n}\n\nexport type Store = Map<string, StoreEntry>\nexport type StoreEntry = Map<string, Cookie>\nexport type CookieString = Omit<Cookie, 'expires'> & { expires?: string }\n\nexport const PERSISTENCY_KEY = 'MSW_COOKIE_STORE'\n\nfunction supportsLocalStorage() {\n  try {\n    if (localStorage == null) {\n      return false\n    }\n\n    const testKey = PERSISTENCY_KEY + '_test'\n\n    localStorage.setItem(testKey, 'test')\n    localStorage.getItem(testKey)\n    localStorage.removeItem(testKey)\n\n    return true\n  } catch (error) {\n    return false\n  }\n}\n\nconst hasLocalStorageSupport = supportsLocalStorage()\n\n/**\n * Checks that accessing a given property on an object\n * by name does not throw an error.\n *\n * This is generally used to avoid issues in environments\n * like `miniflare` where some properties are defined as getters\n * where accessing that property throws directly.\n */\nfunction isPropertyAccessible<Obj extends Record<string, any>>(\n  object: Obj,\n  method: keyof Obj,\n) {\n  try {\n    object[method]\n    return true\n  } catch {\n    return false\n  }\n}\n\nclass CookieStore {\n  private store: Store\n\n  constructor() {\n    this.store = new Map()\n  }\n\n  /**\n   * Sets the given request cookies into the store.\n   * Respects the `request.credentials` policy.\n   */\n  add(request: RequestLike, response: ResponseLike): void {\n    if (\n      isPropertyAccessible(request, 'credentials') &&\n      request.credentials === 'omit'\n    ) {\n      return\n    }\n\n    const requestUrl = new URL(request.url)\n    const responseCookies = response.headers.get('set-cookie')\n\n    if (!responseCookies) {\n      return\n    }\n\n    const now = Date.now()\n    const parsedResponseCookies = parseCookie(responseCookies).map(\n      ({ maxAge, ...cookie }) => ({\n        ...cookie,\n        expires:\n          maxAge === undefined ? cookie.expires : new Date(now + maxAge * 1000),\n        maxAge,\n      }),\n    )\n\n    const prevCookies =\n      this.store.get(requestUrl.origin) || new Map<string, Cookie>()\n\n    parsedResponseCookies.forEach((cookie) => {\n      this.store.set(requestUrl.origin, prevCookies.set(cookie.name, cookie))\n    })\n  }\n\n  /**\n   * Returns cookies relevant to the given request\n   * and its `request.credentials` policy.\n   */\n  get(request: RequestLike): StoreEntry {\n    this.deleteExpiredCookies()\n\n    const requestUrl = new URL(request.url)\n    const originCookies =\n      this.store.get(requestUrl.origin) || new Map<string, Cookie>()\n\n    if (!isPropertyAccessible(request, 'credentials')) {\n      return originCookies\n    }\n\n    switch (request.credentials) {\n      case 'include': {\n        // Support running this method in Node.js.\n        if (typeof document === 'undefined') {\n          return originCookies\n        }\n\n        const documentCookies = parseCookie(document.cookie)\n\n        documentCookies.forEach((cookie) => {\n          originCookies.set(cookie.name, cookie)\n        })\n\n        return originCookies\n      }\n\n      case 'same-origin': {\n        return originCookies\n      }\n\n      default:\n        return new Map()\n    }\n  }\n\n  /**\n   * Returns a collection of all stored cookies.\n   */\n  getAll(): Store {\n    this.deleteExpiredCookies()\n    return this.store\n  }\n\n  /**\n   * Deletes all cookies associated with the given request.\n   */\n  deleteAll(request: RequestLike): void {\n    const requestUrl = new URL(request.url)\n    this.store.delete(requestUrl.origin)\n  }\n\n  /**\n   * Clears the entire cookie store.\n   */\n  clear(): void {\n    this.store.clear()\n  }\n\n  /**\n   * Hydrates the virtual cookie store from the `localStorage` if defined.\n   */\n  hydrate(): void {\n    if (!hasLocalStorageSupport) {\n      return\n    }\n\n    const persistedCookies = localStorage.getItem(PERSISTENCY_KEY)\n\n    if (!persistedCookies) {\n      return\n    }\n\n    try {\n      const parsedCookies: [string, [string, CookieString][]][] =\n        JSON.parse(persistedCookies)\n\n      parsedCookies.forEach(([origin, cookies]) => {\n        this.store.set(\n          origin,\n          new Map(\n            cookies.map(([token, { expires, ...cookie }]) => [\n              token,\n              expires === undefined\n                ? cookie\n                : { ...cookie, expires: new Date(expires) },\n            ]),\n          ),\n        )\n      })\n    } catch (error) {\n      console.warn(`\n[virtual-cookie] Failed to parse a stored cookie from the localStorage (key \"${PERSISTENCY_KEY}\").\n\nStored value:\n${localStorage.getItem(PERSISTENCY_KEY)}\n\nThrown exception:\n${error}\n\nInvalid value has been removed from localStorage to prevent subsequent failed parsing attempts.`)\n      localStorage.removeItem(PERSISTENCY_KEY)\n    }\n  }\n\n  /**\n   * Persists the current virtual cookies into the `localStorage` if defined,\n   * so they are available on the next page load.\n   */\n  persist(): void {\n    if (!hasLocalStorageSupport) {\n      return\n    }\n\n    const serializedCookies = Array.from(this.store.entries()).map(\n      ([origin, cookies]) => {\n        return [origin, Array.from(cookies.entries())]\n      },\n    )\n\n    localStorage.setItem(PERSISTENCY_KEY, JSON.stringify(serializedCookies))\n  }\n\n  private deleteExpiredCookies() {\n    const now = Date.now()\n\n    this.store.forEach((originCookies, origin) => {\n      originCookies.forEach(({ expires, name }) => {\n        if (expires !== undefined && expires.getTime() <= now) {\n          originCookies.delete(name)\n        }\n      })\n\n      if (originCookies.size === 0) {\n        this.store.delete(origin)\n      }\n    })\n  }\n}\n\nexport const store = new CookieStore()\n", "import cookieUtils from '@bundled-es-modules/cookie'\nimport { store } from '@mswjs/cookies'\n\nfunction getAllDocumentCookies() {\n  return cookieUtils.parse(document.cookie)\n}\n\n/** @todo Rename this to \"getDocumentCookies\" */\n/**\n * Returns relevant document cookies based on the request `credentials` option.\n */\nexport function getRequestCookies(request: Request): Record<string, string> {\n  /**\n   * @note No cookies persist on the document in Node.js: no document.\n   */\n  if (typeof document === 'undefined' || typeof location === 'undefined') {\n    return {}\n  }\n\n  switch (request.credentials) {\n    case 'same-origin': {\n      const url = new URL(request.url)\n\n      // Return document cookies only when requested a resource\n      // from the same origin as the current document.\n      return location.origin === url.origin ? getAllDocumentCookies() : {}\n    }\n\n    case 'include': {\n      // Return all document cookies.\n      return getAllDocumentCookies()\n    }\n\n    default: {\n      return {}\n    }\n  }\n}\n\nexport function getAllRequestCookies(request: Request): Record<string, string> {\n  const requestCookiesString = request.headers.get('cookie')\n  const cookiesFromHeaders = requestCookiesString\n    ? cookieUtils.parse(requestCookiesString)\n    : {}\n\n  store.hydrate()\n\n  const cookiesFromStore = Array.from(store.get(request)?.entries()).reduce<\n    Record<string, string>\n  >((cookies, [name, { value }]) => {\n    return Object.assign(cookies, { [name.trim()]: value })\n  }, {})\n\n  const cookiesFromDocument = getRequestCookies(request)\n\n  const forwardedCookies = {\n    ...cookiesFromDocument,\n    ...cookiesFromStore,\n  }\n\n  // Set the inferred cookies from the cookie store and the document\n  // on the request's headers.\n  /**\n   * @todo Consider making this a separate step so this function\n   * is pure-er.\n   */\n  for (const [name, value] of Object.entries(forwardedCookies)) {\n    request.headers.append('cookie', cookieUtils.serialize(name, value))\n  }\n\n  return {\n    ...forwardedCookies,\n    ...cookiesFromHeaders,\n  }\n}\n", "import { ResponseResolutionContext } from '../utils/executeHandlers'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { isStringEqual } from '../utils/internal/isStringEqual'\nimport { getStatusCodeColor } from '../utils/logging/getStatusCodeColor'\nimport { getTimestamp } from '../utils/logging/getTimestamp'\nimport { serializeRequest } from '../utils/logging/serializeRequest'\nimport { serializeResponse } from '../utils/logging/serializeResponse'\nimport {\n  matchRequestUrl,\n  Match,\n  Path,\n  PathParams,\n} from '../utils/matching/matchRequestUrl'\nimport { toPublicUrl } from '../utils/request/toPublicUrl'\nimport { getAllRequestCookies } from '../utils/request/getRequestCookies'\nimport { cleanUrl, getSearchParams } from '../utils/url/cleanUrl'\nimport {\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './RequestHandler'\n\ntype HttpHandlerMethod = string | RegExp\n\nexport interface HttpHandlerInfo extends RequestHandlerDefaultInfo {\n  method: HttpHandlerMethod\n  path: Path\n}\n\nexport enum HttpMethods {\n  HEAD = 'HEAD',\n  GET = 'GET',\n  POST = 'POST',\n  PUT = 'PUT',\n  PATCH = 'PATCH',\n  OPTIONS = 'OPTIONS',\n  DELETE = 'DELETE',\n}\n\nexport type RequestQuery = {\n  [queryName: string]: string\n}\n\nexport type HttpRequestParsedResult = {\n  match: Match\n  cookies: Record<string, string>\n}\n\nexport type HttpRequestResolverExtras<Params extends PathParams> = {\n  params: Params\n  cookies: Record<string, string>\n}\n\n/**\n * Request handler for HTTP requests.\n * Provides request matching based on method and URL.\n */\nexport class HttpHandler extends RequestHandler<\n  HttpHandlerInfo,\n  HttpRequestParsedResult,\n  HttpRequestResolverExtras<any>\n> {\n  constructor(\n    method: HttpHandlerMethod,\n    path: Path,\n    resolver: ResponseResolver<HttpRequestResolverExtras<any>, any, any>,\n    options?: RequestHandlerOptions,\n  ) {\n    super({\n      info: {\n        header: `${method} ${path}`,\n        path,\n        method,\n      },\n      resolver,\n      options,\n    })\n\n    this.checkRedundantQueryParameters()\n  }\n\n  private checkRedundantQueryParameters() {\n    const { method, path } = this.info\n\n    if (path instanceof RegExp) {\n      return\n    }\n\n    const url = cleanUrl(path)\n\n    // Bypass request handler URLs that have no redundant characters.\n    if (url === path) {\n      return\n    }\n\n    const searchParams = getSearchParams(path)\n    const queryParams: string[] = []\n\n    searchParams.forEach((_, paramName) => {\n      queryParams.push(paramName)\n    })\n\n    devUtils.warn(\n      `Found a redundant usage of query parameters in the request handler URL for \"${method} ${path}\". Please match against a path instead and access query parameters using \"new URL(request.url).searchParams\" instead. Learn more: https://mswjs.io/docs/recipes/query-parameters`,\n    )\n  }\n\n  async parse(args: {\n    request: Request\n    resolutionContext?: ResponseResolutionContext\n  }) {\n    const url = new URL(args.request.url)\n    const match = matchRequestUrl(\n      url,\n      this.info.path,\n      args.resolutionContext?.baseUrl,\n    )\n    const cookies = getAllRequestCookies(args.request)\n\n    return {\n      match,\n      cookies,\n    }\n  }\n\n  predicate(args: { request: Request; parsedResult: HttpRequestParsedResult }) {\n    const hasMatchingMethod = this.matchMethod(args.request.method)\n    const hasMatchingUrl = args.parsedResult.match.matches\n    return hasMatchingMethod && hasMatchingUrl\n  }\n\n  private matchMethod(actualMethod: string): boolean {\n    return this.info.method instanceof RegExp\n      ? this.info.method.test(actualMethod)\n      : isStringEqual(this.info.method, actualMethod)\n  }\n\n  protected extendResolverArgs(args: {\n    request: Request\n    parsedResult: HttpRequestParsedResult\n  }) {\n    return {\n      params: args.parsedResult.match?.params || {},\n      cookies: args.parsedResult.cookies,\n    }\n  }\n\n  async log(args: { request: Request; response: Response }) {\n    const publicUrl = toPublicUrl(args.request.url)\n    const loggedRequest = await serializeRequest(args.request)\n    const loggedResponse = await serializeResponse(args.response)\n    const statusColor = getStatusCodeColor(loggedResponse.status)\n\n    console.groupCollapsed(\n      devUtils.formatMessage(\n        `${getTimestamp()} ${args.request.method} ${publicUrl} (%c${\n          loggedResponse.status\n        } ${loggedResponse.statusText}%c)`,\n      ),\n      `color:${statusColor}`,\n      'color:inherit',\n    )\n    console.log('Request', loggedRequest)\n    console.log('Handler:', this)\n    console.log('Response', loggedResponse)\n    console.groupEnd()\n  }\n}\n", "import {\n  DefaultBodyType,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './handlers/RequestHandler'\nimport {\n  HttpMethods,\n  HttpHandler,\n  HttpRequestResolverExtras,\n} from './handlers/HttpHandler'\nimport type { Path, PathParams } from './utils/matching/matchRequestUrl'\n\nexport type HttpRequestHandler = <\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  // Response body type MUST be undefined by default.\n  // This is how we can distinguish between a handler that\n  // returns plain \"Response\" and the one returning \"HttpResponse\"\n  // to enforce a stricter response body type.\n  ResponseBodyType extends DefaultBodyType = undefined,\n  RequestPath extends Path = Path,\n>(\n  path: RequestPath,\n  resolver: HttpResponseResolver<Params, RequestBodyType, ResponseBodyType>,\n  options?: RequestHandlerOptions,\n) => HttpHandler\n\nexport type HttpResponseResolver<\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  ResponseBodyType extends DefaultBodyType = DefaultBodyType,\n> = ResponseResolver<\n  HttpRequestResolverExtras<Params>,\n  RequestBodyType,\n  ResponseBodyType\n>\n\nfunction createHttpHandler<Method extends HttpMethods | RegExp>(\n  method: Method,\n): HttpRequestHandler {\n  return (path, resolver, options = {}) => {\n    return new HttpHandler(method, path, resolver, options)\n  }\n}\n\n/**\n * A namespace to intercept and mock HTTP requests.\n *\n * @example\n * http.get('/user', resolver)\n * http.post('/post/:id', resolver)\n *\n * @see {@link https://mswjs.io/docs/api/http `http` API reference}\n */\nexport const http = {\n  all: createHttpHandler(/.+/),\n  head: createHttpHandler(HttpMethods.HEAD),\n  get: createHttpHandler(HttpMethods.GET),\n  post: createHttpHandler(HttpMethods.POST),\n  put: createHttpHandler(HttpMethods.PUT),\n  delete: createHttpHandler(HttpMethods.DELETE),\n  patch: createHttpHandler(HttpMethods.PATCH),\n  options: createHttpHandler(HttpMethods.OPTIONS),\n}\n", "\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "import { splitCookiesString } from 'set-cookie-parser'\nimport { HeadersList, HeadersObject } from './glossary'\nimport { normalizeHeaderName } from './utils/normalizeHeaderName'\nimport { normalizeHeaderValue } from './utils/normalizeHeaderValue'\nimport { isValidHeaderName } from './utils/isValidHeaderName'\nimport { isValidHeaderValue } from './utils/isValidHeaderValue'\n\nexport const NORMALIZED_HEADERS: unique symbol = Symbol('normalizedHeaders')\n\nexport const RAW_HEADER_NAMES: unique symbol = Symbol('rawHeaderNames')\n\nconst HEADER_VALUE_DELIMITER = ', ' as const\n\nexport class Headers {\n  // Normalized header {\"name\":\"a, b\"} storage.\n  private [NORMALIZED_HEADERS]: Record<string, string> = {}\n\n  // Keeps the mapping between the raw header name\n  // and the normalized header name to ease the lookup.\n  private [RAW_HEADER_NAMES]: Map<string, string> = new Map()\n\n  constructor(init?: HeadersInit | HeadersObject | HeadersList) {\n    /**\n     * @note Cannot necessarily check if the `init` is an instance of the\n     * `Headers` because that class may not be defined in Node or jsdom.\n     */\n    if (\n      ['Headers', 'HeadersPolyfill'].includes(init?.constructor.name) ||\n      init instanceof Headers ||\n      (typeof globalThis.Headers !== 'undefined' &&\n        init instanceof globalThis.Headers)\n    ) {\n      const initialHeaders = init as Headers\n      initialHeaders.forEach((value, name) => {\n        this.append(name, value)\n      }, this)\n    } else if (Array.isArray(init)) {\n      init.forEach(([name, value]) => {\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    } else if (init) {\n      Object.getOwnPropertyNames(init).forEach((name) => {\n        const value = init[name]\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    }\n  }\n\n  [Symbol.toStringTag] = 'Headers';\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  *keys(): IterableIterator<string> {\n    for (const [name] of this.entries()) {\n      yield name\n    }\n  }\n\n  *values(): IterableIterator<string> {\n    for (const [, value] of this.entries()) {\n      yield value\n    }\n  }\n\n  *entries(): IterableIterator<[string, string]> {\n    // https://fetch.spec.whatwg.org/#concept-header-list-sort-and-combine\n    let sortedKeys = Object.keys(this[NORMALIZED_HEADERS]).sort((a, b) =>\n      a.localeCompare(b)\n    )\n    for (const name of sortedKeys) {\n      if (name === 'set-cookie') {\n        for (const value of this.getSetCookie()) {\n          yield [name, value]\n        }\n      } else {\n        yield [name, this.get(name)]\n      }\n    }\n  }\n\n  /**\n   * Returns a boolean stating whether a `Headers` object contains a certain header.\n   */\n  has(name: string): boolean {\n    if (!isValidHeaderName(name)) {\n      throw new TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS].hasOwnProperty(normalizeHeaderName(name))\n  }\n\n  /**\n   * Returns a `ByteString` sequence of all the values of a header with a given name.\n   */\n  get(name: string): string | null {\n    if (!isValidHeaderName(name)) {\n      throw TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS][normalizeHeaderName(name)] ?? null\n  }\n\n  /**\n   * Sets a new value for an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  set(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    this[NORMALIZED_HEADERS][normalizedName] =\n      normalizeHeaderValue(normalizedValue)\n    this[RAW_HEADER_NAMES].set(normalizedName, name)\n  }\n\n  /**\n   * Appends a new value onto an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  append(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    let resolvedValue = this.has(normalizedName)\n      ? `${this.get(normalizedName)}, ${normalizedValue}`\n      : normalizedValue\n\n    this.set(name, resolvedValue)\n  }\n\n  /**\n   * Deletes a header from the `Headers` object.\n   */\n  delete(name: string): void {\n    if (!isValidHeaderName(name)) {\n      return\n    }\n\n    if (!this.has(name)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    delete this[NORMALIZED_HEADERS][normalizedName]\n    this[RAW_HEADER_NAMES].delete(normalizedName)\n  }\n\n  /**\n   * Traverses the `Headers` object,\n   * calling the given callback for each header.\n   */\n  forEach<ThisArg = this>(\n    callback: (\n      this: ThisArg,\n      value: string,\n      name: string,\n      parent: this\n    ) => void,\n    thisArg?: ThisArg\n  ) {\n    for (const [name, value] of this.entries()) {\n      callback.call(thisArg, value, name, this)\n    }\n  }\n\n  /**\n   * Returns an array containing the values\n   * of all Set-Cookie headers associated\n   * with a response\n   */\n  getSetCookie(): string[] {\n    const setCookieHeader = this.get('set-cookie')\n\n    if (setCookieHeader === null) {\n      return []\n    }\n\n    if (setCookieHeader === '') {\n      return ['']\n    }\n\n    return splitCookiesString(setCookieHeader)\n  }\n}\n", "const HEADERS_INVALID_CHARACTERS = /[^a-z0-9\\-#$%&'*+.^_`|~]/i\n\nexport function normalizeHeaderName(name: string): string {\n  if (HEADERS_INVALID_CHARACTERS.test(name) || name.trim() === '') {\n    throw new TypeError('Invalid character in header field name')\n  }\n\n  return name.trim().toLowerCase()\n}\n", "const charCodesToRemove = [\n  String.fromCharCode(0x0a),\n  String.fromCharCode(0x0d),\n  String.fromCharCode(0x09),\n  String.fromCharCode(0x20),\n]\n\nconst HEADER_VALUE_REMOVE_REGEXP = new RegExp(\n  `(^[${charCodesToRemove.join('')}]|$[${charCodesToRemove.join('')}])`,\n  'g'\n)\n\n/**\n * Normalize the given header value.\n * @see https://fetch.spec.whatwg.org/#concept-header-value-normalize\n */\nexport function normalizeHeaderValue(value: string): string {\n  const nextValue = value.replace(HEADER_VALUE_REMOVE_REGEXP, '')\n  return nextValue\n}\n", "/**\n * Validate the given header name.\n * @see https://fetch.spec.whatwg.org/#header-name\n */\nexport function isValidHeaderName(value: unknown) {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.length === 0) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (character > 0x7f || !isToken(character)) {\n      return false\n    }\n  }\n\n  return true\n}\n\nfunction isToken(value: string | number): boolean {\n  return ![\n    0x7f,\n    0x20,\n    '(',\n    ')',\n    '<',\n    '>',\n    '@',\n    ',',\n    ';',\n    ':',\n    '\\\\',\n    '\"',\n    '/',\n    '[',\n    ']',\n    '?',\n    '=',\n    '{',\n    '}',\n  ].includes(value)\n}\n", "/**\n * Validate the given header value.\n * @see https://fetch.spec.whatwg.org/#header-value\n */\nexport function isValidHeaderValue(value: unknown): boolean {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.trim() !== value) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (\n      // NUL.\n      character === 0x00 ||\n      // HTTP newline bytes.\n      character === 0x0a ||\n      character === 0x0d\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import { RAW_HEADER_NAMES } from './Headers'\n\n/**\n * Returns the object of all raw headers.\n */\nexport function getRawHeaders(headers: Headers) {\n  const rawHeaders: Record<string, string> = {}\n\n  for (const [name, value] of headers.entries()) {\n    rawHeaders[headers[RAW_HEADER_NAMES].get(name)] = value\n  }\n\n  return rawHeaders\n}\n", "import { HeadersList } from '../glossary'\n\nexport function headersToList(headers: Headers): HeadersList {\n  const headersList: HeadersList = []\n\n  headers.forEach((value, name) => {\n    const resolvedValue = value.includes(',')\n      ? value.split(',').map((value) => value.trim())\n      : value\n\n    headersList.push([name, resolvedValue])\n  })\n\n  return headersList\n}\n", "import { headersToList } from './headersToList'\n\n/**\n * Converts a given `Headers` instance to its string representation.\n */\nexport function headersToString(headers: Headers): string {\n  const list = headersToList(headers)\n  const lines = list.map(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n    return `${name}: ${values.join(', ')}`\n  })\n\n  return lines.join('\\r\\n')\n}\n", "import { HeadersObject } from '../glossary'\n\n// List of headers that cannot have multiple values,\n// while potentially having a comma in their single value.\nconst singleValueHeaders = ['user-agent']\n\n/**\n * Converts a given `Headers` instance into a plain object.\n * Respects headers with multiple values.\n */\nexport function headersToObject(headers: Headers): HeadersObject {\n  const headersObject: HeadersObject = {}\n\n  headers.forEach((value, name) => {\n    const isMultiValue =\n      !singleValueHeaders.includes(name.toLowerCase()) && value.includes(',')\n    headersObject[name] = isMultiValue\n      ? value.split(',').map((s) => s.trim())\n      : value\n  })\n\n  return headersObject\n}\n", "import { Headers } from '../Headers'\n\n/**\n * Converts a string representation of headers (i.e. from XMLHttpRequest)\n * to a new `Headers` instance.\n */\nexport function stringToHeaders(str: string): Headers {\n  const lines = str.trim().split(/[\\r\\n]+/)\n\n  return lines.reduce((headers, line) => {\n    if (line.trim() === '') {\n      return headers\n    }\n\n    const parts = line.split(': ')\n    const name = parts.shift()\n    const value = parts.join(': ')\n    headers.append(name, value)\n\n    return headers\n  }, new Headers())\n}\n", "import { Headers } from '../Headers'\nimport { HeadersList } from '../glossary'\n\nexport function listToHeaders(list: HeadersList): Headers {\n  const headers = new Headers()\n\n  list.forEach(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n\n    values.forEach((value) => {\n      headers.append(name, value)\n    })\n  })\n\n  return headers\n}\n", "import { HeadersObject } from '../glossary'\n\n/**\n * Reduces given headers object instnace.\n */\nexport function reduceHeadersObject<R>(\n  headers: HeadersObject,\n  reducer: (headers: R, name: string, value: string | string[]) => R,\n  initialState: R\n): R {\n  return Object.keys(headers).reduce<R>((nextHeaders, name) => {\n    return reducer(nextHeaders, name, headers[name])\n  }, initialState)\n}\n", "import { Headers } from '../Headers'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\n/**\n * Converts a given headers object to a new `Headers` instance.\n */\nexport function objectToHeaders(\n  headersObject: Record<string, string | string[] | undefined>\n): Headers {\n  return reduceHeadersObject(\n    headersObject,\n    (headers, name, value) => {\n      const values = ([] as string[]).concat(value).filter(Boolean)\n\n      values.forEach((value) => {\n        headers.append(name, value)\n      })\n\n      return headers\n    },\n    new Headers()\n  )\n}\n", "import { HeadersList, FlatHeadersList } from '../glossary'\n\nexport function flattenHeadersList(list: HeadersList): FlatHeadersList {\n  return list.map(([name, values]) => {\n    return [name, ([] as string[]).concat(values).join(', ')]\n  })\n}\n", "import { HeadersObject, FlatHeadersObject } from '../glossary'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\nexport function flattenHeadersObject(\n  headersObject: HeadersObject\n): FlatHeadersObject {\n  return reduceHeadersObject<FlatHeadersObject>(\n    headersObject,\n    (headers, name, value) => {\n      headers[name] = ([] as string[]).concat(value).join(', ')\n      return headers\n    },\n    {}\n  )\n}\n", "export type AsyncTuple<\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n> =\n  | {\n      error: ErrorType\n      data: null\n    }\n  | { error: null; data: DataType }\n\n/**\n * Gracefully handles a given Promise factory.\n * @example\n * const { error, data } = await until(() => asyncAction())\n */\nexport const until = async <\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n>(\n  promise: () => Promise<DataType>,\n): Promise<AsyncTuple<ErrorType, DataType>> => {\n  try {\n    const data = await promise().catch((error) => {\n      throw error\n    })\n    return { error: null, data }\n  } catch (error) {\n    return { error, data: null }\n  }\n}\n", "import {\n  RequestHand<PERSON>,\n  RequestHandlerExecutionResult,\n} from '../handlers/RequestHandler'\n\nexport interface HandlersExecutionResult {\n  handler: RequestHandler\n  parsedResult?: any\n  response?: Response\n}\n\nexport interface ResponseResolutionContext {\n  baseUrl?: string\n}\n\n/**\n * Executes the list of request handlers against the given request.\n * Returns the execution result object containing any matching request\n * handler and any mocked response it returned.\n */\nexport const executeHandlers = async <Handlers extends Array<RequestHandler>>({\n  request,\n  requestId,\n  handlers,\n  resolutionContext,\n}: {\n  request: Request\n  requestId: string\n  handlers: Handlers\n  resolutionContext?: ResponseResolutionContext\n}): Promise<HandlersExecutionResult | null> => {\n  let matchingHandler: RequestHandler | null = null\n  let result: RequestHandlerExecutionResult<any> | null = null\n\n  for (const handler of handlers) {\n    result = await handler.run({ request, requestId, resolutionContext })\n\n    // If the handler produces some result for this request,\n    // it automatically becomes matching.\n    if (result !== null) {\n      matchingHandler = handler\n    }\n\n    // Stop the lookup if this handler returns a mocked response.\n    // If it doesn't, it will still be considered the last matching\n    // handler until any of them returns a response. This way we can\n    // distinguish between fallthrough handlers without responses\n    // and the lack of a matching handler.\n    if (result?.response) {\n      break\n    }\n  }\n\n  if (matchingHandler) {\n    return {\n      handler: matchingHandler,\n      parsedResult: result?.parsedResult,\n      response: result?.response,\n    }\n  }\n\n  return null\n}\n", "import { toPublicUrl } from './toPublicUrl'\nimport { InternalError, devUtils } from '../internal/devUtils'\n\nexport interface UnhandledRequestPrint {\n  warning(): void\n  error(): void\n}\n\nexport type UnhandledRequestCallback = (\n  request: Request,\n  print: UnhandledRequestPrint,\n) => void\n\nexport type UnhandledRequestStrategy =\n  | 'bypass'\n  | 'warn'\n  | 'error'\n  | UnhandledRequestCallback\n\nexport async function onUnhandledRequest(\n  request: Request,\n  strategy: UnhandledRequestStrategy = 'warn',\n): Promise<void> {\n  const url = new URL(request.url)\n  const publicUrl = toPublicUrl(url) + url.search\n\n  const unhandledRequestMessage = `intercepted a request without a matching request handler:\\n\\n  \\u2022 ${request.method} ${publicUrl}\\n\\nIf you still wish to intercept this unhandled request, please create a request handler for it.\\nRead more: https://mswjs.io/docs/getting-started/mocks`\n\n  function applyStrategy(strategy: UnhandledRequestStrategy) {\n    switch (strategy) {\n      case 'error': {\n        // Print a developer-friendly error.\n        devUtils.error('Error: %s', unhandledRequestMessage)\n\n        // Throw an exception to halt request processing and not perform the original request.\n        throw new InternalError(\n          devUtils.formatMessage(\n            'Cannot bypass a request when using the \"error\" strategy for the \"onUnhandledRequest\" option.',\n          ),\n        )\n      }\n\n      case 'warn': {\n        devUtils.warn('Warning: %s', unhandledRequestMessage)\n        break\n      }\n\n      case 'bypass':\n        break\n\n      default:\n        throw new InternalError(\n          devUtils.formatMessage(\n            'Failed to react to an unhandled request: unknown strategy \"%s\". Please provide one of the supported strategies (\"bypass\", \"warn\", \"error\") or a custom callback function as the value of the \"onUnhandledRequest\" option.',\n            strategy,\n          ),\n        )\n    }\n  }\n\n  if (typeof strategy === 'function') {\n    strategy(request, {\n      warning: applyStrategy.bind(null, 'warn'),\n      error: applyStrategy.bind(null, 'error'),\n    })\n    return\n  }\n\n  /**\n   * @note Ignore \"file://\" requests.\n   * Those often are an implementation detail of modern tooling\n   * that fetches modules via HTTP. Developers don't issue those\n   * requests and so they mustn't be warned about them.\n   */\n  if (url.protocol === 'file:') {\n    return\n  }\n\n  applyStrategy(strategy)\n}\n", "import { store } from '@mswjs/cookies'\n\nexport function readResponseCookies(\n  request: Request,\n  response: Response,\n): void {\n  store.add({ ...request, url: request.url.toString() }, response)\n  store.persist()\n}\n", "import { until } from '@open-draft/until'\nimport { Emitter } from 'strict-event-emitter'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { LifeCycleEventsMap, SharedOptions } from '../sharedOptions'\nimport { RequiredDeep } from '../typeUtils'\nimport { HandlersExecutionResult, executeHandlers } from './executeHandlers'\nimport { onUnhandledRequest } from './request/onUnhandledRequest'\nimport { readResponseCookies } from './request/readResponseCookies'\n\nexport interface HandleRequestOptions {\n  /**\n   * `resolutionContext` is not part of the general public api\n   * but is exposed to aid in creating extensions like\n   * `@mswjs/http-middleware`.\n   */\n  resolutionContext?: {\n    /**\n     * A base url to use when resolving relative urls.\n     * @note This is primarily used by the `@mswjs/http-middleware`\n     * to resolve relative urls in the context of the running server\n     */\n    baseUrl?: string\n  }\n\n  /**\n   * Transforms a `MockedResponse` instance returned from a handler\n   * to a response instance supported by the lower tooling (i.e. interceptors).\n   */\n  transformResponse?(response: Response): Response\n\n  /**\n   * Invoked whenever a request is performed as-is.\n   */\n  onPassthroughResponse?(request: Request): void\n\n  /**\n   * Invoked when the mocked response is ready to be sent.\n   */\n  onMockedResponse?(\n    response: Response,\n    handler: RequiredDeep<HandlersExecutionResult>,\n  ): void\n}\n\nexport async function handleRequest(\n  request: Request,\n  requestId: string,\n  handlers: Array<RequestHandler>,\n  options: RequiredDeep<SharedOptions>,\n  emitter: Emitter<LifeCycleEventsMap>,\n  handleRequestOptions?: HandleRequestOptions,\n): Promise<Response | undefined> {\n  emitter.emit('request:start', { request, requestId })\n\n  // Perform bypassed requests (i.e. wrapped in \"bypass()\") as-is.\n  if (request.headers.get('x-msw-intention') === 'bypass') {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Resolve a mocked response from the list of request handlers.\n  const lookupResult = await until(() => {\n    return executeHandlers({\n      request,\n      requestId,\n      handlers,\n      resolutionContext: handleRequestOptions?.resolutionContext,\n    })\n  })\n\n  if (lookupResult.error) {\n    // Allow developers to react to unhandled exceptions in request handlers.\n    emitter.emit('unhandledException', {\n      error: lookupResult.error,\n      request,\n      requestId,\n    })\n    throw lookupResult.error\n  }\n\n  // If the handler lookup returned nothing, no request handler was found\n  // matching this request. Report the request as unhandled.\n  if (!lookupResult.data) {\n    await onUnhandledRequest(request, options.onUnhandledRequest)\n    emitter.emit('request:unhandled', { request, requestId })\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  const { response } = lookupResult.data\n\n  // When the handled request returned no mocked response, warn the developer,\n  // as it may be an oversight on their part. Perform the request as-is.\n  if (!response) {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Perform the request as-is when the developer explicitly returned \"req.passthrough()\".\n  // This produces no warning as the request was handled.\n  if (\n    response.status === 302 &&\n    response.headers.get('x-msw-intention') === 'passthrough'\n  ) {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Store all the received response cookies in the virtual cookie store.\n  readResponseCookies(request, response)\n\n  emitter.emit('request:match', { request, requestId })\n\n  const requiredLookupResult =\n    lookupResult.data as RequiredDeep<HandlersExecutionResult>\n\n  const transformedResponse =\n    handleRequestOptions?.transformResponse?.(response) ||\n    (response as any as Response)\n\n  handleRequestOptions?.onMockedResponse?.(\n    transformedResponse,\n    requiredLookupResult,\n  )\n\n  emitter.emit('request:end', { request, requestId })\n\n  return transformedResponse\n}\n", "import statuses from '@bundled-es-modules/statuses'\nimport type { HttpResponseInit } from '../../HttpResponse'\nimport { Headers as HeadersPolyfill } from 'headers-polyfill'\n\nconst { message } = statuses\n\nexport interface HttpResponseDecoratedInit extends HttpResponseInit {\n  status: number\n  statusText: string\n  headers: Headers\n}\n\nexport function normalizeResponseInit(\n  init: HttpResponseInit = {},\n): HttpResponseDecoratedInit {\n  const status = init?.status || 200\n  const statusText = init?.statusText || message[status] || ''\n  const headers = new Headers(init?.headers)\n\n  return {\n    ...init,\n    headers,\n    status,\n    statusText,\n  }\n}\n\nexport function decorateResponse(\n  response: Response,\n  init: HttpResponseDecoratedInit,\n): Response {\n  // Allow to mock the response type.\n  if (init.type) {\n    Object.defineProperty(response, 'type', {\n      value: init.type,\n      enumerable: true,\n      writable: false,\n    })\n  }\n\n  // Cookie forwarding is only relevant in the browser.\n  if (typeof document !== 'undefined') {\n    // Write the mocked response cookies to the document.\n    // Use `headers-polyfill` to get the Set-Cookie header value correctly.\n    // This is an alternative until TypeScript 5.2\n    // and Node.js v20 become the minimum supported version\n    // and getSetCookie in Headers can be used directly.\n    const responseCookies = HeadersPolyfill.prototype.getSetCookie.call(\n      init.headers,\n    )\n\n    for (const cookieString of responseCookies) {\n      // No need to parse the cookie headers because it's defined\n      // as the valid cookie string to begin with.\n      document.cookie = cookieString\n    }\n  }\n\n  return response\n}\n", "import type { DefaultBodyType, JsonBodyType } from './handlers/RequestHandler'\nimport type { NoInfer } from './typeUtils'\nimport {\n  decorateResponse,\n  normalizeResponseInit,\n} from './utils/HttpResponse/decorators'\n\nexport interface HttpResponseInit extends ResponseInit {\n  type?: ResponseType\n}\n\ndeclare const bodyType: unique symbol\n\nexport interface StrictRequest<BodyType extends DefaultBodyType>\n  extends Request {\n  json(): Promise<BodyType>\n}\n\n/**\n * Opaque `Response` type that supports strict body type.\n */\nexport interface StrictResponse<BodyType extends DefaultBodyType>\n  extends Response {\n  readonly [bodyType]: BodyType\n}\n\n/**\n * A drop-in replacement for the standard `Response` class\n * to allow additional features, like mocking the response `Set-Cookie` header.\n *\n * @example\n * new HttpResponse('Hello world', { status: 201 })\n * HttpResponse.json({ name: 'John' })\n * HttpResponse.formData(form)\n *\n * @see {@link https://mswjs.io/docs/api/http-response `HttpResponse` API reference}\n */\nexport class HttpResponse extends Response {\n  constructor(body?: BodyInit | null, init?: HttpResponseInit) {\n    const responseInit = normalizeResponseInit(init)\n    super(body, responseInit)\n    decorateResponse(this, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"text/plain\"` body.\n   * @example\n   * HttpResponse.text('hello world')\n   * HttpResponse.text('Error', { status: 500 })\n   */\n  static text<BodyType extends string>(\n    body?: NoInfer<BodyType> | null,\n    init?: HttpResponseInit,\n  ): StrictResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/plain')\n    }\n\n    // Automatically set the \"Content-Length\" response header\n    // for non-empty text responses. This enforces consistency and\n    // brings mocked responses closer to production.\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        body ? new Blob([body]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(body, responseInit) as StrictResponse<BodyType>\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/json\"` body.\n   * @example\n   * HttpResponse.json({ firstName: 'John' })\n   * HttpResponse.json({ error: 'Not Authorized' }, { status: 401 })\n   */\n  static json<BodyType extends JsonBodyType>(\n    body?: NoInfer<BodyType> | null,\n    init?: HttpResponseInit,\n  ): StrictResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'application/json')\n    }\n\n    /**\n     * @note TypeScript is incorrect here.\n     * Stringifying undefined will return undefined.\n     */\n    const responseText = JSON.stringify(body) as string | undefined\n\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        responseText ? new Blob([responseText]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(\n      responseText,\n      responseInit,\n    ) as StrictResponse<BodyType>\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/xml\"` body.\n   * @example\n   * HttpResponse.xml(`<user name=\"John\" />`)\n   * HttpResponse.xml(`<article id=\"abc-123\" />`, { status: 201 })\n   */\n  static xml<BodyType extends string>(\n    body?: BodyType | null,\n    init?: HttpResponseInit,\n  ): Response {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/xml')\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with an `ArrayBuffer` body.\n   * @example\n   * const buffer = new ArrayBuffer(3)\n   * const view = new Uint8Array(buffer)\n   * view.set([1, 2, 3])\n   *\n   * HttpResponse.arrayBuffer(buffer)\n   */\n  static arrayBuffer(body?: ArrayBuffer, init?: HttpResponseInit): Response {\n    const responseInit = normalizeResponseInit(init)\n\n    if (body) {\n      responseInit.headers.set('Content-Length', body.byteLength.toString())\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `FormData` body.\n   * @example\n   * const data = new FormData()\n   * data.set('name', 'Alice')\n   *\n   * HttpResponse.formData(data)\n   */\n  static formData(body?: FormData, init?: HttpResponseInit): Response {\n    return new HttpResponse(body, normalizeResponseInit(init))\n  }\n}\n", "import { checkGlobals } from './utils/internal/checkGlobals'\n\nexport { SetupApi } from './SetupApi'\n\n/* Request handlers */\nexport { RequestHandler } from './handlers/RequestHandler'\nexport { http } from './http'\nexport { HttpHandler, HttpMethods } from './handlers/HttpHandler'\nexport { graphql } from './graphql'\nexport { GraphQLHandler } from './handlers/GraphQLHandler'\n\n/* Utils */\nexport { matchRequestUrl } from './utils/matching/matchRequestUrl'\nexport * from './utils/handleRequest'\nexport { getResponse } from './getResponse'\nexport { cleanUrl } from './utils/url/cleanUrl'\n\n/**\n * Type definitions.\n */\n\nexport type { SharedOptions, LifeCycleEventsMap } from './sharedOptions'\n\nexport type {\n  ResponseResolver,\n  ResponseResolverReturnType,\n  AsyncResponseResolverReturnType,\n  RequestHandlerOptions,\n  DefaultBodyType,\n  DefaultRequestMultipartBody,\n  JsonBodyType,\n} from './handlers/RequestHandler'\n\nexport type {\n  RequestQuery,\n  HttpRequestParsedResult,\n} from './handlers/HttpHandler'\nexport type { HttpRequestHandler, HttpResponseResolver } from './http'\n\nexport type {\n  GraphQLQuery,\n  GraphQLVariables,\n  GraphQLRequestBody,\n  GraphQLJsonRequestBody,\n} from './handlers/GraphQLHandler'\nexport type { GraphQLRequestHandler, GraphQLResponseResolver } from './graphql'\n\nexport type { Path, PathParams, Match } from './utils/matching/matchRequestUrl'\nexport type { ParsedGraphQLRequest } from './utils/internal/parseGraphQLRequest'\n\nexport * from './HttpResponse'\nexport * from './delay'\nexport { bypass } from './bypass'\nexport { passthrough } from './passthrough'\n\n// Validate environmental globals before executing any code.\n// This ensures that the library gives user-friendly errors\n// when ran in the environments that require additional polyfills\n// from the end user.\ncheckGlobals()\n", "import { AsyncLocalStorage } from 'node:async_hooks'\nimport { ClientRequestInterceptor } from '@mswjs/interceptors/ClientRequest'\nimport { XMLHttpRequestInterceptor } from '@mswjs/interceptors/XMLHttpRequest'\nimport { FetchInterceptor } from '@mswjs/interceptors/fetch'\nimport { HandlersController } from '~/core/SetupApi'\nimport type { RequestHandler } from '~/core/handlers/RequestHandler'\nimport type { SetupServer } from './glossary'\nimport { SetupServerCommonApi } from './SetupServerCommonApi'\n\nconst store = new AsyncLocalStorage<RequestHandlersContext>()\n\ntype RequestHandlersContext = {\n  initialHandlers: Array<RequestHandler>\n  handlers: Array<RequestHandler>\n}\n\n/**\n * A handlers controller that utilizes `AsyncLocalStorage` in Node.js\n * to prevent the request handlers list from being a shared state\n * across mutliple tests.\n */\nclass AsyncHandlersController implements HandlersController {\n  private rootContext: RequestHandlersContext\n\n  constructor(initialHandlers: Array<RequestHandler>) {\n    this.rootContext = { initialHandlers, handlers: [] }\n  }\n\n  get context(): RequestHandlersContext {\n    return store.getStore() || this.rootContext\n  }\n\n  public prepend(runtimeHandlers: Array<RequestHandler>) {\n    this.context.handlers.unshift(...runtimeHandlers)\n  }\n\n  public reset(nextHandlers: Array<RequestHandler>) {\n    const context = this.context\n    context.handlers = []\n    context.initialHandlers =\n      nextHandlers.length > 0 ? nextHandlers : context.initialHandlers\n  }\n\n  public currentHandlers(): Array<RequestHandler> {\n    const { initialHandlers, handlers } = this.context\n    return handlers.concat(initialHandlers)\n  }\n}\n\nexport class SetupServerApi\n  extends SetupServerCommonApi\n  implements SetupServer\n{\n  constructor(handlers: Array<RequestHandler>) {\n    super(\n      [ClientRequestInterceptor, XMLHttpRequestInterceptor, FetchInterceptor],\n      handlers,\n    )\n\n    this.handlersController = new AsyncHandlersController(handlers)\n  }\n\n  public boundary<Args extends Array<any>, R>(\n    callback: (...args: Args) => R,\n  ): (...args: Args) => R {\n    return (...args: Args): R => {\n      return store.run<any, any>(\n        {\n          initialHandlers: this.handlersController.currentHandlers(),\n          handlers: [],\n        },\n        callback,\n        ...args,\n      )\n    }\n  }\n\n  public close(): void {\n    super.close()\n    store.disable()\n  }\n}\n", "/**\n * @note This API is extended by both \"msw/node\" and \"msw/native\"\n * so be minding about the things you import!\n */\nimport type { RequiredDeep } from 'type-fest'\nimport { invariant } from 'outvariant'\nimport {\n  BatchInterceptor,\n  InterceptorReadyState,\n  type HttpRequestEventMap,\n  type Interceptor,\n} from '@mswjs/interceptors'\nimport type { LifeCycleEventsMap, SharedOptions } from '~/core/sharedOptions'\nimport { SetupApi } from '~/core/SetupApi'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport type { RequestHandler } from '~/core/handlers/RequestHandler'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport { InternalError, devUtils } from '~/core/utils/internal/devUtils'\nimport type { SetupServerCommon } from './glossary'\n\nexport const DEFAULT_LISTEN_OPTIONS: RequiredDeep<SharedOptions> = {\n  onUnhandledRequest: 'warn',\n}\n\nexport class SetupServerCommonApi\n  extends SetupApi<LifeCycleEventsMap>\n  implements SetupServerCommon\n{\n  protected readonly interceptor: BatchInterceptor<\n    Array<Interceptor<HttpRequestEventMap>>,\n    HttpRequestEventMap\n  >\n  private resolvedOptions: RequiredDeep<SharedOptions>\n\n  constructor(\n    interceptors: Array<{ new (): Interceptor<HttpRequestEventMap> }>,\n    handlers: Array<RequestHandler>,\n  ) {\n    super(...handlers)\n\n    this.interceptor = new BatchInterceptor({\n      name: 'setup-server',\n      interceptors: interceptors.map((Interceptor) => new Interceptor()),\n    })\n\n    this.resolvedOptions = {} as RequiredDeep<SharedOptions>\n\n    this.init()\n  }\n\n  /**\n   * Subscribe to all requests that are using the interceptor object\n   */\n  private init(): void {\n    this.interceptor.on('request', async ({ request, requestId }) => {\n      const response = await handleRequest(\n        request,\n        requestId,\n        this.handlersController.currentHandlers(),\n        this.resolvedOptions,\n        this.emitter,\n      )\n\n      if (response) {\n        request.respondWith(response)\n      }\n\n      return\n    })\n\n    this.interceptor.on('unhandledException', ({ error }) => {\n      if (error instanceof InternalError) {\n        throw error\n      }\n    })\n\n    this.interceptor.on(\n      'response',\n      ({ response, isMockedResponse, request, requestId }) => {\n        this.emitter.emit(\n          isMockedResponse ? 'response:mocked' : 'response:bypass',\n          {\n            response,\n            request,\n            requestId,\n          },\n        )\n      },\n    )\n  }\n\n  public listen(options: Partial<SharedOptions> = {}): void {\n    this.resolvedOptions = mergeRight(\n      DEFAULT_LISTEN_OPTIONS,\n      options,\n    ) as RequiredDeep<SharedOptions>\n\n    // Apply the interceptor when starting the server.\n    this.interceptor.apply()\n\n    this.subscriptions.push(() => {\n      this.interceptor.dispose()\n    })\n\n    // Assert that the interceptor has been applied successfully.\n    // Also guards us from forgetting to call \"interceptor.apply()\"\n    // as a part of the \"listen\" method.\n    invariant(\n      [InterceptorReadyState.APPLYING, InterceptorReadyState.APPLIED].includes(\n        this.interceptor.readyState,\n      ),\n      devUtils.formatMessage(\n        'Failed to start \"setupServer\": the interceptor failed to apply. This is likely an issue with the library and you should report it at \"%s\".',\n      ),\n      'https://github.com/mswjs/msw/issues/new/choose',\n    )\n  }\n\n  public close(): void {\n    this.dispose()\n  }\n}\n", "import type { <PERSON>questHand<PERSON> } from '~/core/handlers/RequestHandler'\nimport { SetupServerApi } from './SetupServerApi'\n\n/**\n * Sets up a requests interception in Node.js with the given request handlers.\n * @param {RequestHandler[]} handlers List of request handlers.\n *\n * @see {@link https://mswjs.io/docs/api/setup-server `setupServer()` API reference}\n */\nexport const setupServer = (\n  ...handlers: Array<RequestHandler>\n): SetupServerApi => {\n  return new SetupServerApi(handlers)\n}\n", "export type PromiseState = 'pending' | 'fulfilled' | 'rejected'\n\nexport type Executor<Value> = ConstructorParameters<typeof Promise<Value>>[0]\nexport type ResolveFunction<Value> = Parameters<Executor<Value>>[0]\nexport type RejectFunction<Reason> = Parameters<Executor<Reason>>[1]\n\nexport type DeferredPromiseExecutor<Input = never, Output = Input> = {\n  (resolve?: ResolveFunction<Input>, reject?: RejectFunction<any>): void\n\n  resolve: ResolveFunction<Input>\n  reject: RejectFunction<any>\n  result?: Output\n  state: PromiseState\n  rejectionReason?: unknown\n}\nexport function createDeferredExecutor<\n  Input = never,\n  Output = Input\n>(): DeferredPromiseExecutor<Input, Output> {\n  const executor = <DeferredPromiseExecutor<Input, Output>>((\n    resolve,\n    reject\n  ) => {\n    executor.state = 'pending'\n\n    executor.resolve = (data) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      executor.result = data as Output\n\n      const onFulfilled = <Value>(value: Value) => {\n        executor.state = 'fulfilled'\n        return value\n      }\n\n      return resolve(\n        data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled)\n      )\n    }\n\n    executor.reject = (reason) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      queueMicrotask(() => {\n        executor.state = 'rejected'\n      })\n\n      return reject((executor.rejectionReason = reason))\n    }\n  })\n\n  return executor\n}\n", "import {\n  type Executor,\n  type RejectFunction,\n  type ResolveFunction,\n  type DeferredPromiseExecutor,\n  createDeferredExecutor,\n} from './createDeferredExecutor'\n\nexport class DeferredPromise<Input, Output = Input> extends Promise<Input> {\n  #executor: DeferredPromiseExecutor\n\n  public resolve: ResolveFunction<Output>\n  public reject: RejectFunction<Output>\n\n  constructor(executor: Executor<Input> | null = null) {\n    const deferredExecutor = createDeferredExecutor()\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject)\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject)\n    })\n\n    this.#executor = deferredExecutor\n    this.resolve = this.#executor.resolve\n    this.reject = this.#executor.reject\n  }\n\n  public get state() {\n    return this.#executor.state\n  }\n\n  public get rejectionReason() {\n    return this.#executor.rejectionReason\n  }\n\n  public then<ThenResult = Input, CatchResult = never>(\n    onFulfilled?: (value: Input) => ThenResult | PromiseLike<ThenResult>,\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.then(onFulfilled, onRejected))\n  }\n\n  public catch<CatchResult = never>(\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.catch(onRejected))\n  }\n\n  public finally(onfinally?: () => void | Promise<any>) {\n    return this.#decorate(super.finally(onfinally))\n  }\n\n  #decorate<ChildInput>(\n    promise: Promise<ChildInput>\n  ): DeferredPromise<ChildInput, Output> {\n    return Object.defineProperties(promise, {\n      resolve: { configurable: true, value: this.resolve },\n      reject: { configurable: true, value: this.reject },\n    }) as DeferredPromise<ChildInput, Output>\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\n\nexport class RequestController {\n  public responsePromise: DeferredPromise<Response | undefined>\n\n  constructor(protected request: Request) {\n    this.responsePromise = new DeferredPromise()\n  }\n\n  public respondWith(response?: Response): void {\n    invariant(\n      this.responsePromise.state === 'pending',\n      'Failed to respond to \"%s %s\" request: the \"request\" event has already been responded to.',\n      this.request.method,\n      this.request.url\n    )\n\n    this.responsePromise.resolve(response)\n  }\n}\n", "import { RequestController } from './RequestController'\n\nexport type InteractiveRequest = globalThis.Request & {\n  respondWith: RequestController['respondWith']\n}\n\nexport function toInteractiveRequest(request: Request): {\n  interactiveRequest: InteractiveRequest\n  requestController: RequestController\n} {\n  const requestController = new RequestController(request)\n\n  Reflect.set(\n    request,\n    'respondWith',\n    requestController.respondWith.bind(requestController)\n  )\n\n  return {\n    interactiveRequest: request as InteractiveRequest,\n    requestController,\n  }\n}\n", "import { Emitter, EventMap } from 'strict-event-emitter'\n\n/**\n * Emits an event on the given emitter but executes\n * the listeners sequentially. This accounts for asynchronous\n * listeners (e.g. those having \"sleep\" and handling the request).\n */\nexport async function emitAsync<\n  Events extends EventMap,\n  EventName extends keyof Events\n>(\n  emitter: Emitter<Events>,\n  eventName: EventName,\n  ...data: Events[EventName]\n): Promise<void> {\n  const listners = emitter.listeners(eventName)\n\n  if (listners.length === 0) {\n    return\n  }\n\n  for (const listener of listners) {\n    await listener.apply(emitter, data)\n  }\n}\n", "import http from 'http'\nimport https from 'https'\nimport type { Emitter } from 'strict-event-emitter'\nimport { HttpRequestEventMap } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { get } from './http.get'\nimport { request } from './http.request'\nimport { NodeClientOptions, Protocol } from './NodeClientRequest'\n\nexport type ClientRequestEmitter = Emitter<HttpRequestEventMap>\n\nexport type ClientRequestModules = Map<Protocol, typeof http | typeof https>\n\n/**\n * Intercept requests made via the `ClientRequest` class.\n * Such requests include `http.get`, `https.request`, etc.\n */\nexport class ClientRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static interceptorSymbol = Symbol('http')\n  private modules: ClientRequestModules\n\n  constructor() {\n    super(ClientRequestInterceptor.interceptorSymbol)\n\n    this.modules = new Map()\n    this.modules.set('http', http)\n    this.modules.set('https', https)\n  }\n\n  protected setup(): void {\n    const logger = this.logger.extend('setup')\n\n    for (const [protocol, requestModule] of this.modules) {\n      const { request: pureRequest, get: pureGet } = requestModule\n\n      this.subscriptions.push(() => {\n        requestModule.request = pureRequest\n        requestModule.get = pureGet\n\n        logger.info('native \"%s\" module restored!', protocol)\n      })\n\n      const options: NodeClientOptions = {\n        emitter: this.emitter,\n        logger: this.logger,\n      }\n\n      // @ts-ignore\n      requestModule.request =\n        // Force a line break.\n        request(protocol, options)\n\n      // @ts-ignore\n      requestModule.get =\n        // Force a line break.\n        get(protocol, options)\n\n      logger.info('native \"%s\" module patched!', protocol)\n    }\n  }\n}\n", "import { ClientRequest, IncomingMessage, STATUS_CODES } from 'node:http'\nimport type { Logger } from '@open-draft/logger'\nimport { until } from '@open-draft/until'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport type { ClientRequestEmitter } from '.'\nimport {\n  ClientRequestEndCallback,\n  ClientRequestEndChunk,\n  normalizeClientRequestEndArgs,\n} from './utils/normalizeClientRequestEndArgs'\nimport { NormalizedClientRequestArgs } from './utils/normalizeClientRequestArgs'\nimport {\n  ClientRequestWriteArgs,\n  normalizeClientRequestWriteArgs,\n} from './utils/normalizeClientRequestWriteArgs'\nimport { cloneIncomingMessage } from './utils/cloneIncomingMessage'\nimport { createResponse } from './utils/createResponse'\nimport { createRequest } from './utils/createRequest'\nimport { toInteractiveRequest } from '../../utils/toInteractiveRequest'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { getRawFetchHeaders } from '../../utils/getRawFetchHeaders'\nimport { isNodeLikeError } from '../../utils/isNodeLikeError'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { createRequestId } from '../../createRequestId'\nimport {\n  createServerErrorResponse,\n  isResponseError,\n} from '../../utils/responseUtils'\n\nexport type Protocol = 'http' | 'https'\n\nenum HttpClientInternalState {\n  // Have the concept of an idle request because different\n  // request methods can kick off request sending\n  // (e.g. \".end()\" or \".flushHeaders()\").\n  Idle,\n  Sending,\n  Sent,\n  MockLookupStart,\n  MockLookupEnd,\n  ResponseReceived,\n}\n\nexport interface NodeClientOptions {\n  emitter: ClientRequestEmitter\n  logger: Logger\n}\n\nexport class NodeClientRequest extends ClientRequest {\n  /**\n   * The list of internal Node.js errors to suppress while\n   * using the \"mock\" response source.\n   */\n  static suppressErrorCodes = [\n    'ENOTFOUND',\n    'ECONNREFUSED',\n    'ECONNRESET',\n    'EAI_AGAIN',\n    'ENETUNREACH',\n    'EHOSTUNREACH',\n  ]\n\n  /**\n   * Internal state of the request.\n   */\n  private state: HttpClientInternalState\n  private responseType?: 'mock' | 'passthrough'\n  private response: IncomingMessage\n  private emitter: ClientRequestEmitter\n  private logger: Logger\n  private chunks: Array<{\n    chunk?: string | Buffer\n    encoding?: BufferEncoding\n  }> = []\n  private capturedError?: NodeJS.ErrnoException\n\n  public url: URL\n  public requestBuffer: Buffer | null\n\n  constructor(\n    [url, requestOptions, callback]: NormalizedClientRequestArgs,\n    options: NodeClientOptions\n  ) {\n    super(requestOptions, callback)\n\n    this.logger = options.logger.extend(\n      `request ${requestOptions.method} ${url.href}`\n    )\n\n    this.logger.info('constructing ClientRequest using options:', {\n      url,\n      requestOptions,\n      callback,\n    })\n\n    this.state = HttpClientInternalState.Idle\n    this.url = url\n    this.emitter = options.emitter\n\n    // Set request buffer to null by default so that GET/HEAD requests\n    // without a body wouldn't suddenly get one.\n    this.requestBuffer = null\n\n    // Construct a mocked response message.\n    this.response = new IncomingMessage(this.socket!)\n  }\n\n  private writeRequestBodyChunk(\n    chunk: string | Buffer | null,\n    encoding?: BufferEncoding\n  ): void {\n    if (chunk == null) {\n      return\n    }\n\n    if (this.requestBuffer == null) {\n      this.requestBuffer = Buffer.from([])\n    }\n\n    const resolvedChunk = Buffer.isBuffer(chunk)\n      ? chunk\n      : Buffer.from(chunk, encoding)\n\n    this.requestBuffer = Buffer.concat([this.requestBuffer, resolvedChunk])\n  }\n\n  write(...args: ClientRequestWriteArgs): boolean {\n    const [chunk, encoding, callback] = normalizeClientRequestWriteArgs(args)\n    this.logger.info('write:', { chunk, encoding, callback })\n    this.chunks.push({ chunk, encoding })\n\n    // Write each request body chunk to the internal buffer.\n    this.writeRequestBodyChunk(chunk, encoding)\n\n    this.logger.info(\n      'chunk successfully stored!',\n      this.requestBuffer?.byteLength\n    )\n\n    /**\n     * Prevent invoking the callback if the written chunk is empty.\n     * @see https://nodejs.org/api/http.html#requestwritechunk-encoding-callback\n     */\n    if (!chunk || chunk.length === 0) {\n      this.logger.info('written chunk is empty, skipping callback...')\n    } else {\n      callback?.()\n    }\n\n    // Do not write the request body chunks to prevent\n    // the Socket from sending data to a potentially existing\n    // server when there is a mocked response defined.\n    return true\n  }\n\n  end(...args: any): this {\n    this.logger.info('end', args)\n\n    const requestId = createRequestId()\n\n    const [chunk, encoding, callback] = normalizeClientRequestEndArgs(...args)\n    this.logger.info('normalized arguments:', { chunk, encoding, callback })\n\n    // Write the last request body chunk passed to the \"end()\" method.\n    this.writeRequestBodyChunk(chunk, encoding || undefined)\n\n    /**\n     * @note Mark the request as sent immediately when invoking \".end()\".\n     * In Node.js, calling \".end()\" will flush the remaining request body\n     * and mark the request as \"finished\" immediately (\"end\" is synchronous)\n     * but we delegate that property update to:\n     *\n     * - respondWith(), in the case of mocked responses;\n     * - super.end(), in the case of bypassed responses.\n     *\n     * For that reason, we have to keep an internal flag for a finished request.\n     */\n    this.state = HttpClientInternalState.Sent\n\n    const capturedRequest = createRequest(this)\n    const { interactiveRequest, requestController } =\n      toInteractiveRequest(capturedRequest)\n\n    /**\n     * @todo Remove this modification of the original request\n     * and expose the controller alongside it in the \"request\"\n     * listener argument.\n     */\n    Object.defineProperty(capturedRequest, 'respondWith', {\n      value: requestController.respondWith.bind(requestController),\n    })\n\n    // Prevent handling this request if it has already been handled\n    // in another (parent) interceptor (like XMLHttpRequest -> ClientRequest).\n    // That means some interceptor up the chain has concluded that\n    // this request must be performed as-is.\n    if (this.hasHeader(INTERNAL_REQUEST_ID_HEADER_NAME)) {\n      this.removeHeader(INTERNAL_REQUEST_ID_HEADER_NAME)\n      return this.passthrough(chunk, encoding, callback)\n    }\n\n    // Add the last \"request\" listener that always resolves\n    // the pending response Promise. This way if the consumer\n    // hasn't handled the request themselves, we will prevent\n    // the response Promise from pending indefinitely.\n    this.emitter.once('request', ({ requestId: pendingRequestId }) => {\n      /**\n       * @note Ignore request events emitted by irrelevant\n       * requests. This happens when response patching.\n       */\n      if (pendingRequestId !== requestId) {\n        return\n      }\n\n      if (requestController.responsePromise.state === 'pending') {\n        this.logger.info(\n          'request has not been handled in listeners, executing fail-safe listener...'\n        )\n\n        requestController.responsePromise.resolve(undefined)\n      }\n    })\n\n    // Execute the resolver Promise like a side-effect.\n    // Node.js 16 forces \"ClientRequest.end\" to be synchronous and return \"this\".\n    until<unknown, Response | undefined>(async () => {\n      // Notify the interceptor about the request.\n      // This will call any \"request\" listeners the users have.\n      this.logger.info(\n        'emitting the \"request\" event for %d listener(s)...',\n        this.emitter.listenerCount('request')\n      )\n\n      this.state = HttpClientInternalState.MockLookupStart\n\n      await emitAsync(this.emitter, 'request', {\n        request: interactiveRequest,\n        requestId,\n      })\n\n      this.logger.info('all \"request\" listeners done!')\n\n      const mockedResponse = await requestController.responsePromise\n      this.logger.info('event.respondWith called with:', mockedResponse)\n\n      return mockedResponse\n    }).then((resolverResult) => {\n      this.logger.info('the listeners promise awaited!')\n\n      this.state = HttpClientInternalState.MockLookupEnd\n\n      /**\n       * @fixme We are in the \"end()\" method that still executes in parallel\n       * to our mocking logic here. This can be solved by migrating to the\n       * Proxy-based approach and deferring the passthrough \"end()\" properly.\n       * @see https://github.com/mswjs/interceptors/issues/346\n       */\n      if (!this.headersSent) {\n        // Forward any request headers that the \"request\" listener\n        // may have modified before proceeding with this request.\n        for (const [headerName, headerValue] of capturedRequest.headers) {\n          this.setHeader(headerName, headerValue)\n        }\n      }\n\n      if (resolverResult.error) {\n        this.logger.info(\n          'unhandled resolver exception, coercing to an error response...',\n          resolverResult.error\n        )\n\n        // Handle thrown Response instances.\n        if (resolverResult.error instanceof Response) {\n          // Treat thrown Response.error() as a request error.\n          if (isResponseError(resolverResult.error)) {\n            this.logger.info(\n              'received network error response, erroring request...'\n            )\n\n            this.errorWith(new TypeError('Network error'))\n          } else {\n            // Handle a thrown Response as a mocked response.\n            this.respondWith(resolverResult.error)\n          }\n\n          return\n        }\n\n        // Allow throwing Node.js-like errors, like connection rejection errors.\n        // Treat them as request errors.\n        if (isNodeLikeError(resolverResult.error)) {\n          this.errorWith(resolverResult.error)\n          return this\n        }\n\n        until(async () => {\n          if (this.emitter.listenerCount('unhandledException') > 0) {\n            // Emit the \"unhandledException\" event to allow the client\n            // to opt-out from the default handling of exceptions\n            // as 500 error responses.\n            await emitAsync(this.emitter, 'unhandledException', {\n              error: resolverResult.error,\n              request: capturedRequest,\n              requestId,\n              controller: {\n                respondWith: this.respondWith.bind(this),\n                errorWith: this.errorWith.bind(this),\n              },\n            })\n\n            // If after the \"unhandledException\" listeners are done,\n            // the request is either not writable (was mocked) or\n            // destroyed (has errored), do nothing.\n            if (this.writableEnded || this.destroyed) {\n              return\n            }\n          }\n\n          // Unhandled exceptions in the request listeners are\n          // synonymous to unhandled exceptions on the server.\n          // Those are represented as 500 error responses.\n          this.respondWith(createServerErrorResponse(resolverResult.error))\n        })\n\n        return this\n      }\n\n      const mockedResponse = resolverResult.data\n\n      if (mockedResponse) {\n        this.logger.info(\n          'received mocked response:',\n          mockedResponse.status,\n          mockedResponse.statusText\n        )\n\n        /**\n         * @note Ignore this request being destroyed by TLS in Node.js\n         * due to connection errors.\n         */\n        this.destroyed = false\n\n        // Handle mocked \"Response.error\" network error responses.\n        if (isResponseError(mockedResponse)) {\n          this.logger.info(\n            'received network error response, erroring request...'\n          )\n\n          /**\n           * There is no standardized error format for network errors\n           * in Node.js. Instead, emit a generic TypeError.\n           */\n          this.errorWith(new TypeError('Network error'))\n\n          return this\n        }\n\n        const responseClone = mockedResponse.clone()\n\n        this.respondWith(mockedResponse)\n        this.logger.info(\n          mockedResponse.status,\n          mockedResponse.statusText,\n          '(MOCKED)'\n        )\n\n        callback?.()\n\n        this.logger.info('emitting the custom \"response\" event...')\n        this.emitter.emit('response', {\n          response: responseClone,\n          isMockedResponse: true,\n          request: capturedRequest,\n          requestId,\n        })\n\n        this.logger.info('request (mock) is completed')\n\n        return this\n      }\n\n      this.logger.info('no mocked response received!')\n\n      this.once('response-internal', (message: IncomingMessage) => {\n        this.logger.info(message.statusCode, message.statusMessage)\n        this.logger.info('original response headers:', message.headers)\n\n        this.logger.info('emitting the custom \"response\" event...')\n        this.emitter.emit('response', {\n          response: createResponse(message),\n          isMockedResponse: false,\n          request: capturedRequest,\n          requestId,\n        })\n      })\n\n      return this.passthrough(chunk, encoding, callback)\n    })\n\n    return this\n  }\n\n  emit(event: string, ...data: any[]) {\n    this.logger.info('emit: %s', event)\n\n    if (event === 'response') {\n      this.logger.info('found \"response\" event, cloning the response...')\n\n      try {\n        /**\n         * Clone the response object when emitting the \"response\" event.\n         * This prevents the response body stream from locking\n         * and allows reading it twice:\n         * 1. Internal \"response\" event from the observer.\n         * 2. Any external response body listeners.\n         * @see https://github.com/mswjs/interceptors/issues/161\n         */\n        const response = data[0] as IncomingMessage\n        const firstClone = cloneIncomingMessage(response)\n        const secondClone = cloneIncomingMessage(response)\n\n        this.emit('response-internal', secondClone)\n\n        this.logger.info(\n          'response successfully cloned, emitting \"response\" event...'\n        )\n        return super.emit(event, firstClone, ...data.slice(1))\n      } catch (error) {\n        this.logger.info('error when cloning response:', error)\n        return super.emit(event, ...data)\n      }\n    }\n\n    if (event === 'error') {\n      const error = data[0] as NodeJS.ErrnoException\n      const errorCode = error.code || ''\n\n      this.logger.info('error:\\n', error)\n\n      // Suppress only specific Node.js connection errors.\n      if (NodeClientRequest.suppressErrorCodes.includes(errorCode)) {\n        // Until we aren't sure whether the request will be\n        // passthrough, capture the first emitted connection\n        // error in case we have to replay it for this request.\n        if (this.state < HttpClientInternalState.MockLookupEnd) {\n          if (!this.capturedError) {\n            this.capturedError = error\n            this.logger.info('captured the first error:', this.capturedError)\n          }\n          return false\n        }\n\n        // Ignore any connection errors once we know the request\n        // has been resolved with a mocked response. Don't capture\n        // them as they won't ever be replayed.\n        if (\n          this.state === HttpClientInternalState.ResponseReceived &&\n          this.responseType === 'mock'\n        ) {\n          return false\n        }\n      }\n    }\n\n    return super.emit(event, ...data)\n  }\n\n  /**\n   * Performs the intercepted request as-is.\n   * Replays the captured request body chunks,\n   * still emits the internal events, and wraps\n   * up the request with `super.end()`.\n   */\n  private passthrough(\n    chunk: ClientRequestEndChunk | null,\n    encoding?: BufferEncoding | null,\n    callback?: ClientRequestEndCallback | null\n  ): this {\n    this.state = HttpClientInternalState.ResponseReceived\n    this.responseType = 'passthrough'\n\n    // Propagate previously captured errors.\n    // For example, a ECONNREFUSED error when connecting to a non-existing host.\n    if (this.capturedError) {\n      this.emit('error', this.capturedError)\n      return this\n    }\n\n    this.logger.info('writing request chunks...', this.chunks)\n\n    // Write the request body chunks in the order of \".write()\" calls.\n    // Note that no request body has been written prior to this point\n    // in order to prevent the Socket to communicate with a potentially\n    // existing server.\n    for (const { chunk, encoding } of this.chunks) {\n      if (encoding) {\n        super.write(chunk, encoding)\n      } else {\n        super.write(chunk)\n      }\n    }\n\n    this.once('error', (error) => {\n      this.logger.info('original request error:', error)\n    })\n\n    this.once('abort', () => {\n      this.logger.info('original request aborted!')\n    })\n\n    this.once('response-internal', (message: IncomingMessage) => {\n      this.logger.info(message.statusCode, message.statusMessage)\n      this.logger.info('original response headers:', message.headers)\n    })\n\n    this.logger.info('performing original request...')\n\n    // This call signature is way too dynamic.\n    return super.end(...[chunk, encoding as any, callback].filter(Boolean))\n  }\n\n  /**\n   * Responds to this request instance using a mocked response.\n   */\n  private respondWith(mockedResponse: Response): void {\n    this.logger.info('responding with a mocked response...', mockedResponse)\n\n    this.state = HttpClientInternalState.ResponseReceived\n    this.responseType = 'mock'\n\n    /**\n     * Mark the request as finished right before streaming back the response.\n     * This is not entirely conventional but this will allow the consumer to\n     * modify the outoging request in the interceptor.\n     *\n     * The request is finished when its headers and bodies have been sent.\n     * @see https://nodejs.org/api/http.html#event-finish\n     */\n    Object.defineProperties(this, {\n      writableFinished: { value: true },\n      writableEnded: { value: true },\n    })\n    this.emit('finish')\n\n    const { status, statusText, headers, body } = mockedResponse\n    this.response.statusCode = status\n    this.response.statusMessage = statusText || STATUS_CODES[status]\n\n    // Try extracting the raw headers from the headers instance.\n    // If not possible, fallback to the headers instance as-is.\n    const rawHeaders = getRawFetchHeaders(headers) || headers\n\n    if (rawHeaders) {\n      this.response.headers = {}\n\n      rawHeaders.forEach((headerValue, headerName) => {\n        /**\n         * @note Make sure that multi-value headers are appended correctly.\n         */\n        this.response.rawHeaders.push(headerName, headerValue)\n\n        const insensitiveHeaderName = headerName.toLowerCase()\n        const prevHeaders = this.response.headers[insensitiveHeaderName]\n        this.response.headers[insensitiveHeaderName] = prevHeaders\n          ? Array.prototype.concat([], prevHeaders, headerValue)\n          : headerValue\n      })\n    }\n    this.logger.info('mocked response headers ready:', headers)\n\n    /**\n     * Set the internal \"res\" property to the mocked \"OutgoingMessage\"\n     * to make the \"ClientRequest\" instance think there's data received\n     * from the socket.\n     * @see https://github.com/nodejs/node/blob/9c405f2591f5833d0247ed0fafdcd68c5b14ce7a/lib/_http_client.js#L501\n     *\n     * Set the response immediately so the interceptor could stream data\n     * chunks to the request client as they come in.\n     */\n    // @ts-ignore\n    this.res = this.response\n    this.emit('response', this.response)\n\n    const isResponseStreamFinished = new DeferredPromise<void>()\n\n    const finishResponseStream = () => {\n      this.logger.info('finished response stream!')\n\n      // Push \"null\" to indicate that the response body is complete\n      // and shouldn't be written to anymore.\n      this.response.push(null)\n      this.response.complete = true\n\n      isResponseStreamFinished.resolve()\n    }\n\n    if (body) {\n      const bodyReader = body.getReader()\n      const readNextChunk = async (): Promise<void> => {\n        const { done, value } = await bodyReader.read()\n\n        if (done) {\n          finishResponseStream()\n          return\n        }\n\n        this.response.emit('data', value)\n\n        return readNextChunk()\n      }\n\n      readNextChunk()\n    } else {\n      finishResponseStream()\n    }\n\n    isResponseStreamFinished.then(() => {\n      this.logger.info('finalizing response...')\n      this.response.emit('end')\n      this.terminate()\n\n      this.logger.info('request complete!')\n    })\n  }\n\n  private errorWith(error: Error): void {\n    this.destroyed = true\n    this.emit('error', error)\n    this.terminate()\n  }\n\n  /**\n   * Terminates a pending request.\n   */\n  private terminate(): void {\n    /**\n     * @note Some request clients (e.g. Octokit) create a ClientRequest\n     * in a way that it has no Agent set. Now, whether that's correct is\n     * debatable, but we should still handle this case gracefully.\n     * @see https://github.com/mswjs/interceptors/issues/304\n     */\n    // @ts-ignore\n    this.agent?.destroy()\n  }\n}\n", "import { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('utils getUrlByRequestOptions')\n\nexport type ClientRequestEndChunk = string | Buffer\nexport type ClientRequestEndCallback = () => void\n\ntype HttpRequestEndArgs =\n  | []\n  | [ClientRequestEndCallback]\n  | [ClientRequestEndChunk, ClientRequestEndCallback?]\n  | [ClientRequestEndChunk, BufferEncoding, ClientRequestEndCallback?]\n\ntype NormalizedHttpRequestEndParams = [\n  ClientRequestEndChunk | null,\n  BufferEncoding | null,\n  ClientRequestEndCallback | null\n]\n\n/**\n * Normalizes a list of arguments given to the `ClientRequest.end()`\n * method to always include `chunk`, `encoding`, and `callback`.\n */\nexport function normalizeClientRequestEndArgs(\n  ...args: HttpRequestEndArgs\n): NormalizedHttpRequestEndParams {\n  logger.info('arguments', args)\n  const normalizedArgs = new Array(3)\n    .fill(null)\n    .map((value, index) => args[index] || value)\n\n  normalizedArgs.sort((a, b) => {\n    // If first element is a function, move it rightwards.\n    if (typeof a === 'function') {\n      return 1\n    }\n\n    // If second element is a function, move the first leftwards.\n    if (typeof b === 'function') {\n      return -1\n    }\n\n    // If both elements are strings, preserve their original index.\n    if (typeof a === 'string' && typeof b === 'string') {\n      return normalizedArgs.indexOf(a) - normalizedArgs.indexOf(b)\n    }\n\n    return 0\n  })\n\n  logger.info('normalized args', normalizedArgs)\n  return normalizedArgs as NormalizedHttpRequestEndParams\n}\n", "import { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('http normalizeWriteArgs')\n\nexport type ClientRequestWriteCallback = (error?: Error | null) => void\nexport type ClientRequestWriteArgs = [\n  chunk: string | Buffer,\n  encoding?: BufferEncoding | ClientRequestWriteCallback,\n  callback?: ClientRequestWriteCallback\n]\n\nexport type NormalizedClientRequestWriteArgs = [\n  chunk: string | Buffer,\n  encoding?: BufferEncoding,\n  callback?: ClientRequestWriteCallback\n]\n\nexport function normalizeClientRequestWriteArgs(\n  args: ClientRequestWriteArgs\n): NormalizedClientRequestWriteArgs {\n  logger.info('normalizing ClientRequest.write arguments...', args)\n\n  const chunk = args[0]\n  const encoding =\n    typeof args[1] === 'string' ? (args[1] as BufferEncoding) : undefined\n  const callback = typeof args[1] === 'function' ? args[1] : args[2]\n\n  const writeArgs: NormalizedClientRequestWriteArgs = [\n    chunk,\n    encoding,\n    callback,\n  ]\n  logger.info(\n    'successfully normalized ClientRequest.write arguments:',\n    writeArgs\n  )\n\n  return writeArgs\n}\n", "import { IncomingMessage } from 'http'\nimport { PassThrough } from 'stream'\n\nexport const IS_CLONE = Symbol('isClone')\n\nexport interface ClonedIncomingMessage extends IncomingMessage {\n  [IS_CLONE]: boolean\n}\n\n/**\n * Clones a given `http.IncomingMessage` instance.\n */\nexport function cloneIncomingMessage(\n  message: IncomingMessage\n): ClonedIncomingMessage {\n  const clone = message.pipe(new PassThrough())\n\n  // Inherit all direct \"IncomingMessage\" properties.\n  inheritProperties(message, clone)\n\n  // Deeply inherit the message prototypes (Readable, Stream, EventEmitter, etc.).\n  const clonedPrototype = Object.create(IncomingMessage.prototype)\n  getPrototypes(clone).forEach((prototype) => {\n    inheritProperties(prototype, clonedPrototype)\n  })\n  Object.setPrototypeOf(clone, clonedPrototype)\n\n  Object.defineProperty(clone, IS_CLONE, {\n    enumerable: true,\n    value: true,\n  })\n\n  return clone as unknown as ClonedIncomingMessage\n}\n\n/**\n * Returns a list of all prototypes the given object extends.\n */\nfunction getPrototypes(source: object): object[] {\n  const prototypes: object[] = []\n  let current = source\n\n  while ((current = Object.getPrototypeOf(current))) {\n    prototypes.push(current)\n  }\n\n  return prototypes\n}\n\n/**\n * Inherits a given target object properties and symbols\n * onto the given source object.\n * @param source Object which should acquire properties.\n * @param target Object to inherit the properties from.\n */\nfunction inheritProperties(source: object, target: object): void {\n  const properties = [\n    ...Object.getOwnPropertyNames(source),\n    ...Object.getOwnPropertySymbols(source),\n  ]\n\n  for (const property of properties) {\n    if (target.hasOwnProperty(property)) {\n      continue\n    }\n\n    const descriptor = Object.getOwnPropertyDescriptor(source, property)\n    if (!descriptor) {\n      continue\n    }\n\n    Object.defineProperty(target, property, descriptor)\n  }\n}\n", "import type { IncomingHttpHeaders, IncomingMessage } from 'http'\nimport { isResponseWithoutBody } from '../../../utils/responseUtils'\n\n/**\n * Creates a Fetch API `Response` instance from the given\n * `http.IncomingMessage` instance.\n */\nexport function createResponse(message: IncomingMessage): Response {\n  const responseBodyOrNull = isResponseWithoutBody(message.statusCode || 200)\n    ? null\n    : new ReadableStream({\n        start(controller) {\n          message.on('data', (chunk) => controller.enqueue(chunk))\n          message.on('end', () => controller.close())\n\n          /**\n           * @todo Should also listen to the \"error\" on the message\n           * and forward it to the controller. Otherwise the stream\n           * will pend indefinitely.\n           */\n        },\n      })\n\n  return new Response(responseBodyOrNull, {\n    status: message.statusCode,\n    statusText: message.statusMessage,\n    headers: createHeadersFromIncomingHttpHeaders(message.headers),\n  })\n}\n\nfunction createHeadersFromIncomingHttpHeaders(\n  httpHeaders: IncomingHttpHeaders\n): Headers {\n  const headers = new Headers()\n\n  for (const headerName in httpHeaders) {\n    const headerValues = httpHeaders[headerName]\n\n    if (typeof headerValues === 'undefined') {\n      continue\n    }\n\n    if (Array.isArray(headerValues)) {\n      headerValues.forEach((headerValue) => {\n        headers.append(headerName, headerValue)\n      })\n\n      continue\n    }\n\n    headers.set(headerName, headerValues)\n  }\n\n  return headers\n}\n", "import type { NodeClientRequest } from '../NodeClientRequest'\n\n/**\n * Creates a Fetch API `Request` instance from the given `http.ClientRequest`.\n */\nexport function createRequest(clientRequest: NodeClientRequest): Request {\n  const headers = new Headers()\n\n  const outgoingHeaders = clientRequest.getHeaders()\n  for (const headerName in outgoingHeaders) {\n    const headerValue = outgoingHeaders[headerName]\n\n    if (typeof headerValue === 'undefined') {\n      continue\n    }\n\n    const valuesList = Array.prototype.concat([], headerValue)\n    for (const value of valuesList) {\n      headers.append(headerName, value.toString())\n    }\n  }\n\n  /**\n   * Translate the authentication from the request URL to\n   * the request \"Authorization\" header.\n   * @see https://github.com/mswjs/interceptors/issues/438\n   */\n  if (clientRequest.url.username || clientRequest.url.password) {\n    const username = decodeURIComponent(clientRequest.url.username || '')\n    const password = decodeURIComponent(clientRequest.url.password || '')\n    const auth = `${username}:${password}`\n    headers.set('Authorization', `Basic ${btoa(auth)}`)\n\n    // Remove the credentials from the URL since you cannot\n    // construct a Request instance with such a URL.\n    clientRequest.url.username = ''\n    clientRequest.url.password = ''\n  }\n\n  const method = clientRequest.method || 'GET'\n\n  return new Request(clientRequest.url, {\n    method,\n    headers,\n    credentials: 'same-origin',\n    body:\n      method === 'HEAD' || method === 'GET'\n        ? null\n        : clientRequest.requestBuffer,\n  })\n}\n", "/**\n * Returns the value behind the symbol with the given name.\n */\nexport function getValueBySymbol<T>(\n  symbolName: string,\n  source: object\n): T | undefined {\n  const ownSymbols = Object.getOwnPropertySymbols(source)\n\n  const symbol = ownSymbols.find((symbol) => {\n    return symbol.description === symbolName\n  })\n\n  if (symbol) {\n    return Reflect.get(source, symbol)\n  }\n\n  return\n}\n", "/**\n * Determines if a given value is an instance of object.\n */\nexport function isObject<T>(value: any, loose = false): value is T {\n  return loose\n    ? Object.prototype.toString.call(value).startsWith('[object ')\n    : Object.prototype.toString.call(value) === '[object Object]'\n}\n", "import { getValueBySymbol } from './getValueBySymbol'\nimport { isObject } from './isObject'\n\ntype RawHeadersMap = Map<string, string>\ntype HeadersMapHeader = { name: string; value: string }\n\n/**\n * Returns raw headers from the given `Headers` instance.\n * @example\n * const headers = new Headers([\n *   ['X-HeadeR-NamE', 'Value']\n * ])\n * getRawFetchHeaders(headers)\n * // { 'X-HeadeR-NamE': 'Value' }\n */\nexport function getRawFetchHeaders(\n  headers: Headers\n): RawHeadersMap | undefined {\n  const headersList = getValueBySymbol<object>('headers list', headers)\n\n  if (!headersList) {\n    return\n  }\n\n  const headersMap = getValueBySymbol<\n    Map<string, string> | Map<string, HeadersMapHeader>\n  >('headers map', headersList)\n\n  /**\n   * @note Older versions of Node.js (e.g. 18.8.0) keep headers map\n   * as Map<normalizedHeaderName, value> without any means to tap\n   * into raw header values. Detect that and return undefined.\n   */\n  if (!headersMap || !isHeadersMapWithRawHeaderNames(headersMap)) {\n    return\n  }\n\n  // Raw headers is a map of { rawHeaderName: rawHeaderValue }\n  const rawHeaders: RawHeadersMap = new Map<string, string>()\n\n  headersMap.forEach(({ name, value }) => {\n    rawHeaders.set(name, value)\n  })\n\n  return rawHeaders\n}\n\nfunction isHeadersMapWithRawHeaderNames(\n  headersMap: Map<string, string> | Map<string, HeadersMapHeader>\n): headersMap is Map<string, HeadersMapHeader> {\n  return Array.from(\n    headersMap.values() as Iterable<string | HeadersMapHeader>\n  ).every((value) => {\n    return isObject<HeadersMapHeader>(value) && 'name' in value\n  })\n}\n", "export function isNodeLikeError(\n  error: unknown\n): error is NodeJS.ErrnoException {\n  if (error == null) {\n    return false\n  }\n\n  if (!(error instanceof Error)) {\n    return false\n  }\n\n  return 'code' in error && 'errno' in error\n}\n", "import {\n  Agent as HttpAgent,\n  globalAgent as httpGlobalAgent,\n  IncomingMessage,\n} from 'http'\nimport {\n  RequestOptions,\n  Agent as HttpsAgent,\n  globalAgent as httpsGlobalAgent,\n} from 'https'\nimport { Url as LegacyURL, parse as parseUrl } from 'url'\nimport { Logger } from '@open-draft/logger'\nimport { getRequestOptionsByUrl } from '../../../utils/getRequestOptionsByUrl'\nimport {\n  ResolvedRequestOptions,\n  getUrlByRequestOptions,\n} from '../../../utils/getUrlByRequestOptions'\nimport { cloneObject } from '../../../utils/cloneObject'\nimport { isObject } from '../../../utils/isObject'\n\nconst logger = new Logger('http normalizeClientRequestArgs')\n\nexport type HttpRequestCallback = (response: IncomingMessage) => void\n\nexport type ClientRequestArgs =\n  // Request without any arguments is also possible.\n  | []\n  | [string | URL | LegacyURL, HttpRequestCallback?]\n  | [string | URL | LegacyURL, RequestOptions, HttpRequestCallback?]\n  | [RequestOptions, HttpRequestCallback?]\n\nfunction resolveRequestOptions(\n  args: ClientRequestArgs,\n  url: URL\n): RequestOptions {\n  // Calling `fetch` provides only URL to `ClientRequest`\n  // without any `RequestOptions` or callback.\n  if (typeof args[1] === 'undefined' || typeof args[1] === 'function') {\n    logger.info('request options not provided, deriving from the url', url)\n    return getRequestOptionsByUrl(url)\n  }\n\n  if (args[1]) {\n    logger.info('has custom RequestOptions!', args[1])\n    const requestOptionsFromUrl = getRequestOptionsByUrl(url)\n\n    logger.info('derived RequestOptions from the URL:', requestOptionsFromUrl)\n\n    /**\n     * Clone the request options to lock their state\n     * at the moment they are provided to `ClientRequest`.\n     * @see https://github.com/mswjs/interceptors/issues/86\n     */\n    logger.info('cloning RequestOptions...')\n    const clonedRequestOptions = cloneObject(args[1])\n    logger.info('successfully cloned RequestOptions!', clonedRequestOptions)\n\n    return {\n      ...requestOptionsFromUrl,\n      ...clonedRequestOptions,\n    }\n  }\n\n  logger.info('using an empty object as request options')\n  return {} as RequestOptions\n}\n\n/**\n * Overrides the given `URL` instance with the explicit properties provided\n * on the `RequestOptions` object. The options object takes precedence,\n * and will replace URL properties like \"host\", \"path\", and \"port\", if specified.\n */\nfunction overrideUrlByRequestOptions(url: URL, options: RequestOptions): URL {\n  url.host = options.host || url.host\n  url.hostname = options.hostname || url.hostname\n  url.port = options.port ? options.port.toString() : url.port\n\n  if (options.path) {\n    const parsedOptionsPath = parseUrl(options.path, false)\n    url.pathname = parsedOptionsPath.pathname || ''\n    url.search = parsedOptionsPath.search || ''\n  }\n\n  return url\n}\n\nfunction resolveCallback(\n  args: ClientRequestArgs\n): HttpRequestCallback | undefined {\n  return typeof args[1] === 'function' ? args[1] : args[2]\n}\n\nexport type NormalizedClientRequestArgs = [\n  url: URL,\n  options: ResolvedRequestOptions,\n  callback?: HttpRequestCallback\n]\n\n/**\n * Normalizes parameters given to a `http.request` call\n * so it always has a `URL` and `RequestOptions`.\n */\nexport function normalizeClientRequestArgs(\n  defaultProtocol: string,\n  ...args: ClientRequestArgs\n): NormalizedClientRequestArgs {\n  let url: URL\n  let options: ResolvedRequestOptions\n  let callback: HttpRequestCallback | undefined\n\n  logger.info('arguments', args)\n  logger.info('using default protocol:', defaultProtocol)\n\n  // Support \"http.request()\" calls without any arguments.\n  // That call results in a \"GET http://localhost\" request.\n  if (args.length === 0) {\n    const url = new URL('http://localhost')\n    const options = resolveRequestOptions(args, url)\n    return [url, options]\n  }\n\n  // Convert a url string into a URL instance\n  // and derive request options from it.\n  if (typeof args[0] === 'string') {\n    logger.info('first argument is a location string:', args[0])\n\n    url = new URL(args[0])\n    logger.info('created a url:', url)\n\n    const requestOptionsFromUrl = getRequestOptionsByUrl(url)\n    logger.info('request options from url:', requestOptionsFromUrl)\n\n    options = resolveRequestOptions(args, url)\n    logger.info('resolved request options:', options)\n\n    callback = resolveCallback(args)\n  }\n  // Handle a given URL instance as-is\n  // and derive request options from it.\n  else if (args[0] instanceof URL) {\n    url = args[0]\n    logger.info('first argument is a URL:', url)\n\n    // Check if the second provided argument is RequestOptions.\n    // If it is, check if \"options.path\" was set and rewrite it\n    // on the input URL.\n    // Do this before resolving options from the URL below\n    // to prevent query string from being duplicated in the path.\n    if (typeof args[1] !== 'undefined' && isObject<RequestOptions>(args[1])) {\n      url = overrideUrlByRequestOptions(url, args[1])\n    }\n\n    options = resolveRequestOptions(args, url)\n    logger.info('derived request options:', options)\n\n    callback = resolveCallback(args)\n  }\n  // Handle a legacy URL instance and re-normalize from either a RequestOptions object\n  // or a WHATWG URL.\n  else if ('hash' in args[0] && !('method' in args[0])) {\n    const [legacyUrl] = args\n    logger.info('first argument is a legacy URL:', legacyUrl)\n\n    if (legacyUrl.hostname === null) {\n      /**\n       * We are dealing with a relative url, so use the path as an \"option\" and\n       * merge in any existing options, giving priority to exising options -- i.e. a path in any\n       * existing options will take precedence over the one contained in the url. This is consistent\n       * with the behaviour in ClientRequest.\n       * @see https://github.com/nodejs/node/blob/d84f1312915fe45fe0febe888db692c74894c382/lib/_http_client.js#L122\n       */\n      logger.info('given legacy URL is relative (no hostname)')\n\n      return isObject(args[1])\n        ? normalizeClientRequestArgs(\n            defaultProtocol,\n            { path: legacyUrl.path, ...args[1] },\n            args[2]\n          )\n        : normalizeClientRequestArgs(\n            defaultProtocol,\n            { path: legacyUrl.path },\n            args[1] as HttpRequestCallback\n          )\n    }\n\n    logger.info('given legacy url is absolute')\n\n    // We are dealing with an absolute URL, so convert to WHATWG and try again.\n    const resolvedUrl = new URL(legacyUrl.href)\n\n    return args[1] === undefined\n      ? normalizeClientRequestArgs(defaultProtocol, resolvedUrl)\n      : typeof args[1] === 'function'\n      ? normalizeClientRequestArgs(defaultProtocol, resolvedUrl, args[1])\n      : normalizeClientRequestArgs(\n          defaultProtocol,\n          resolvedUrl,\n          args[1],\n          args[2]\n        )\n  }\n  // Handle a given \"RequestOptions\" object as-is\n  // and derive the URL instance from it.\n  else if (isObject(args[0])) {\n    options = args[0] as any\n    logger.info('first argument is RequestOptions:', options)\n\n    // When handling a \"RequestOptions\" object without an explicit \"protocol\",\n    // infer the protocol from the request issuing module (http/https).\n    options.protocol = options.protocol || defaultProtocol\n    logger.info('normalized request options:', options)\n\n    url = getUrlByRequestOptions(options)\n    logger.info('created a URL from RequestOptions:', url.href)\n\n    callback = resolveCallback(args)\n  } else {\n    throw new Error(\n      `Failed to construct ClientRequest with these parameters: ${args}`\n    )\n  }\n\n  options.protocol = options.protocol || url.protocol\n  options.method = options.method || 'GET'\n\n  /**\n   * Infer a fallback agent from the URL protocol.\n   * The interception is done on the \"ClientRequest\" level (\"NodeClientRequest\")\n   * and it may miss the correct agent. Always align the agent\n   * with the URL protocol, if not provided.\n   *\n   * @note Respect the \"agent: false\" value.\n   */\n  if (typeof options.agent === 'undefined') {\n    const agent =\n      options.protocol === 'https:'\n        ? new HttpsAgent({\n            rejectUnauthorized: options.rejectUnauthorized,\n          })\n        : new HttpAgent()\n\n    options.agent = agent\n    logger.info('resolved fallback agent:', agent)\n  }\n\n  /**\n   * Ensure that the default Agent is always set.\n   * This prevents the protocol mismatch for requests with { agent: false },\n   * where the global Agent is inferred.\n   * @see https://github.com/mswjs/msw/issues/1150\n   * @see https://github.com/nodejs/node/blob/418ff70b810f0e7112d48baaa72932a56cfa213b/lib/_http_client.js#L130\n   * @see https://github.com/nodejs/node/blob/418ff70b810f0e7112d48baaa72932a56cfa213b/lib/_http_client.js#L157-L159\n   */\n  if (!options._defaultAgent) {\n    logger.info(\n      'has no default agent, setting the default agent for \"%s\"',\n      options.protocol\n    )\n\n    options._defaultAgent =\n      options.protocol === 'https:' ? httpsGlobalAgent : httpGlobalAgent\n  }\n\n  logger.info('successfully resolved url:', url.href)\n  logger.info('successfully resolved options:', options)\n  logger.info('successfully resolved callback:', callback)\n\n  return [url, options, callback]\n}\n", "import { RequestOptions } from 'http'\n\n/**\n * Converts a URL instance into the RequestOptions object expected by\n * the `ClientRequest` class.\n * @see https://github.com/nodejs/node/blob/908292cf1f551c614a733d858528ffb13fb3a524/lib/internal/url.js#L1257\n */\nexport function getRequestOptionsByUrl(url: URL): RequestOptions {\n  const options: RequestOptions = {\n    method: 'GET',\n    protocol: url.protocol,\n    hostname:\n      typeof url.hostname === 'string' && url.hostname.startsWith('[')\n        ? url.hostname.slice(1, -1)\n        : url.hostname,\n    host: url.host,\n    path: `${url.pathname}${url.search || ''}`,\n  }\n\n  if (!!url.port) {\n    options.port = Number(url.port)\n  }\n\n  if (url.username || url.password) {\n    options.auth = `${url.username}:${url.password}`\n  }\n\n  return options\n}\n", "import { Agent } from 'http'\nimport { RequestOptions, Agent as HttpsAgent } from 'https'\nimport { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('utils getUrlByRequestOptions')\n\n// Request instance constructed by the \"request\" library\n// has a \"self\" property that has a \"uri\" field. This is\n// reproducible by performing a \"XMLHttpRequest\" request in JSDOM.\nexport interface RequestSelf {\n  uri?: URL\n}\n\nexport type ResolvedRequestOptions = RequestOptions & RequestSelf\n\nexport const DEFAULT_PATH = '/'\nconst DEFAULT_PROTOCOL = 'http:'\nconst DEFAULT_HOSTNAME = 'localhost'\nconst SSL_PORT = 443\n\nfunction getAgent(\n  options: ResolvedRequestOptions\n): Agent | HttpsAgent | undefined {\n  return options.agent instanceof Agent ? options.agent : undefined\n}\n\nfunction getProtocolByRequestOptions(options: ResolvedRequestOptions): string {\n  if (options.protocol) {\n    return options.protocol\n  }\n\n  const agent = getAgent(options)\n  const agentProtocol = (agent as RequestOptions)?.protocol\n\n  if (agentProtocol) {\n    return agentProtocol\n  }\n\n  const port = getPortByRequestOptions(options)\n  const isSecureRequest = options.cert || port === SSL_PORT\n\n  return isSecureRequest ? 'https:' : options.uri?.protocol || DEFAULT_PROTOCOL\n}\n\nfunction getPortByRequestOptions(\n  options: ResolvedRequestOptions\n): number | undefined {\n  // Use the explicitly provided port.\n  if (options.port) {\n    return Number(options.port)\n  }\n\n  // Otherwise, try to resolve port from the agent.\n  const agent = getAgent(options)\n\n  if ((agent as HttpsAgent)?.options.port) {\n    return Number((agent as HttpsAgent).options.port)\n  }\n\n  if ((agent as RequestOptions)?.defaultPort) {\n    return Number((agent as RequestOptions).defaultPort)\n  }\n\n  // Lastly, return undefined indicating that the port\n  // must inferred from the protocol. Do not infer it here.\n  return undefined\n}\n\ninterface RequestAuth {\n  username: string\n  password: string\n}\n\nfunction getAuthByRequestOptions(\n  options: ResolvedRequestOptions\n): RequestAuth | undefined {\n  if (options.auth) {\n    const [username, password] = options.auth.split(':')\n    return { username, password }\n  }\n}\n\n/**\n * Returns true if host looks like an IPv6 address without surrounding brackets\n * It assumes any host containing `:` is definitely not IPv4 and probably IPv6,\n * but note that this could include invalid IPv6 addresses as well.\n */\nfunction isRawIPv6Address(host: string): boolean {\n  return host.includes(':') && !host.startsWith('[') && !host.endsWith(']')\n}\n\nfunction getHostname(options: ResolvedRequestOptions): string | undefined {\n  let host = options.hostname || options.host\n\n  if (host) {\n    if (isRawIPv6Address(host)) {\n       host = `[${host}]`\n    }\n\n    // Check the presence of the port, and if it's present,\n    // remove it from the host, returning a hostname.\n    return new URL(`http://${host}`).hostname\n  }\n\n  return DEFAULT_HOSTNAME\n}\n\n/**\n * Creates a `URL` instance from a given `RequestOptions` object.\n */\nexport function getUrlByRequestOptions(options: ResolvedRequestOptions): URL {\n  logger.info('request options', options)\n\n  if (options.uri) {\n    logger.info(\n      'constructing url from explicitly provided \"options.uri\": %s',\n      options.uri\n    )\n    return new URL(options.uri.href)\n  }\n\n  logger.info('figuring out url from request options...')\n\n  const protocol = getProtocolByRequestOptions(options)\n  logger.info('protocol', protocol)\n\n  const port = getPortByRequestOptions(options)\n  logger.info('port', port)\n\n  const hostname = getHostname(options)\n  logger.info('hostname', hostname)\n\n  const path = options.path || DEFAULT_PATH\n  logger.info('path', path)\n\n  const credentials = getAuthByRequestOptions(options)\n  logger.info('credentials', credentials)\n\n  const authString = credentials\n    ? `${credentials.username}:${credentials.password}@`\n    : ''\n  logger.info('auth string:', authString)\n\n  const portString = typeof port !== 'undefined' ? `:${port}` : ''\n  const url = new URL(`${protocol}//${hostname}${portString}${path}`)\n  url.username = credentials?.username || ''\n  url.password = credentials?.password || ''\n\n  logger.info('created url:', url)\n\n  return url\n}\n", "import { Logger } from '@open-draft/logger'\n\nconst logger = new Logger('cloneObject')\n\nfunction isPlainObject(obj?: Record<string, any>): boolean {\n  logger.info('is plain object?', obj)\n\n  if (obj == null || !obj.constructor?.name) {\n    logger.info('given object is undefined, not a plain object...')\n    return false\n  }\n\n  logger.info('checking the object constructor:', obj.constructor.name)\n  return obj.constructor.name === 'Object'\n}\n\nexport function cloneObject<ObjectType extends Record<string, any>>(\n  obj: ObjectType\n): ObjectType {\n  logger.info('cloning object:', obj)\n\n  const enumerableProperties = Object.entries(obj).reduce<Record<string, any>>(\n    (acc, [key, value]) => {\n      logger.info('analyzing key-value pair:', key, value)\n\n      // Recursively clone only plain objects, omitting class instances.\n      acc[key] = isPlainObject(value) ? cloneObject(value) : value\n      return acc\n    },\n    {}\n  )\n\n  return isPlainObject(obj)\n    ? enumerableProperties\n    : Object.assign(Object.getPrototypeOf(obj), enumerableProperties)\n}\n", "import { ClientRequest } from 'node:http'\nimport {\n  NodeClientOptions,\n  NodeClientRequest,\n  Protocol,\n} from './NodeClientRequest'\nimport {\n  ClientRequestArgs,\n  normalizeClientRequestArgs,\n} from './utils/normalizeClientRequestArgs'\n\nexport function get(protocol: Protocol, options: NodeClientOptions) {\n  return function interceptorsHttpGet(\n    ...args: ClientRequestArgs\n  ): ClientRequest {\n    const clientRequestArgs = normalizeClientRequestArgs(\n      `${protocol}:`,\n      ...args\n    )\n    const request = new NodeClientRequest(clientRequestArgs, options)\n\n    /**\n     * @note https://nodejs.org/api/http.html#httpgetoptions-callback\n     * \"http.get\" sets the method to \"GET\" and calls \"req.end()\" automatically.\n     */\n    request.end()\n\n    return request\n  }\n}\n", "import { ClientRequest } from 'http'\nimport { Logger } from '@open-draft/logger'\nimport {\n  NodeClientOptions,\n  NodeClientRequest,\n  Protocol,\n} from './NodeClientRequest'\nimport {\n  normalizeClientRequestArgs,\n  ClientRequestArgs,\n} from './utils/normalizeClientRequestArgs'\n\nconst logger = new Logger('http request')\n\nexport function request(protocol: Protocol, options: NodeClientOptions) {\n  return function interceptorsHttpRequest(\n    ...args: ClientRequestArgs\n  ): ClientRequest {\n    logger.info('request call (protocol \"%s\"):', protocol, args)\n\n    const clientRequestArgs = normalizeClientRequestArgs(\n      `${protocol}:`,\n      ...args\n    )\n    return new NodeClientRequest(clientRequestArgs, options)\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { Emitter } from 'strict-event-emitter'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { InteractiveRequest } from '../../utils/toInteractiveRequest'\nimport { Interceptor } from '../../Interceptor'\nimport { createXMLHttpRequestProxy } from './XMLHttpRequestProxy'\n\nexport type XMLHttpRequestEventListener = (args: {\n  request: InteractiveRequest\n  requestId: string\n}) => Promise<void> | void\n\nexport type XMLHttpRequestEmitter = Emitter<HttpRequestEventMap>\n\nexport class XMLHttpRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static interceptorSymbol = Symbol('xhr')\n\n  constructor() {\n    super(XMLHttpRequestInterceptor.interceptorSymbol)\n  }\n\n  protected checkEnvironment() {\n    return typeof globalThis.XMLHttpRequest !== 'undefined'\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('patching \"XMLHttpRequest\" module...')\n\n    const PureXMLHttpRequest = globalThis.XMLHttpRequest\n\n    invariant(\n      !(PureXMLHttpRequest as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"XMLHttpRequest\" module: already patched.'\n    )\n\n    globalThis.XMLHttpRequest = createXMLHttpRequestProxy({\n      emitter: this.emitter,\n      logger: this.logger,\n    })\n\n    logger.info(\n      'native \"XMLHttpRequest\" module patched!',\n      globalThis.XMLHttpRequest.name\n    )\n\n    Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.XMLHttpRequest = PureXMLHttpRequest\n      logger.info(\n        'native \"XMLHttpRequest\" module restored!',\n        globalThis.XMLHttpRequest.name\n      )\n    })\n  }\n}\n", "import { until } from '@open-draft/until'\nimport type { Logger } from '@open-draft/logger'\nimport { XMLHttpRequestEmitter } from '.'\nimport { toInteractiveRequest } from '../../utils/toInteractiveRequest'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { XMLHttpRequestController } from './XMLHttpRequestController'\nimport {\n  createServerErrorResponse,\n  isResponseError,\n} from '../../utils/responseUtils'\n\nexport interface XMLHttpRequestProxyOptions {\n  emitter: XMLHttpRequestEmitter\n  logger: Logger\n}\n\n/**\n * Create a proxied `XMLHttpRequest` class.\n * The proxied class establishes spies on certain methods,\n * allowing us to intercept requests and respond to them.\n */\nexport function createXMLHttpRequestProxy({\n  emitter,\n  logger,\n}: XMLHttpRequestProxyOptions) {\n  const XMLHttpRequestProxy = new Proxy(globalThis.XMLHttpRequest, {\n    construct(target, args, newTarget) {\n      logger.info('constructed new XMLHttpRequest')\n\n      const originalRequest = Reflect.construct(\n        target,\n        args,\n        newTarget\n      ) as XMLHttpRequest\n\n      /**\n       * @note Forward prototype descriptors onto the proxied object.\n       * XMLHttpRequest is implemented in JSDOM in a way that assigns\n       * a bunch of descriptors, like \"set responseType()\" on the prototype.\n       * With this propagation, we make sure that those descriptors trigger\n       * when the user operates with the proxied request instance.\n       */\n      const prototypeDescriptors = Object.getOwnPropertyDescriptors(\n        target.prototype\n      )\n      for (const propertyName in prototypeDescriptors) {\n        Reflect.defineProperty(\n          originalRequest,\n          propertyName,\n          prototypeDescriptors[propertyName]\n        )\n      }\n\n      const xhrRequestController = new XMLHttpRequestController(\n        originalRequest,\n        logger\n      )\n\n      xhrRequestController.onRequest = async function ({ request, requestId }) {\n        const { interactiveRequest, requestController } =\n          toInteractiveRequest(request)\n\n        this.logger.info('awaiting mocked response...')\n\n        emitter.once('request', ({ requestId: pendingRequestId }) => {\n          if (pendingRequestId !== requestId) {\n            return\n          }\n\n          if (requestController.responsePromise.state === 'pending') {\n            requestController.respondWith(undefined)\n          }\n        })\n\n        const resolverResult = await until(async () => {\n          this.logger.info(\n            'emitting the \"request\" event for %s listener(s)...',\n            emitter.listenerCount('request')\n          )\n\n          await emitAsync(emitter, 'request', {\n            request: interactiveRequest,\n            requestId,\n          })\n\n          this.logger.info('all \"request\" listeners settled!')\n\n          const mockedResponse = await requestController.responsePromise\n\n          this.logger.info('event.respondWith called with:', mockedResponse)\n\n          return mockedResponse\n        })\n\n        if (resolverResult.error) {\n          this.logger.info(\n            'request listener threw an exception, aborting request...',\n            resolverResult.error\n          )\n\n          // Treat thrown Responses as mocked responses.\n          if (resolverResult.error instanceof Response) {\n            if (isResponseError(resolverResult.error)) {\n              xhrRequestController.errorWith(new TypeError('Network error'))\n            } else {\n              this.respondWith(resolverResult.error)\n            }\n\n            return\n          }\n\n          if (emitter.listenerCount('unhandledException') > 0) {\n            // Emit the \"unhandledException\" event so the client can opt-out\n            // from the default exception handling (producing 500 error responses).\n            await emitAsync(emitter, 'unhandledException', {\n              error: resolverResult.error,\n              request,\n              requestId,\n              controller: {\n                respondWith:\n                  xhrRequestController.respondWith.bind(xhrRequestController),\n                errorWith:\n                  xhrRequestController.errorWith.bind(xhrRequestController),\n              },\n            })\n\n            // If any of the \"unhandledException\" listeners handled the request,\n            // do nothing. Note that mocked responses will dispatch\n            // HEADERS_RECEIVED (2), then LOADING (3), and DONE (4) can take\n            // time as the mocked response body finishes streaming.\n            if (originalRequest.readyState > XMLHttpRequest.OPENED) {\n              return\n            }\n          }\n\n          // Unhandled exceptions in the request listeners are\n          // synonymous to unhandled exceptions on the server.\n          // Those are represented as 500 error responses.\n          xhrRequestController.respondWith(\n            createServerErrorResponse(resolverResult.error)\n          )\n\n          return\n        }\n\n        const mockedResponse = resolverResult.data\n\n        if (typeof mockedResponse !== 'undefined') {\n          this.logger.info(\n            'received mocked response: %d %s',\n            mockedResponse.status,\n            mockedResponse.statusText\n          )\n\n          if (isResponseError(mockedResponse)) {\n            this.logger.info(\n              'received a network error response, rejecting the request promise...'\n            )\n\n            xhrRequestController.errorWith(new TypeError('Network error'))\n            return\n          }\n\n          return xhrRequestController.respondWith(mockedResponse)\n        }\n\n        this.logger.info(\n          'no mocked response received, performing request as-is...'\n        )\n      }\n\n      xhrRequestController.onResponse = async function ({\n        response,\n        isMockedResponse,\n        request,\n        requestId,\n      }) {\n        this.logger.info(\n          'emitting the \"response\" event for %s listener(s)...',\n          emitter.listenerCount('response')\n        )\n\n        emitter.emit('response', {\n          response,\n          isMockedResponse,\n          request,\n          requestId,\n        })\n      }\n\n      // Return the proxied request from the controller\n      // so that the controller can react to the consumer's interactions\n      // with this request (opening/sending/etc).\n      return xhrRequestController.request\n    },\n  })\n\n  return XMLHttpRequestProxy\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport type { Logger } from '@open-draft/logger'\nimport { concatArrayBuffer } from './utils/concatArrayBuffer'\nimport { createEvent } from './utils/createEvent'\nimport {\n  decodeBuffer,\n  encode<PERSON>uffer,\n  toArray<PERSON>uffer,\n} from '../../utils/bufferUtils'\nimport { createProxy } from '../../utils/createProxy'\nimport { isDomParserSupportedType } from './utils/isDomParserSupportedType'\nimport { parseJson } from '../../utils/parseJson'\nimport { createResponse } from './utils/createResponse'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { createRequestId } from '../../createRequestId'\n\nconst IS_MOCKED_RESPONSE = Symbol('isMockedResponse')\nconst IS_NODE = isNodeProcess()\n\n/**\n * An `XMLHttpRequest` instance controller that allows us\n * to handle any given request instance (e.g. responding to it).\n */\nexport class XMLHttpRequestController {\n  public request: XMLHttpRequest\n  public requestId: string\n  public onRequest?: (\n    this: XMLHttpRequestController,\n    args: {\n      request: Request\n      requestId: string\n    }\n  ) => Promise<void>\n  public onResponse?: (\n    this: XMLHttpRequestController,\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ) => void\n\n  private method: string = 'GET'\n  private url: URL = null as any\n  private requestHeaders: Headers\n  private requestBody?: XMLHttpRequestBodyInit | Document | null\n  private responseBuffer: Uint8Array\n  private events: Map<keyof XMLHttpRequestEventTargetEventMap, Array<Function>>\n\n  constructor(readonly initialRequest: XMLHttpRequest, public logger: Logger) {\n    this.events = new Map()\n    this.requestId = createRequestId()\n    this.requestHeaders = new Headers()\n    this.responseBuffer = new Uint8Array()\n\n    this.request = createProxy(initialRequest, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case 'ontimeout': {\n            const eventName = propertyName.slice(\n              2\n            ) as keyof XMLHttpRequestEventTargetEventMap\n\n            /**\n             * @note Proxy callbacks to event listeners because JSDOM has trouble\n             * translating these properties to callbacks. It seemed to be operating\n             * on events exclusively.\n             */\n            this.request.addEventListener(eventName, nextValue as any)\n\n            return invoke()\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case 'open': {\n            const [method, url] = args as [string, string | undefined]\n\n            if (typeof url === 'undefined') {\n              this.method = 'GET'\n              this.url = toAbsoluteUrl(method)\n            } else {\n              this.method = method\n              this.url = toAbsoluteUrl(url)\n            }\n\n            this.logger = this.logger.extend(`${this.method} ${this.url.href}`)\n            this.logger.info('open', this.method, this.url.href)\n\n            return invoke()\n          }\n\n          case 'addEventListener': {\n            const [eventName, listener] = args as [\n              keyof XMLHttpRequestEventTargetEventMap,\n              Function\n            ]\n\n            this.registerEvent(eventName, listener)\n            this.logger.info('addEventListener', eventName, listener)\n\n            return invoke()\n          }\n\n          case 'setRequestHeader': {\n            const [name, value] = args as [string, string]\n            this.requestHeaders.set(name, value)\n\n            this.logger.info('setRequestHeader', name, value)\n\n            return invoke()\n          }\n\n          case 'send': {\n            const [body] = args as [\n              body?: XMLHttpRequestBodyInit | Document | null\n            ]\n\n            if (body != null) {\n              this.requestBody =\n                typeof body === 'string' ? encodeBuffer(body) : body\n            }\n\n            this.request.addEventListener('load', () => {\n              if (typeof this.onResponse !== 'undefined') {\n                // Create a Fetch API Response representation of whichever\n                // response this XMLHttpRequest received. Note those may\n                // be either a mocked and the original response.\n                const fetchResponse = createResponse(\n                  this.request,\n                  /**\n                   * The `response` property is the right way to read\n                   * the ambiguous response body, as the request's \"responseType\" may differ.\n                   * @see https://xhr.spec.whatwg.org/#the-response-attribute\n                   */\n                  this.request.response\n                )\n\n                // Notify the consumer about the response.\n                this.onResponse.call(this, {\n                  response: fetchResponse,\n                  isMockedResponse: IS_MOCKED_RESPONSE in this.request,\n                  request: fetchRequest,\n                  requestId: this.requestId!,\n                })\n              }\n            })\n\n            // Delegate request handling to the consumer.\n            const fetchRequest = this.toFetchApiRequest()\n            const onceRequestSettled =\n              this.onRequest?.call(this, {\n                request: fetchRequest,\n                requestId: this.requestId!,\n              }) || Promise.resolve()\n\n            onceRequestSettled.finally(() => {\n              // If the consumer didn't handle the request perform it as-is.\n              // Note that the request may not yet be DONE and may, in fact,\n              // be LOADING while the \"respondWith\" method does its magic.\n              if (this.request.readyState < this.request.LOADING) {\n                this.logger.info(\n                  'request callback settled but request has not been handled (readystate %d), performing as-is...',\n                  this.request.readyState\n                )\n\n                /**\n                 * @note Set the intercepted request ID on the original request in Node.js\n                 * so that if it triggers any other interceptors, they don't attempt\n                 * to process it once again.\n                 *\n                 * For instance, XMLHttpRequest is often implemented via \"http.ClientRequest\"\n                 * and we don't want for both XHR and ClientRequest interceptors to\n                 * handle the same request at the same time (e.g. emit the \"response\" event twice).\n                 */\n                if (IS_NODE) {\n                  this.request.setRequestHeader(\n                    INTERNAL_REQUEST_ID_HEADER_NAME,\n                    this.requestId!\n                  )\n                }\n\n                return invoke()\n              }\n            })\n\n            break\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n    })\n  }\n\n  private registerEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.events.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.events.set(eventName, nextEvents)\n\n    this.logger.info('registered event \"%s\"', eventName, listener)\n  }\n\n  /**\n   * Responds to the current request with the given\n   * Fetch API `Response` instance.\n   */\n  public respondWith(response: Response): void {\n    this.logger.info(\n      'responding with a mocked response: %d %s',\n      response.status,\n      response.statusText\n    )\n\n    /**\n     * @note Since `XMLHttpRequestController` delegates the handling of the responses\n     * to the \"load\" event listener that doesn't distinguish between the mocked and original\n     * responses, mark the request that had a mocked response with a corresponding symbol.\n     */\n    define(this.request, IS_MOCKED_RESPONSE, true)\n\n    define(this.request, 'status', response.status)\n    define(this.request, 'statusText', response.statusText)\n    define(this.request, 'responseURL', this.url.href)\n\n    this.request.getResponseHeader = new Proxy(this.request.getResponseHeader, {\n      apply: (_, __, args: [name: string]) => {\n        this.logger.info('getResponseHeader', args[0])\n\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info('headers not received yet, returning null')\n\n          // Headers not received yet, nothing to return.\n          return null\n        }\n\n        const headerValue = response.headers.get(args[0])\n        this.logger.info(\n          'resolved response header \"%s\" to',\n          args[0],\n          headerValue\n        )\n\n        return headerValue\n      },\n    })\n\n    this.request.getAllResponseHeaders = new Proxy(\n      this.request.getAllResponseHeaders,\n      {\n        apply: () => {\n          this.logger.info('getAllResponseHeaders')\n\n          if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n            this.logger.info('headers not received yet, returning empty string')\n\n            // Headers not received yet, nothing to return.\n            return ''\n          }\n\n          const headersList = Array.from(response.headers.entries())\n          const allHeaders = headersList\n            .map(([headerName, headerValue]) => {\n              return `${headerName}: ${headerValue}`\n            })\n            .join('\\r\\n')\n\n          this.logger.info('resolved all response headers to', allHeaders)\n\n          return allHeaders\n        },\n      }\n    )\n\n    // Update the response getters to resolve against the mocked response.\n    Object.defineProperties(this.request, {\n      response: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.response,\n      },\n      responseText: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseText,\n      },\n      responseXML: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseXML,\n      },\n    })\n\n    const totalResponseBodyLength = response.headers.has('Content-Length')\n      ? Number(response.headers.get('Content-Length'))\n      : /**\n         * @todo Infer the response body length from the response body.\n         */\n        undefined\n\n    this.logger.info('calculated response body length', totalResponseBodyLength)\n\n    this.trigger('loadstart', {\n      loaded: 0,\n      total: totalResponseBodyLength,\n    })\n\n    this.setReadyState(this.request.HEADERS_RECEIVED)\n    this.setReadyState(this.request.LOADING)\n\n    const finalizeResponse = () => {\n      this.logger.info('finalizing the mocked response...')\n\n      this.setReadyState(this.request.DONE)\n\n      this.trigger('load', {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n\n      this.trigger('loadend', {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n    }\n\n    if (response.body) {\n      this.logger.info('mocked response has body, streaming...')\n\n      const reader = response.body.getReader()\n\n      const readNextResponseBodyChunk = async () => {\n        const { value, done } = await reader.read()\n\n        if (done) {\n          this.logger.info('response body stream done!')\n          finalizeResponse()\n          return\n        }\n\n        if (value) {\n          this.logger.info('read response body chunk:', value)\n          this.responseBuffer = concatArrayBuffer(this.responseBuffer, value)\n\n          this.trigger('progress', {\n            loaded: this.responseBuffer.byteLength,\n            total: totalResponseBodyLength,\n          })\n        }\n\n        readNextResponseBodyChunk()\n      }\n\n      readNextResponseBodyChunk()\n    } else {\n      finalizeResponse()\n    }\n  }\n\n  private responseBufferToText(): string {\n    return decodeBuffer(this.responseBuffer)\n  }\n\n  get response(): unknown {\n    this.logger.info(\n      'getResponse (responseType: %s)',\n      this.request.responseType\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    switch (this.request.responseType) {\n      case 'json': {\n        const responseJson = parseJson(this.responseBufferToText())\n        this.logger.info('resolved response JSON', responseJson)\n\n        return responseJson\n      }\n\n      case 'arraybuffer': {\n        const arrayBuffer = toArrayBuffer(this.responseBuffer)\n        this.logger.info('resolved response ArrayBuffer', arrayBuffer)\n\n        return arrayBuffer\n      }\n\n      case 'blob': {\n        const mimeType =\n          this.request.getResponseHeader('Content-Type') || 'text/plain'\n        const responseBlob = new Blob([this.responseBufferToText()], {\n          type: mimeType,\n        })\n\n        this.logger.info(\n          'resolved response Blob (mime type: %s)',\n          responseBlob,\n          mimeType\n        )\n\n        return responseBlob\n      }\n\n      default: {\n        const responseText = this.responseBufferToText()\n        this.logger.info(\n          'resolving \"%s\" response type as text',\n          this.request.responseType,\n          responseText\n        )\n\n        return responseText\n      }\n    }\n  }\n\n  get responseText(): string {\n    /**\n     * Throw when trying to read the response body as text when the\n     * \"responseType\" doesn't expect text. This just respects the spec better.\n     * @see https://xhr.spec.whatwg.org/#the-responsetext-attribute\n     */\n    invariant(\n      this.request.responseType === '' || this.request.responseType === 'text',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (\n      this.request.readyState !== this.request.LOADING &&\n      this.request.readyState !== this.request.DONE\n    ) {\n      return ''\n    }\n\n    const responseText = this.responseBufferToText()\n    this.logger.info('getResponseText: \"%s\"', responseText)\n\n    return responseText\n  }\n\n  get responseXML(): Document | null {\n    invariant(\n      this.request.responseType === '' ||\n        this.request.responseType === 'document',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    const contentType = this.request.getResponseHeader('Content-Type') || ''\n\n    if (typeof DOMParser === 'undefined') {\n      console.warn(\n        'Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.'\n      )\n      return null\n    }\n\n    if (isDomParserSupportedType(contentType)) {\n      return new DOMParser().parseFromString(\n        this.responseBufferToText(),\n        contentType\n      )\n    }\n\n    return null\n  }\n\n  public errorWith(error: Error): void {\n    this.logger.info('responding with an error')\n\n    this.setReadyState(this.request.DONE)\n    this.trigger('error')\n    this.trigger('loadend')\n  }\n\n  /**\n   * Transitions this request's `readyState` to the given one.\n   */\n  private setReadyState(nextReadyState: number): void {\n    this.logger.info(\n      'setReadyState: %d -> %d',\n      this.request.readyState,\n      nextReadyState\n    )\n\n    if (this.request.readyState === nextReadyState) {\n      this.logger.info('ready state identical, skipping transition...')\n      return\n    }\n\n    define(this.request, 'readyState', nextReadyState)\n\n    this.logger.info('set readyState to: %d', nextReadyState)\n\n    if (nextReadyState !== this.request.UNSENT) {\n      this.logger.info('triggerring \"readystatechange\" event...')\n\n      this.trigger('readystatechange')\n    }\n  }\n\n  /**\n   * Triggers given event on the `XMLHttpRequest` instance.\n   */\n  private trigger<\n    EventName extends keyof (XMLHttpRequestEventTargetEventMap & {\n      readystatechange: ProgressEvent<XMLHttpRequestEventTarget>\n    })\n  >(eventName: EventName, options?: ProgressEventInit): void {\n    const callback = this.request[`on${eventName}`]\n    const event = createEvent(this.request, eventName, options)\n\n    this.logger.info('trigger \"%s\"', eventName, options || '')\n\n    // Invoke direct callbacks.\n    if (typeof callback === 'function') {\n      this.logger.info('found a direct \"%s\" callback, calling...', eventName)\n      callback.call(this.request, event)\n    }\n\n    // Invoke event listeners.\n    for (const [registeredEventName, listeners] of this.events) {\n      if (registeredEventName === eventName) {\n        this.logger.info(\n          'found %d listener(s) for \"%s\" event, calling...',\n          listeners.length,\n          eventName\n        )\n\n        listeners.forEach((listener) => listener.call(this.request, event))\n      }\n    }\n  }\n\n  /**\n   * Converts this `XMLHttpRequest` instance into a Fetch API `Request` instance.\n   */\n  public toFetchApiRequest(): Request {\n    this.logger.info('converting request to a Fetch API Request...')\n\n    const fetchRequest = new Request(this.url.href, {\n      method: this.method,\n      headers: this.requestHeaders,\n      /**\n       * @see https://xhr.spec.whatwg.org/#cross-origin-credentials\n       */\n      credentials: this.request.withCredentials ? 'include' : 'same-origin',\n      body: ['GET', 'HEAD'].includes(this.method)\n        ? null\n        : (this.requestBody as BodyInit),\n    })\n\n    const proxyHeaders = createProxy(fetchRequest.headers, {\n      methodCall: ([methodName, args], invoke) => {\n        // Forward the latest state of the internal request headers\n        // because the interceptor might have modified them\n        // without responding to the request.\n        switch (methodName) {\n          case 'append':\n          case 'set': {\n            const [headerName, headerValue] = args as [string, string]\n            this.request.setRequestHeader(headerName, headerValue)\n            break\n          }\n\n          case 'delete': {\n            const [headerName] = args as [string]\n            console.warn(\n              `XMLHttpRequest: Cannot remove a \"${headerName}\" header from the Fetch API representation of the \"${fetchRequest.method} ${fetchRequest.url}\" request. XMLHttpRequest headers cannot be removed.`\n            )\n            break\n          }\n        }\n\n        return invoke()\n      },\n    })\n    define(fetchRequest, 'headers', proxyHeaders)\n\n    this.logger.info('converted request to a Fetch API Request!', fetchRequest)\n\n    return fetchRequest\n  }\n}\n\nfunction toAbsoluteUrl(url: string | URL): URL {\n  /**\n   * @note XMLHttpRequest interceptor may run in environments\n   * that implement XMLHttpRequest but don't implement \"location\"\n   * (for example, React Native). If that's the case, return the\n   * input URL as-is (nothing to be relative to).\n   * @see https://github.com/mswjs/msw/issues/1777\n   */\n  if (typeof location === 'undefined') {\n    return new URL(url)\n  }\n\n  return new URL(url.toString(), location.href)\n}\n\nfunction define(\n  target: object,\n  property: string | symbol,\n  value: unknown\n): void {\n  Reflect.defineProperty(target, property, {\n    // Ensure writable properties to allow redefining readonly properties.\n    writable: true,\n    enumerable: true,\n    value,\n  })\n}\n", "/**\n * Concatenate two `Uint8Array` buffers.\n */\nexport function concatArrayBuffer(\n  left: Uint8Array,\n  right: Uint8Array\n): Uint8Array {\n  const result = new Uint8Array(left.byteLength + right.byteLength)\n  result.set(left, 0)\n  result.set(right, left.byteLength)\n  return result\n}\n", "export class EventPolyfill implements Event {\n  readonly AT_TARGET: number = 0\n  readonly BUBBLING_PHASE: number = 0\n  readonly CAPTURING_PHASE: number = 0\n  readonly NONE: number = 0\n\n  public type: string = ''\n  public srcElement: EventTarget | null = null\n  public target: EventTarget | null\n  public currentTarget: EventTarget | null = null\n  public eventPhase: number = 0\n  public timeStamp: number\n  public isTrusted: boolean = true\n  public composed: boolean = false\n  public cancelable: boolean = true\n  public defaultPrevented: boolean = false\n  public bubbles: boolean = true\n  public lengthComputable: boolean = true\n  public loaded: number = 0\n  public total: number = 0\n\n  cancelBubble: boolean = false\n  returnValue: boolean = true\n\n  constructor(\n    type: string,\n    options?: { target: EventTarget; currentTarget: EventTarget }\n  ) {\n    this.type = type\n    this.target = options?.target || null\n    this.currentTarget = options?.currentTarget || null\n    this.timeStamp = Date.now()\n  }\n\n  public composedPath(): EventTarget[] {\n    return []\n  }\n\n  public initEvent(type: string, bubbles?: boolean, cancelable?: boolean) {\n    this.type = type\n    this.bubbles = !!bubbles\n    this.cancelable = !!cancelable\n  }\n\n  public preventDefault() {\n    this.defaultPrevented = true\n  }\n\n  public stopPropagation() {}\n  public stopImmediatePropagation() {}\n}\n", "import { EventPolyfill } from './EventPolyfill'\n\nexport class ProgressEventPolyfill extends EventPolyfill {\n  readonly lengthComputable: boolean\n  readonly composed: boolean\n  readonly loaded: number\n  readonly total: number\n\n  constructor(type: string, init?: ProgressEventInit) {\n    super(type)\n\n    this.lengthComputable = init?.lengthComputable || false\n    this.composed = init?.composed || false\n    this.loaded = init?.loaded || 0\n    this.total = init?.total || 0\n  }\n}\n", "import { EventPolyfill } from '../polyfills/EventPolyfill'\nimport { ProgressEventPolyfill } from '../polyfills/ProgressEventPolyfill'\n\nconst SUPPORTS_PROGRESS_EVENT = typeof ProgressEvent !== 'undefined'\n\nexport function createEvent(\n  target: XMLHttpRequest,\n  type: string,\n  init?: ProgressEventInit\n): EventPolyfill {\n  const progressEvents = [\n    'error',\n    'progress',\n    'loadstart',\n    'loadend',\n    'load',\n    'timeout',\n    'abort',\n  ]\n\n  /**\n   * `ProgressEvent` is not supported in React Native.\n   * @see https://github.com/mswjs/interceptors/issues/40\n   */\n  const ProgressEventClass = SUPPORTS_PROGRESS_EVENT\n    ? ProgressEvent\n    : ProgressEventPolyfill\n\n  const event = progressEvents.includes(type)\n    ? new ProgressEventClass(type, {\n        lengthComputable: true,\n        loaded: init?.loaded || 0,\n        total: init?.total || 0,\n      })\n    : new EventPolyfill(type, {\n        target,\n        currentTarget: target,\n      })\n\n  return event\n}\n", "/**\n * Returns the source object of the given property on the target object\n * (the target itself, any parent in its prototype, or null).\n */\nexport function findPropertySource(\n  target: object,\n  propertyName: string | symbol\n): object | null {\n  if (!(propertyName in target)) {\n    return null\n  }\n\n  const hasProperty = Object.prototype.hasOwnProperty.call(target, propertyName)\n  if (hasProperty) {\n    return target\n  }\n\n  const prototype = Reflect.getPrototypeOf(target)\n  return prototype ? findPropertySource(prototype, propertyName) : null\n}\n", "import { findPropertySource } from './findPropertySource'\n\nexport interface ProxyOptions<Target extends Record<string, any>> {\n  constructorCall?(args: Array<unknown>, next: NextFunction<Target>): Target\n\n  methodCall?<F extends keyof Target>(\n    this: Target,\n    data: [methodName: F, args: Array<unknown>],\n    next: NextFunction<void>\n  ): void\n\n  setProperty?(\n    data: [propertyName: string | symbol, nextValue: unknown],\n    next: NextFunction<boolean>\n  ): boolean\n\n  getProperty?(\n    data: [propertyName: string | symbol, receiver: Target],\n    next: NextFunction<void>\n  ): void\n}\n\nexport type NextFunction<ReturnType> = () => ReturnType\n\nexport function createProxy<Target extends object>(\n  target: Target,\n  options: ProxyOptions<Target>\n): Target {\n  const proxy = new Proxy(target, optionsToProxyHandler(options))\n\n  return proxy\n}\n\nfunction optionsToProxyHandler<T extends Record<string, any>>(\n  options: ProxyOptions<T>\n): ProxyHandler<T> {\n  const { constructorCall, methodCall, getProperty, setProperty } = options\n  const handler: Proxy<PERSON>andler<T> = {}\n\n  if (typeof constructorCall !== 'undefined') {\n    handler.construct = function (target, args, newTarget) {\n      const next = Reflect.construct.bind(null, target as any, args, newTarget)\n      return constructorCall.call(newTarget, args, next)\n    }\n  }\n\n  handler.set = function (target, propertyName, nextValue) {\n    const next = () => {\n      const propertySource = findPropertySource(target, propertyName) || target\n      const ownDescriptors = Reflect.getOwnPropertyDescriptor(\n        propertySource,\n        propertyName\n      )\n\n      // Respect any custom setters present for this property.\n      if (typeof ownDescriptors?.set !== 'undefined') {\n        ownDescriptors.set.apply(target, [nextValue])\n        return true\n      }\n\n      // Otherwise, set the property on the source.\n      return Reflect.defineProperty(propertySource, propertyName, {\n        writable: true,\n        enumerable: true,\n        configurable: true,\n        value: nextValue,\n      })\n    }\n\n    if (typeof setProperty !== 'undefined') {\n      return setProperty.call(target, [propertyName, nextValue], next)\n    }\n\n    return next()\n  }\n\n  handler.get = function (target, propertyName, receiver) {\n    /**\n     * @note Using `Reflect.get()` here causes \"TypeError: Illegal invocation\".\n     */\n    const next = () => target[propertyName as any]\n\n    const value =\n      typeof getProperty !== 'undefined'\n        ? getProperty.call(target, [propertyName, receiver], next)\n        : next()\n\n    if (typeof value === 'function') {\n      return (...args: Array<any>) => {\n        const next = value.bind(target, ...args)\n\n        if (typeof methodCall !== 'undefined') {\n          return methodCall.call(target, [propertyName as any, args], next)\n        }\n\n        return next()\n      }\n    }\n\n    return value\n  }\n\n  return handler\n}\n", "export function isDomParserSupportedType(\n  type: string\n): type is DOMParserSupportedType {\n  const supportedTypes: Array<DOMParserSupportedType> = [\n    'application/xhtml+xml',\n    'application/xml',\n    'image/svg+xml',\n    'text/html',\n    'text/xml',\n  ]\n  return supportedTypes.some((supportedType) => {\n    return type.startsWith(supportedType)\n  })\n}\n", "/**\n * Parses a given string into JSON.\n * Gracefully handles invalid JSON by returning `null`.\n */\nexport function parseJson(data: string): Record<string, unknown> | null {\n  try {\n    const json = JSON.parse(data)\n    return json\n  } catch (_) {\n    return null\n  }\n}\n", "import { isResponseWithoutBody } from '../../../utils/responseUtils'\n\n/**\n * Creates a Fetch API `Response` instance from the given\n * `XMLHttpRequest` instance and a response body.\n */\nexport function createResponse(\n  request: XMLHttpRequest,\n  body: BodyInit | null\n): Response {\n  /**\n   * Handle XMLHttpRequest responses that must have null as the\n   * response body when represented using Fetch API Response.\n   * XMLHttpRequest response will always have an empty string\n   * as the \"request.response\" in those cases, resulting in an error\n   * when constructing a Response instance.\n   * @see https://github.com/mswjs/interceptors/issues/379\n   */\n  const responseBodyOrNull = isResponseWithoutBody(request.status) ? null : body\n\n  return new Response(responseBodyOrNull, {\n    status: request.status,\n    statusText: request.statusText,\n    headers: createHeadersFromXMLHttpReqestHeaders(\n      request.getAllResponseHeaders()\n    ),\n  })\n}\n\nfunction createHeadersFromXMLHttpReqestHeaders(headersString: string): Headers {\n  const headers = new Headers()\n\n  const lines = headersString.split(/[\\r\\n]+/)\n  for (const line of lines) {\n    if (line.trim() === '') {\n      continue\n    }\n\n    const [name, ...parts] = line.split(': ')\n    const value = parts.join(': ')\n\n    headers.append(name, value)\n  }\n\n  return headers\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { until } from '@open-draft/until'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { toInteractiveRequest } from '../../utils/toInteractiveRequest'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { canParseUrl } from '../../utils/canParseUrl'\nimport { createRequestId } from '../../createRequestId'\nimport {\n  createServerErrorResponse,\n  isResponseError,\n} from '../../utils/responseUtils'\n\nexport class FetchInterceptor extends Interceptor<HttpRequestEventMap> {\n  static symbol = Symbol('fetch')\n\n  constructor() {\n    super(FetchInterceptor.symbol)\n  }\n\n  protected checkEnvironment() {\n    return (\n      typeof globalThis !== 'undefined' &&\n      typeof globalThis.fetch !== 'undefined'\n    )\n  }\n\n  protected async setup() {\n    const pureFetch = globalThis.fetch\n\n    invariant(\n      !(pureFetch as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"fetch\" module: already patched.'\n    )\n\n    globalThis.fetch = async (input, init) => {\n      const requestId = createRequestId()\n\n      /**\n       * @note Resolve potentially relative request URL\n       * against the present `location`. This is mainly\n       * for native `fetch` in JSDOM.\n       * @see https://github.com/mswjs/msw/issues/1625\n       */\n      const resolvedInput =\n        typeof input === 'string' &&\n        typeof location !== 'undefined' &&\n        !canParseUrl(input)\n          ? new URL(input, location.origin)\n          : input\n\n      const request = new Request(resolvedInput, init)\n\n      this.logger.info('[%s] %s', request.method, request.url)\n\n      const { interactiveRequest, requestController } =\n        toInteractiveRequest(request)\n\n      this.logger.info(\n        'emitting the \"request\" event for %d listener(s)...',\n        this.emitter.listenerCount('request')\n      )\n\n      this.emitter.once('request', ({ requestId: pendingRequestId }) => {\n        if (pendingRequestId !== requestId) {\n          return\n        }\n\n        if (requestController.responsePromise.state === 'pending') {\n          requestController.responsePromise.resolve(undefined)\n        }\n      })\n\n      this.logger.info('awaiting for the mocked response...')\n\n      const signal = interactiveRequest.signal\n      const requestAborted = new DeferredPromise()\n\n      // Signal isn't always defined in react-native.\n      if (signal) {\n        signal.addEventListener(\n          'abort',\n          () => {\n            requestAborted.reject(signal.reason)\n          },\n          { once: true }\n        )\n      }\n\n      const responsePromise = new DeferredPromise<Response>()\n\n      const respondWith = (response: Response): void => {\n        this.logger.info('responding with a mock response:', response)\n\n        if (this.emitter.listenerCount('response') > 0) {\n          this.logger.info('emitting the \"response\" event...')\n\n          // Clone the mocked response for the \"response\" event listener.\n          // This way, the listener can read the response and not lock its body\n          // for the actual fetch consumer.\n          const responseClone = response.clone()\n\n          this.emitter.emit('response', {\n            response: responseClone,\n            isMockedResponse: true,\n            request: interactiveRequest,\n            requestId,\n          })\n        }\n\n        // Set the \"response.url\" property to equal the intercepted request URL.\n        Object.defineProperty(response, 'url', {\n          writable: false,\n          enumerable: true,\n          configurable: false,\n          value: request.url,\n        })\n\n        responsePromise.resolve(response)\n      }\n\n      const errorWith = (reason: unknown): void => {\n        responsePromise.reject(reason)\n      }\n\n      const resolverResult = await until<unknown, Response | undefined>(\n        async () => {\n          const listenersFinished = emitAsync(this.emitter, 'request', {\n            request: interactiveRequest,\n            requestId,\n          })\n\n          await Promise.race([\n            requestAborted,\n            // Put the listeners invocation Promise in the same race condition\n            // with the request abort Promise because otherwise awaiting the listeners\n            // would always yield some response (or undefined).\n            listenersFinished,\n            requestController.responsePromise,\n          ])\n\n          this.logger.info('all request listeners have been resolved!')\n\n          const mockedResponse = await requestController.responsePromise\n          this.logger.info('event.respondWith called with:', mockedResponse)\n\n          return mockedResponse\n        }\n      )\n\n      if (requestAborted.state === 'rejected') {\n        this.logger.info(\n          'request has been aborted:',\n          requestAborted.rejectionReason\n        )\n\n        responsePromise.reject(requestAborted.rejectionReason)\n        return responsePromise\n      }\n\n      if (resolverResult.error) {\n        this.logger.info(\n          'request listerner threw an error:',\n          resolverResult.error\n        )\n\n        // Treat thrown Responses as mocked responses.\n        if (resolverResult.error instanceof Response) {\n          // Treat thrown Response.error() as a request error.\n          if (isResponseError(resolverResult.error)) {\n            errorWith(createNetworkError(resolverResult.error))\n          } else {\n            // Treat the rest of thrown Responses as mocked responses.\n            respondWith(resolverResult.error)\n          }\n        }\n\n        // Emit the \"unhandledException\" interceptor event so the client\n        // can opt-out from exceptions translating to 500 error responses.\n\n        if (this.emitter.listenerCount('unhandledException') > 0) {\n          await emitAsync(this.emitter, 'unhandledException', {\n            error: resolverResult.error,\n            request,\n            requestId,\n            controller: {\n              respondWith,\n              errorWith,\n            },\n          })\n\n          if (responsePromise.state !== 'pending') {\n            return responsePromise\n          }\n        }\n\n        // Unhandled exceptions in the request listeners are\n        // synonymous to unhandled exceptions on the server.\n        // Those are represented as 500 error responses.\n        respondWith(createServerErrorResponse(resolverResult.error))\n        return responsePromise\n      }\n\n      const mockedResponse = resolverResult.data\n\n      if (mockedResponse && !request.signal?.aborted) {\n        this.logger.info('received mocked response:', mockedResponse)\n\n        // Reject the request Promise on mocked \"Response.error\" responses.\n        if (isResponseError(mockedResponse)) {\n          this.logger.info(\n            'received a network error response, rejecting the request promise...'\n          )\n\n          /**\n           * Set the cause of the request promise rejection to the\n           * network error Response instance. This differs from Undici.\n           * Undici will forward the \"response.error\" custom property\n           * as the rejection reason but for \"Response.error()\" static method\n           * \"response.error\" will equal to undefined, making \"cause\" an empty Error.\n           * @see https://github.com/nodejs/undici/blob/83cb522ae0157a19d149d72c7d03d46e34510d0a/lib/fetch/response.js#L344\n           */\n          errorWith(createNetworkError(mockedResponse))\n        } else {\n          respondWith(mockedResponse)\n        }\n\n        return responsePromise\n      }\n\n      this.logger.info('no mocked response received!')\n\n      return pureFetch(request).then((response) => {\n        this.logger.info('original fetch performed', response)\n\n        if (this.emitter.listenerCount('response') > 0) {\n          this.logger.info('emitting the \"response\" event...')\n\n          const responseClone = response.clone()\n\n          this.emitter.emit('response', {\n            response: responseClone,\n            isMockedResponse: false,\n            request: interactiveRequest,\n            requestId,\n          })\n        }\n\n        return response\n      })\n    }\n\n    Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.fetch = pureFetch\n\n      this.logger.info(\n        'restored native \"globalThis.fetch\"!',\n        globalThis.fetch.name\n      )\n    })\n  }\n}\n\nfunction createNetworkError(cause: unknown) {\n  return Object.assign(new TypeError('Failed to fetch'), {\n    cause,\n  })\n}\n", "/**\n * Returns a boolean indicating whether the given URL string\n * can be parsed into a `URL` instance.\n * A substitute for `URL.canParse()` for Node.js 18.\n */\nexport function canParseUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n", "/**\n * Determines if the given value is an object.\n */\nexport function isObject(value: any): boolean {\n  return value != null && typeof value === 'object' && !Array.isArray(value)\n}\n", "import { isObject } from './isObject'\n\n/**\n * Deeply merges two given objects with the right one\n * having a priority during property assignment.\n */\nexport function mergeRight(\n  left: Record<string, any>,\n  right: Record<string, any>,\n) {\n  return Object.entries(right).reduce(\n    (result, [key, rightValue]) => {\n      const leftValue = result[key]\n\n      if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n        result[key] = leftValue.concat(rightValue)\n        return result\n      }\n\n      if (isObject(leftValue) && isObject(rightValue)) {\n        result[key] = mergeRight(leftValue, rightValue)\n        return result\n      }\n\n      result[key] = rightValue\n      return result\n    },\n    Object.assign({}, left),\n  )\n}\n", "import { HttpResponse, http } from 'msw';\nimport { SetupServer, setupServer } from 'msw/node';\n\nexport class JsonTestServer {\n  readonly server: SetupServer;\n\n  responseHeaders: Record<string, string> = {};\n  responseBodyJson: any = {};\n\n  request: Request | undefined;\n\n  constructor(url: string) {\n    const responseBodyJson = () => this.responseBodyJson;\n\n    this.server = setupServer(\n      http.post(url, ({ request }) => {\n        this.request = request;\n\n        return HttpResponse.json(responseBodyJson(), {\n          headers: {\n            'Content-Type': 'application/json',\n            ...this.responseHeaders,\n          },\n        });\n      }),\n    );\n  }\n\n  async getRequestBodyJson() {\n    expect(this.request).toBeDefined();\n    return JSON.parse(await this.request!.text());\n  }\n\n  async getRequestHeaders() {\n    expect(this.request).toBeDefined();\n    const requestHeaders = this.request!.headers;\n\n    // convert headers to object for easier comparison\n    const headersObject: Record<string, string> = {};\n    requestHeaders.forEach((value, key) => {\n      headersObject[key] = value;\n    });\n\n    return headersObject;\n  }\n\n  async getRequestUrlSearchParams() {\n    expect(this.request).toBeDefined();\n    return new URL(this.request!.url).searchParams;\n  }\n\n  async getRequestUrl() {\n    expect(this.request).toBeDefined();\n    return new URL(this.request!.url).toString();\n  }\n\n  setupTestEnvironment() {\n    beforeAll(() => this.server.listen());\n    beforeEach(() => {\n      this.responseBodyJson = {};\n      this.request = undefined;\n    });\n    afterEach(() => this.server.resetHandlers());\n    afterAll(() => this.server.close());\n  }\n}\n", "import { HttpResponse, http } from 'msw';\nimport { SetupServer, setupServer } from 'msw/node';\n\nexport class StreamingTestServer {\n  readonly server: SetupServer;\n\n  responseHeaders: Record<string, string> = {};\n  responseChunks: any[] = [];\n\n  request: Request | undefined;\n\n  constructor(url: string) {\n    const responseChunks = () => this.responseChunks;\n\n    this.server = setupServer(\n      http.post(url, ({ request }) => {\n        this.request = request;\n\n        const encoder = new TextEncoder();\n        const stream = new ReadableStream({\n          async start(controller) {\n            try {\n              for (const chunk of responseChunks()) {\n                controller.enqueue(encoder.encode(chunk));\n              }\n            } finally {\n              controller.close();\n            }\n          },\n        });\n\n        return new HttpResponse(stream, {\n          status: 200,\n          headers: {\n            'Content-Type': 'text/event-stream',\n            'Cache-Control': 'no-cache',\n            Connection: 'keep-alive',\n            ...this.responseHeaders,\n          },\n        });\n      }),\n    );\n  }\n\n  async getRequestBodyJson() {\n    expect(this.request).toBeDefined();\n    return JSON.parse(await this.request!.text());\n  }\n\n  async getRequestHeaders() {\n    expect(this.request).toBeDefined();\n    const requestHeaders = this.request!.headers;\n\n    // convert headers to object for easier comparison\n    const headersObject: Record<string, string> = {};\n    requestHeaders.forEach((value, key) => {\n      headersObject[key] = value;\n    });\n\n    return headersObject;\n  }\n\n  async getRequestUrlSearchParams() {\n    expect(this.request).toBeDefined();\n    return new URL(this.request!.url).searchParams;\n  }\n\n  setupTestEnvironment() {\n    beforeAll(() => this.server.listen());\n    beforeEach(() => {\n      this.responseChunks = [];\n      this.request = undefined;\n    });\n    afterEach(() => this.server.resetHandlers());\n    afterAll(() => this.server.close());\n  }\n}\n", "import { HttpResponse, JsonBodyType, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { convertArrayToReadableStream } from './convert-array-to-readable-stream';\n\nexport type TestServerJsonBodyType = JsonBodyType;\n\nexport type TestServerResponse = {\n  url: string;\n  headers?: Record<string, string>;\n} & (\n  | {\n      type: 'json-value';\n      content: TestServerJsonBodyType;\n    }\n  | {\n      type: 'stream-values';\n      content: Array<string>;\n    }\n  | {\n      type: 'controlled-stream';\n      id?: string;\n    }\n  | {\n      type: 'error';\n      status: number;\n      content?: string;\n    }\n);\n\nclass TestServerCall {\n  constructor(private request: Request) {}\n\n  async getRequestBodyJson() {\n    expect(this.request).toBeDefined();\n    return JSON.parse(await this.request!.text());\n  }\n\n  getRequestHeaders() {\n    expect(this.request).toBeDefined();\n    const requestHeaders = this.request!.headers;\n\n    // convert headers to object for easier comparison\n    const headersObject: Record<string, string> = {};\n    requestHeaders.forEach((value, key) => {\n      headersObject[key] = value;\n    });\n\n    return headersObject;\n  }\n\n  getRequestUrlSearchParams() {\n    expect(this.request).toBeDefined();\n    return new URL(this.request!.url).searchParams;\n  }\n}\n\nfunction createServer({\n  responses,\n  pushCall,\n  pushController,\n}: {\n  responses: Array<TestServerResponse> | TestServerResponse;\n  pushCall: (call: TestServerCall) => void;\n  pushController: (\n    id: string,\n    controller: () => ReadableStreamDefaultController<string>,\n  ) => void;\n}) {\n  // group responses by url\n  const responsesArray = Array.isArray(responses) ? responses : [responses];\n  const responsesByUrl = responsesArray.reduce((responsesByUrl, response) => {\n    if (!responsesByUrl[response.url]) {\n      responsesByUrl[response.url] = [];\n    }\n    responsesByUrl[response.url].push(response);\n    return responsesByUrl;\n  }, {} as Record<string, Array<TestServerResponse>>);\n\n  // create stream/streamController pairs for controlled-stream responses\n  const streams = {} as Record<string, ReadableStream<string>>;\n  responsesArray\n    .filter(\n      (\n        response,\n      ): response is TestServerResponse & { type: 'controlled-stream' } =>\n        response.type === 'controlled-stream',\n    )\n    .forEach(response => {\n      let streamController: ReadableStreamDefaultController<string>;\n\n      const stream = new ReadableStream<string>({\n        start(controller) {\n          streamController = controller;\n        },\n      });\n\n      pushController(response.id ?? '', () => streamController);\n      streams[response.id ?? ''] = stream;\n    });\n\n  // keep track of url invocation counts:\n  const urlInvocationCounts = Object.fromEntries(\n    Object.entries(responsesByUrl).map(([url]) => [url, 0]),\n  );\n\n  return setupServer(\n    ...Object.entries(responsesByUrl).map(([url, responses]) => {\n      return http.post(url, ({ request }) => {\n        pushCall(new TestServerCall(request));\n\n        const invocationCount = urlInvocationCounts[url]++;\n        const response =\n          responses[\n            invocationCount > responses.length\n              ? responses.length - 1\n              : invocationCount\n          ];\n\n        switch (response.type) {\n          case 'json-value':\n            return HttpResponse.json(response.content, {\n              status: 200,\n              headers: {\n                'Content-Type': 'application/json',\n                ...response.headers,\n              },\n            });\n\n          case 'stream-values':\n            return new HttpResponse(\n              convertArrayToReadableStream(response.content).pipeThrough(\n                new TextEncoderStream(),\n              ),\n              {\n                status: 200,\n                headers: {\n                  'Content-Type': 'text/event-stream',\n                  'Cache-Control': 'no-cache',\n                  Connection: 'keep-alive',\n                  ...response.headers,\n                },\n              },\n            );\n\n          case 'controlled-stream': {\n            return new HttpResponse(\n              streams[response.id ?? ''].pipeThrough(new TextEncoderStream()),\n              {\n                status: 200,\n                headers: {\n                  'Content-Type': 'text/event-stream',\n                  'Cache-Control': 'no-cache',\n                  Connection: 'keep-alive',\n                  ...response.headers,\n                },\n              },\n            );\n          }\n\n          case 'error':\n            return HttpResponse.text(response.content ?? 'Error', {\n              status: response.status,\n              headers: {\n                ...response.headers,\n              },\n            });\n        }\n      });\n    }),\n  );\n}\n\nexport function withTestServer(\n  responses: Array<TestServerResponse> | TestServerResponse,\n  testFunction: (options: {\n    calls: () => Array<TestServerCall>;\n    call: (index: number) => TestServerCall;\n    getStreamController: (\n      id: string,\n    ) => ReadableStreamDefaultController<string>;\n    streamController: ReadableStreamDefaultController<string>;\n  }) => Promise<void>,\n) {\n  return async () => {\n    const calls: Array<TestServerCall> = [];\n    const controllers: Record<\n      string,\n      () => ReadableStreamDefaultController<string>\n    > = {};\n    const server = createServer({\n      responses,\n      pushCall: call => calls.push(call),\n      pushController: (id, controller) => {\n        controllers[id] = controller;\n      },\n    });\n\n    try {\n      server.listen();\n\n      await testFunction({\n        calls: () => calls,\n        call: (index: number) => calls[index],\n        getStreamController: (id: string) => {\n          return controllers[id]();\n        },\n        get streamController() {\n          return controllers['']();\n        },\n      });\n    } finally {\n      server.close();\n    }\n  };\n}\n\nexport function describeWithTestServer(\n  description: string,\n  responses: Array<TestServerResponse> | TestServerResponse,\n  testFunction: (options: {\n    calls: () => Array<TestServerCall>;\n    call: (index: number) => TestServerCall;\n    getStreamController: (\n      id: string,\n    ) => ReadableStreamDefaultController<string>;\n    streamController: ReadableStreamDefaultController<string>;\n  }) => void,\n) {\n  describe(description, () => {\n    let calls: Array<TestServerCall>;\n    let controllers: Record<\n      string,\n      () => ReadableStreamDefaultController<string>\n    >;\n    let server: ReturnType<typeof setupServer>;\n\n    beforeAll(() => {\n      server = createServer({\n        responses,\n        pushCall: call => calls.push(call),\n        pushController: (id, controller) => {\n          controllers[id] = controller;\n        },\n      });\n      server.listen();\n    });\n\n    beforeEach(() => {\n      calls = [];\n      controllers = {};\n      server.resetHandlers();\n    });\n\n    afterAll(() => {\n      server.close();\n    });\n\n    testFunction({\n      calls: () => calls,\n      call: (index: number) => calls[index],\n      getStreamController: (id: string) => controllers[id](),\n      get streamController() {\n        return controllers['']();\n      },\n    });\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,SAAS,4BAA+B,QAA+B;AAC5E,SAAO;AAAA,IACL,QAAQ,OAAO,aAAa,IAAI;AAC9B,iBAAW,SAAS,QAAQ;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;;;ACRO,SAAS,6BACd,QACmB;AACnB,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,YAAY;AAChB,UAAI;AACF,mBAAW,SAAS,QAAQ;AAC1B,qBAAW,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF,UAAE;AACA,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACdA,eAAsB,4BACpB,UACc;AACd,QAAM,SAAc,CAAC;AACrB,mBAAiB,QAAQ,UAAU;AACjC,WAAO,KAAK,IAAI;AAAA,EAClB;AACA,SAAO;AACT;;;ACRA,eAAsB,6BACpB,QACc;AACd,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,SAAc,CAAC;AAErB,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI;AAAM;AACV,WAAO,KAAK,KAAK;AAAA,EACnB;AAEA,SAAO;AACT;;;ACXA,eAAsB,6BACpB,UACmB;AACnB,SAAO;AAAA,IACL,SAAS,KAAM,YAAY,IAAI,kBAAkB,CAAC;AAAA,EACpD;AACF;;;ACRA,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,YAAiB,MAAmB;AAC/D,UAAQ,MAAM;IAEZ,KAAK;AACH,aAAO;IAGT,KAAK;IACL,KAAK;AACH,aAAO,OAAO,UAAU;IAG1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;IAGlC,KAAK,KAAK;AAER,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;MACT;AAEA,YAAM,OAAO,KAAK,UAAU,UAAU;AAGtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;MACT;AAEA,aAAO;IACT;EACF;AACF;AAEO,SAAS,OAAOA,aAAoB,aAA4B;AACrE,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAOA;EACT;AAEA,MAAI,kBAAkB;AACtB,MAAI,mBAAmBA,SAAQ;IAC7B;IACA,CAACC,QAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY,eAAA;AAC/B,YAAM,QAAQ,oBAAoB,YAAY,IAAI;AAElD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;MACT;AAEA,aAAOA;IACT;EACF;AAGA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG,CAAA;EACrE;AAEA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAE1D,SAAO;AACT;AC/DA,IAAM,yBAAyB;AAO/B,SAAS,gBAAgBC,QAAoB;AAC3C,MAAI,CAACA,OAAM,OAAO;AAChB;EACF;AAEA,QAAM,YAAYA,OAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAG,sBAAsB;AAC1C,EAAAA,OAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AAEO,IAAM,iBAAN,cAA6B,MAAM;EAGxC,YAA4BF,aAAoB,aAAoB;AAClE,UAAMA,QAAO;AADa,SAAA,UAAAA;AAF5B,SAAA,OAAO;AAIL,SAAK,UAAU,OAAOA,UAAS,GAAG,WAAW;AAC7C,oBAAgB,IAAI;EACtB;AACF;AA2BO,IAAM,YAAuB,CAClC,WACAA,aACG,gBACmB;AACtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAeA,UAAS,GAAG,WAAW;EAClD;AACF;AAEA,UAAU,KAAK,CAAC,kBAAkB,WAAWA,aAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAMG,iBACJ,YAAY,WAAW,IAAIH,WAAU,OAAOA,UAAS,GAAG,WAAW;AACrE,QAAIE;AAEJ,QAAI;AACF,MAAAA,SAAQ,QAAQ,UAAU,kBAA4C;QACpEC;MACF,CAAC;IACH,SAAS,KAAP;AACA,MAAAD,SAAS,iBAAwCC,cAAa;IAChE;AAEA,UAAMD;EACR;AACF;;;AC9EA,IAAM,iBAAiB;AAKvB,SAAS,cAAcE,aAAoB,aAA4B;AACrE,QAAM,sBAAsB,OAAOA,UAAS,GAAG,WAAW;AAC1D,SAAO,GAAG,cAAc,IAAI,mBAAmB;AACjD;AAKA,SAAS,KAAKA,aAAoB,aAA0B;AAC1D,UAAQ,KAAK,cAAcA,UAAS,GAAG,WAAW,CAAC;AACrD;AAKA,SAAS,MAAMA,aAAoB,aAA0B;AAC3D,UAAQ,MAAM,cAAcA,UAAS,GAAG,WAAW,CAAC;AACtD;AAEO,IAAM,WAAW;EACtB;EACA;EACA;AACF;AAQO,IAAM,gBAAN,cAA4B,MAAM;EACvC,YAAYA,UAAiB;AAC3B,UAAMA,QAAO;AACb,SAAK,OAAO;EACd;AACF;;;ACxCO,SAAS,eAAe;AAO7B;IACE,OAAO,QAAQ;IACf,SAAS;MACP;IACF;EACF;AACF;;;ACdO,IAAM,kBAAN,cAA8B,MAAM;EACzC,YACkB,SACA,MACA,OAChB;AACA;MACE,+CAA+C,KAAA,IAAS,KAAK,SAAS,CAAA;IACxE;AANgB,SAAA,UAAA;AACA,SAAA,OAAA;AACA,SAAA,QAAA;AAKhB,SAAK,OAAO;EACd;AACF;ACSO,IAAM,WAAN,MAAuC;EAO5C,OAAO,cACL,SACA,WACQ;AACR,WAAO,QAAQ,cAAmB,SAAS;EAC7C;EAEA,cAAc;AACZ,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,eAAe,SAAQ;AAC5B,SAAK,oCAAoC;EAC3C;EAEQ,mBACN,mBACA,WACA,UACM;AACN,SAAK;MACH;MAEA,GAAI,CAAC,WAAW,QAAQ;IAE1B;EACF;EAEQ,cACN,WACiC;AAGjC,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,KAAK,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC;EAC1E;EAEQ,gBACN,WACA,UACoC;AACpC,UAAM,QAAQ,UAAU,QAAQ,QAAQ;AAExC,QAAI,QAAQ,IAAI;AACd,gBAAU,OAAO,OAAO,CAAC;IAC3B;AAEA,WAAO,CAAC;EACV;EAEQ,kBACN,WACA,UAC6B;AAC7B,UAAM,eAAe,IAAI,SAA+B;AACtD,WAAK,eAAe,WAAW,YAAY;AAM3C,aAAO,SAAS,MAAM,MAAM,IAAI;IAClC;AAGA,WAAO,eAAe,cAAc,QAAQ,EAAE,OAAO,SAAS,KAAK,CAAC;AAEpE,WAAO;EACT;EAEO,gBAAgB,cAA4B;AACjD,SAAK,eAAe;AACpB,WAAO;EACT;;;;;;EAOO,kBAA0B;AAC/B,WAAO,KAAK;EACd;;;;;EAMO,aAAkC;AACvC,WAAO,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;EACtC;;;;;;;;;;EAWO,KACL,cACG,MACM;AACT,UAAM,YAAY,KAAK,cAAc,SAAS;AAC9C,cAAU,QAAQ,CAAC,aAAa;AAC9B,eAAS,MAAM,MAAM,IAAI;IAC3B,CAAC;AAED,WAAO,UAAU,SAAS;EAC5B;EAUO,YACL,WACA,UACM;AAEN,SAAK,mBAAmB,eAAe,WAAW,QAAQ;AAE1D,UAAM,gBAAgB,KAAK,cAAc,SAAS,EAAE,OAAO,QAAQ;AACnE,SAAK,OAAO,IAAI,WAAW,aAAa;AAExC,QACE,KAAK,eAAe,KACpB,KAAK,cAAc,SAAS,IAAI,KAAK,gBACrC,CAAC,KAAK,mCACN;AACA,WAAK,oCAAoC;AAEzC,YAAM,oBAAoB,IAAI;QAC5B;QACA;QACA,KAAK,cAAc,SAAS;MAC9B;AACA,cAAQ,KAAK,iBAAiB;IAChC;AAEA,WAAO;EACT;EAUO,GACL,WACA,UACM;AACN,WAAO,KAAK,YAAY,WAAW,QAAQ;EAC7C;EAUO,KACL,WACA,UACM;AACN,WAAO,KAAK;MACV;MACA,KAAK,kBAAkB,WAAW,QAAQ;IAC5C;EACF;EAUO,gBACL,WACA,UACM;AACN,UAAM,YAAY,KAAK,cAAc,SAAS;AAE9C,QAAI,UAAU,SAAS,GAAG;AACxB,YAAM,gBAAgB,CAAC,QAAQ,EAAE,OAAO,SAAS;AACjD,WAAK,OAAO,IAAI,WAAW,aAAa;IAC1C,OAAO;AACL,WAAK,OAAO,IAAI,WAAW,UAAU,OAAO,QAAQ,CAAC;IACvD;AAEA,WAAO;EACT;EAUO,oBACL,WACA,UACM;AACN,WAAO,KAAK;MACV;MACA,KAAK,kBAAkB,WAAW,QAAQ;IAC5C;EACF;EAUO,eACL,WACA,UACM;AACN,UAAM,YAAY,KAAK,cAAc,SAAS;AAE9C,QAAI,UAAU,SAAS,GAAG;AACxB,WAAK,gBAAgB,WAAW,QAAQ;AACxC,WAAK,OAAO,IAAI,WAAW,SAAS;AAGpC,WAAK,mBAAmB,kBAAkB,WAAW,QAAQ;IAC/D;AAEA,WAAO;EACT;;;;;;;EAgBO,IACL,WACA,UACM;AACN,WAAO,KAAK,eAAe,WAAW,QAAQ;EAChD;EAMO,mBACL,WACM;AACN,QAAI,WAAW;AACb,WAAK,OAAO,OAAO,SAAS;IAC9B,OAAO;AACL,WAAK,OAAO,MAAM;IACpB;AAEA,WAAO;EACT;;;;EASO,UAAU,WAA8C;AAC7D,WAAO,MAAM,KAAK,KAAK,cAAc,SAAS,CAAC;EACjD;;;;EASO,cAAc,WAAsD;AACzE,WAAO,KAAK,cAAc,SAAS,EAAE;EACvC;EAEO,aACL,WACoC;AACpC,WAAO,KAAK,UAAU,SAAS;EACjC;AACF;AA7TO,IAAM,UAAN;AAAM,QAKJ,sBAAsB;;;ACtBxB,SAAS,WACd,QACA,aACM;AACN,QAAM,UAAuD,OAAO;AAEpE,MAAI,QAAQ,UAAU;AACpB;EACF;AAEA,QAAM,aACJ,SAASC,YAAgC,UAAU,MAAM;AACvD,gBAAY,KAAK,OAAO,GAAG,IAAI;AAC/B,WAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,IAAI;EAC1C;AAEF,aAAW,WAAW;AAEtB,SAAO,OAAO;AAChB;;;ACrBO,SAAS,gBAAmB,QAAoC;AACrE,QAAM,QAAQ,CAAC,GAAG,MAAM;AACxB,SAAO,OAAO,KAAK;AACnB,SAAO;AACT;;;ACLO,IAAM,aAAN,MAAiB;EAAjB;AACK,yCAA+C,CAAC;;EAEnD,UAAU;AACf,QAAI;AACJ,WAAQ,eAAe,KAAK,cAAc,MAAM,GAAI;AAClD,mBAAa;IACf;EACF;AACF;;;ACOO,IAAM,6BAAN,MAA+D;EAGpE,YAAoB,iBAAwC;AAFpD;AAEY,SAAA,kBAAA;AAClB,SAAK,WAAW,CAAC,GAAG,eAAe;EACrC;EAEO,QAAQ,gBAA6C;AAC1D,SAAK,SAAS,QAAQ,GAAG,cAAc;EACzC;EAEO,MAAM,cAA2C;AACtD,SAAK,WACH,aAAa,SAAS,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,KAAK,eAAe;EAC1E;EAEO,kBAAyC;AAC9C,WAAO,KAAK;EACd;AACF;AAKO,IAAe,WAAf,cAA4D,WAAW;EAO5E,eAAe,iBAAwC;AACrD,UAAM;AAPE;AACS;AACA;AAEH;AAKd;MACE,KAAK,iBAAiB,eAAe;MACrC,SAAS;QACP;MACF;IACF;AAEA,SAAK,qBAAqB,IAAI,2BAA2B,eAAe;AAExE,SAAK,UAAU,IAAI,QAAmB;AACtC,SAAK,gBAAgB,IAAI,QAAmB;AAC5C,eAAW,KAAK,SAAS,KAAK,aAAa;AAE3C,SAAK,SAAS,KAAK,sBAAsB;AAEzC,SAAK,cAAc,KAAK,MAAM;AAC5B,WAAK,QAAQ,mBAAmB;AAChC,WAAK,cAAc,mBAAmB;IACxC,CAAC;EACH;EAEQ,iBAAiB,UAAkD;AAEzE,WAAO,SAAS,MAAM,CAAC,YAAY,CAAC,MAAM,QAAQ,OAAO,CAAC;EAC5D;EAEO,OAAO,iBAA8C;AAC1D;MACE,KAAK,iBAAiB,eAAe;MACrC,SAAS;QACP;MACF;IACF;AAEA,SAAK,mBAAmB,QAAQ,eAAe;EACjD;EAEO,kBAAwB;AAC7B,SAAK,mBAAmB,gBAAgB,EAAE,QAAQ,CAAC,YAAY;AAC7D,cAAQ,SAAS;IACnB,CAAC;EACH;EAEO,iBAAiB,cAA2C;AACjE,SAAK,mBAAmB,MAAM,YAAY;EAC5C;EAEO,eAEL;AACA,WAAO,gBAAgB,KAAK,mBAAmB,gBAAgB,CAAC;EAClE;EAEQ,wBAA0D;AAChE,WAAO;MACL,IAAI,IAAI,SAAgB;AACtB,eAAQ,KAAK,cAAc,GAAW,GAAG,IAAI;MAC/C;MACA,gBAAgB,IAAI,SAAgB;AAClC,eAAQ,KAAK,cAAc,eAAuB,GAAG,IAAI;MAC3D;MACA,oBAAoB,IAAI,SAAgB;AACtC,eAAO,KAAK,cAAc,mBAAmB,GAAG,IAAI;MACtD;IACF;EACF;AACF;;;ACrHA,IAAM,eAAe;AAErB,IAAM,cACJ;AAKK,SAAS,aAAaC,QAAc;AAEzC,QAAM,QAAQA,OAAM;AAEpB,MAAI,CAAC,OAAO;AACV;EACF;AAEA,QAAM,SAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,CAAC;AAIlD,QAAM,mBAAmB,OAAO,KAAK,CAAC,UAAU;AAC9C,WAAO,EAAE,aAAa,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK;EAC7D,CAAC;AAED,MAAI,CAAC,kBAAkB;AACrB;EACF;AAGA,QAAM,kBAAkB,iBACrB,QAAQ,2BAA2B,IAAI,EACvC,QAAQ,MAAM,EAAE;AACnB,SAAO;AACT;;;AC/BO,SAAS,WACd,IAC2D;AAC3D,MAAI,CAAC,IAAI;AACP,WAAO;EACT;AAEA,SAAO,OAAQ,GAA0B,OAAO,QAAQ,KAAK;AAC/D;;;ACyFO,IAAe,kBAAf,MAAe,gBAKpB;EAsBA,YAAY,MAAuD;AAhB5D;AAKA;;;;;AAEG;AACF;AAKA;AACA;AAGN,SAAK,WAAW,KAAK;AACrB,SAAK,UAAU,KAAK;AAEpB,UAAM,YAAY,aAAa,IAAI,MAAM,CAAC;AAE1C,SAAK,OAAO;MACV,GAAG,KAAK;MACR;IACF;AAEA,SAAK,SAAS;EAChB;;;;;EAwBA,MAAM,MAAM,OAGc;AACxB,WAAO,CAAC;EACV;;;;;;;;EASA,MAAa,KAAK,MAGG;AACnB,UAAM,eAAe,MAAM,KAAK,MAAM;MACpC,SAAS,KAAK;MACd,mBAAmB,KAAK;IAC1B,CAAC;AAED,WAAO,KAAK,UAAU;MACpB,SAAS,KAAK;MACd;MACA,mBAAmB,KAAK;IAC1B,CAAC;EACH;EAEU,mBAAmB,OAGV;AACjB,WAAO,CAAC;EACV;;;;EAKQ,2BACNC,UACgC;AAChC,UAAM,gBAAgB,gBAAe,MAAM,IAAIA,QAAO;AAEtD,QAAI,OAAO,kBAAkB,aAAa;AACxC,aAAO;IACT;AAEA,UAAM,gBAAgBA,SAAQ,MAAM;AACpC,oBAAe,MAAM,IAAIA,UAAS,aAAa;AAE/C,WAAO;EACT;;;;;EAMA,MAAa,IAAI,MAI+C;AAlOlE,QAAAC,KAAAC;AAmOI,QAAI,KAAK,YAAUD,MAAA,KAAK,YAAL,gBAAAA,IAAc,OAAM;AACrC,aAAO;IACT;AAOA,UAAM,eAAe,KAAK,2BAA2B,KAAK,OAAO;AAEjE,UAAM,eAAe,MAAM,KAAK,MAAM;MACpC,SAAS,KAAK;MACd,mBAAmB,KAAK;IAC1B,CAAC;AACD,UAAM,yBAAyB,KAAK,UAAU;MAC5C,SAAS,KAAK;MACd;MACA,mBAAmB,KAAK;IAC1B,CAAC;AAED,QAAI,CAAC,wBAAwB;AAC3B,aAAO;IACT;AAIA,QAAI,KAAK,YAAUC,MAAA,KAAK,YAAL,gBAAAA,IAAc,OAAM;AACrC,aAAO;IACT;AAEA,SAAK,SAAS;AAId,UAAM,kBAAkB,KAAK,aAAa,KAAK,QAAQ;AAEvD,UAAM,iBAAiB,KAAK,mBAAmB;MAC7C,SAAS,KAAK;MACd;IACF,CAAC;AAED,UAAM,wBACJ,gBAAgB;MACd,GAAG;MACH,WAAW,KAAK;MAChB,SAAS,KAAK;IAChB,CAAC,EACD,MAAM,CAAC,oBAAoB;AAE3B,UAAI,2BAA2B,UAAU;AACvC,eAAO;MACT;AAGA,YAAM;IACR,CAAC;AAED,UAAM,iBAAiB,MAAM;AAE7B,UAAM,kBAAkB,KAAK,sBAAsB;;;MAGjD,SAAS;MACT,WAAW,KAAK;MAChB,UAAU;MACV;IACF,CAAC;AAED,WAAO;EACT;EAEQ,aACN,UACkC;AAClC,WAAO,OAAO,SAAmD;AAC/D,YAAM,SAAS,KAAK,qBAAsB,MAAM,SAAS,IAAI;AAE7D,UAAI,WAAiD,MAAM,GAAG;AAI5D,aAAK,SAAS;AAEd,cAAM,EAAE,OAAO,KAAK,IAAI,OAAO,OAAO,QAAQ,EAAE,EAAE,KAAK;AACvD,cAAM,eAAe,MAAM;AAE3B,YAAI,MAAM;AACR,eAAK,SAAS;QAChB;AAIA,YAAI,CAAC,gBAAgB,MAAM;AACzB;YACE,KAAK;YACL;UACF;AAIA,iBAAO,KAAK,wBAAwB,MAAM;QAC5C;AAEA,YAAI,CAAC,KAAK,mBAAmB;AAC3B,eAAK,oBAAoB;QAC3B;AAEA,YAAI,cAAc;AAGhB,eAAK,0BAA0B,6CAAc;QAC/C;AAEA,eAAO;MACT;AAEA,aAAO;IACT;EACF;EAEQ,sBAAsB,MAKkB;AAC9C,WAAO;MACL,SAAS;MACT,SAAS,KAAK;MACd,WAAW,KAAK;MAChB,UAAU,KAAK;MACf,cAAc,KAAK;IACrB;EACF;AACF;AAhQE,cANoB,iBAMb,SAAQ,oBAAI,QAGjB;AATG,IAAe,iBAAf;;;ACjGA,SAAS,cAAc,QAAgB,UAA2B;AACvE,SAAO,OAAO,YAAY,MAAM,SAAS,YAAY;AACvD;;;ACIO,SAAS,mBAAmB,QAAiC;AAClE,MAAI,SAAS,KAAK;AAChB,WAAO;EACT;AAEA,MAAI,SAAS,KAAK;AAChB,WAAO;EACT;AAEA,SAAO;AACT;;;AChBO,SAAS,eAAuB;AACrC,QAAM,MAAM,oBAAI,KAAK;AAErB,SAAO,CAAC,IAAI,SAAS,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,CAAC,EACvD,IAAI,MAAM,EACV,IAAI,CAAC,UAAU,MAAM,MAAM,GAAG,CAAC,CAAC,EAChC,IAAI,CAAC,UAAU,MAAM,SAAS,GAAG,GAAG,CAAC,EACrC,KAAK,GAAG;AACb;;;ACDA,eAAsB,iBACpBC,UACwB;AACxB,QAAM,eAAeA,SAAQ,MAAM;AACnC,QAAM,cAAc,MAAM,aAAa,KAAK;AAE5C,SAAO;IACL,KAAK,IAAI,IAAIA,SAAQ,GAAG;IACxB,QAAQA,SAAQ;IAChB,SAAS,OAAO,YAAYA,SAAQ,QAAQ,QAAQ,CAAC;IACrD,MAAM;EACR;AACF;;;ACtBA,IAAIC,YAAW,OAAO;AACtB,IAAIC,aAAY,OAAO;AACvB,IAAIC,oBAAmB,OAAO;AAC9B,IAAIC,qBAAoB,OAAO;AAC/B,IAAIC,gBAAe,OAAO;AAC1B,IAAIC,gBAAe,OAAO,UAAU;AACpC,IAAI,aAAa,CAAC,IAAI,QAAQ,SAAS,YAAY;AACjD,SAAO,QAAQ,GAAG,GAAGF,mBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,GAAG,IAAI;AAC7F;AACA,IAAIG,eAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAOH,mBAAkB,IAAI;AACpC,UAAI,CAACE,cAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,QAAAJ,WAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAOC,kBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAIK,WAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAOP,UAASI,cAAa,GAAG,CAAC,IAAI,CAAC,GAAGE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAaL,WAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,EACzG;AACF;AAGA,IAAI,gBAAgB,WAAW;AAAA,EAC7B,mCAAmCO,UAASC,SAAQ;AAClD,IAAAA,QAAO,UAAU;AAAA,MACf,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAGD,IAAI,mBAAmB,WAAW;AAAA,EAChC,iCAAiCD,UAASC,SAAQ;AAChD;AACA,QAAI,QAAQ,cAAc;AAC1B,IAAAA,QAAO,UAAU;AACjB,YAAQ,UAAU;AAClB,YAAQ,OAAO,6BAA6B,KAAK;AACjD,YAAQ,QAAQ,qBAAqB,KAAK;AAC1C,YAAQ,WAAW;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,aAAS,6BAA6B,QAAQ;AAC5C,UAAI,MAAM,CAAC;AACX,aAAO,KAAK,MAAM,EAAE,QAAQ,SAAS,YAAY,MAAM;AACrD,YAAIC,WAAU,OAAO,IAAI;AACzB,YAAI,UAAU,OAAO,IAAI;AACzB,YAAIA,SAAQ,YAAY,CAAC,IAAI;AAAA,MAC/B,CAAC;AACD,aAAO;AAAA,IACT;AACA,aAAS,qBAAqB,QAAQ;AACpC,aAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAS,QAAQ,MAAM;AACpD,eAAO,OAAO,IAAI;AAAA,MACpB,CAAC;AAAA,IACH;AACA,aAAS,cAAcA,UAAS;AAC9B,UAAI,MAAMA,SAAQ,YAAY;AAC9B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAM,GAAG,GAAG;AAC5D,cAAM,IAAI,MAAM,8BAA8BA,WAAU,GAAG;AAAA,MAC7D;AACA,aAAO,QAAQ,KAAK,GAAG;AAAA,IACzB;AACA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,SAAS,IAAI,GAAG;AAChE,cAAM,IAAI,MAAM,0BAA0B,IAAI;AAAA,MAChD;AACA,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,aAAS,QAAQ,MAAM;AACrB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,iBAAiB,IAAI;AAAA,MAC9B;AACA,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AACA,UAAI,IAAI,SAAS,MAAM,EAAE;AACzB,UAAI,CAAC,MAAM,CAAC,GAAG;AACb,eAAO,iBAAiB,CAAC;AAAA,MAC3B;AACA,aAAO,cAAc,IAAI;AAAA,IAC3B;AAAA,EACF;AACF,CAAC;AAGD,IAAI,kBAAkBH,SAAQ,iBAAiB,GAAG,CAAC;AACnD,IAAI,iBAAiB,gBAAgB;;;ACxKrC,IAAM,EAAE,QAAQ,IAAI;AASpB,eAAsB,kBACpB,UAC6B;AAC7B,QAAM,gBAAgB,SAAS,MAAM;AACrC,QAAM,eAAe,MAAM,cAAc,KAAK;AAK9C,QAAM,iBAAiB,cAAc,UAAU;AAC/C,QAAM,qBACJ,cAAc,cAAc,QAAQ,cAAc,KAAK;AAEzD,SAAO;IACL,QAAQ;IACR,YAAY;IACZ,SAAS,OAAO,YAAY,cAAc,QAAQ,QAAQ,CAAC;IAC3D,MAAM;EACR;AACF;;;ACVA,SAAS,MAAM,KAAW;AACxB,MAAM,SAAqB,CAAA;AAC3B,MAAI,IAAI;AAER,SAAO,IAAI,IAAI,QAAQ;AACrB,QAAM,OAAO,IAAI,CAAC;AAElB,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,aAAO,KAAK,EAAE,MAAM,YAAY,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AAC3D;;AAGF,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,EAAE,MAAM,gBAAgB,OAAO,KAAK,OAAO,IAAI,GAAG,EAAC,CAAE;AACjE;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACvD;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,SAAS,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACxD;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,OAAO;AACX,UAAI,IAAI,IAAI;AAEZ,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAM,OAAO,IAAI,WAAW,CAAC;AAE7B;;UAEG,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEvB,SAAS;UACT;AACA,kBAAQ,IAAI,GAAG;AACf;;AAGF;;AAGF,UAAI,CAAC;AAAM,cAAM,IAAI,UAAU,6BAAA,OAA6B,CAAC,CAAE;AAE/D,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,KAAI,CAAE;AACnD,UAAI;AACJ;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,IAAI,IAAI;AAEZ,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,cAAM,IAAI,UAAU,oCAAA,OAAoC,CAAC,CAAE;;AAG7D,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAI,IAAI,CAAC,MAAM,MAAM;AACnB,qBAAW,IAAI,GAAG,IAAI,IAAI,GAAG;AAC7B;;AAGF,YAAI,IAAI,CAAC,MAAM,KAAK;AAClB;AACA,cAAI,UAAU,GAAG;AACf;AACA;;mBAEO,IAAI,CAAC,MAAM,KAAK;AACzB;AACA,cAAI,IAAI,IAAI,CAAC,MAAM,KAAK;AACtB,kBAAM,IAAI,UAAU,uCAAA,OAAuC,CAAC,CAAE;;;AAIlE,mBAAW,IAAI,GAAG;;AAGpB,UAAI;AAAO,cAAM,IAAI,UAAU,yBAAA,OAAyB,CAAC,CAAE;AAC3D,UAAI,CAAC;AAAS,cAAM,IAAI,UAAU,sBAAA,OAAsB,CAAC,CAAE;AAE3D,aAAO,KAAK,EAAE,MAAM,WAAW,OAAO,GAAG,OAAO,QAAO,CAAE;AACzD,UAAI;AACJ;;AAGF,WAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;;AAGzD,SAAO,KAAK,EAAE,MAAM,OAAO,OAAO,GAAG,OAAO,GAAE,CAAE;AAEhD,SAAO;AACT;AAgBM,SAAU,MAAM,KAAa,SAA0B;AAA1B,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAA0B;AAC3D,MAAM,SAAS,MAAM,GAAG;AAChB,MAAAI,MAAoB,QAAO,UAA3B,WAAQA,QAAA,SAAG,OAAIA;AACvB,MAAM,iBAAiB,KAAA,OAAK,aAAa,QAAQ,aAAa,KAAK,GAAC,KAAA;AACpE,MAAM,SAAkB,CAAA;AACxB,MAAI,MAAM;AACV,MAAI,IAAI;AACR,MAAI,OAAO;AAEX,MAAM,aAAa,SAAC,MAAsB;AACxC,QAAI,IAAI,OAAO,UAAU,OAAO,CAAC,EAAE,SAAS;AAAM,aAAO,OAAO,GAAG,EAAE;EACvE;AAEA,MAAM,cAAc,SAAC,MAAsB;AACzC,QAAMC,SAAQ,WAAW,IAAI;AAC7B,QAAIA,WAAU;AAAW,aAAOA;AAC1B,QAAAD,MAA4B,OAAO,CAAC,GAA5B,WAAQA,IAAA,MAAE,QAAKA,IAAA;AAC7B,UAAM,IAAI,UAAU,cAAA,OAAc,UAAQ,MAAA,EAAA,OAAO,OAAK,aAAA,EAAA,OAAc,IAAI,CAAE;EAC5E;AAEA,MAAM,cAAc,WAAA;AAClB,QAAIE,UAAS;AACb,QAAID;AACJ,WAAQA,SAAQ,WAAW,MAAM,KAAK,WAAW,cAAc,GAAI;AACjE,MAAAC,WAAUD;;AAEZ,WAAOC;EACT;AAEA,SAAO,IAAI,OAAO,QAAQ;AACxB,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,UAAU,WAAW,SAAS;AAEpC,QAAI,QAAQ,SAAS;AACnB,UAAI,SAAS,QAAQ;AAErB,UAAI,SAAS,QAAQ,MAAM,MAAM,IAAI;AACnC,gBAAQ;AACR,iBAAS;;AAGX,UAAI,MAAM;AACR,eAAO,KAAK,IAAI;AAChB,eAAO;;AAGT,aAAO,KAAK;QACV,MAAM,QAAQ;QACd;QACA,QAAQ;QACR,SAAS,WAAW;QACpB,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,QAAM,QAAQ,QAAQ,WAAW,cAAc;AAC/C,QAAI,OAAO;AACT,cAAQ;AACR;;AAGF,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAChB,aAAO;;AAGT,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAI,MAAM;AACR,UAAM,SAAS,YAAW;AAC1B,UAAM,SAAO,WAAW,MAAM,KAAK;AACnC,UAAM,YAAU,WAAW,SAAS,KAAK;AACzC,UAAM,SAAS,YAAW;AAE1B,kBAAY,OAAO;AAEnB,aAAO,KAAK;QACV,MAAM,WAAS,YAAU,QAAQ;QACjC,SAAS,UAAQ,CAAC,YAAU,iBAAiB;QAC7C;QACA;QACA,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,gBAAY,KAAK;;AAGnB,SAAO;AACT;AA+IM,SAAU,MACd,KACA,SAAwE;AAExE,MAAM,OAAc,CAAA;AACpB,MAAM,KAAK,aAAa,KAAK,MAAM,OAAO;AAC1C,SAAO,iBAAoB,IAAI,MAAM,OAAO;AAC9C;AAKM,SAAU,iBACd,IACA,MACA,SAAqC;AAArC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAqC;AAE7B,MAAAC,MAA8B,QAAO,QAArC,SAAMA,QAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAACA;AAEjC,SAAO,SAAU,UAAgB;AAC/B,QAAM,IAAI,GAAG,KAAK,QAAQ;AAC1B,QAAI,CAAC;AAAG,aAAO;AAEP,QAAG,OAAgB,EAAC,CAAA,GAAX,QAAU,EAAC;AAC5B,QAAM,SAAS,uBAAO,OAAO,IAAI;2BAExBC,IAAC;AACR,UAAI,EAAEA,EAAC,MAAM;;AAEb,UAAM,MAAM,KAAKA,KAAI,CAAC;AAEtB,UAAI,IAAI,aAAa,OAAO,IAAI,aAAa,KAAK;AAChD,eAAO,IAAI,IAAI,IAAI,EAAEA,EAAC,EAAE,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,IAAI,SAAC,OAAK;AAC/D,iBAAO,OAAO,OAAO,GAAG;QAC1B,CAAC;aACI;AACL,eAAO,IAAI,IAAI,IAAI,OAAO,EAAEA,EAAC,GAAG,GAAG;;;AAVvC,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAG;cAAxB,CAAC;;AAcV,WAAO,EAAE,MAAM,OAAO,OAAM;EAC9B;AACF;AAKA,SAAS,aAAa,KAAW;AAC/B,SAAO,IAAI,QAAQ,6BAA6B,MAAM;AACxD;AAKA,SAAS,MAAM,SAAiC;AAC9C,SAAO,WAAW,QAAQ,YAAY,KAAK;AAC7C;AAqBA,SAAS,eAAe,MAAc,MAAY;AAChD,MAAI,CAAC;AAAM,WAAO;AAElB,MAAM,cAAc;AAEpB,MAAI,QAAQ;AACZ,MAAI,aAAa,YAAY,KAAK,KAAK,MAAM;AAC7C,SAAO,YAAY;AACjB,SAAK,KAAK;;MAER,MAAM,WAAW,CAAC,KAAK;MACvB,QAAQ;MACR,QAAQ;MACR,UAAU;MACV,SAAS;KACV;AACD,iBAAa,YAAY,KAAK,KAAK,MAAM;;AAG3C,SAAO;AACT;AAKA,SAAS,cACP,OACA,MACA,SAA8C;AAE9C,MAAM,QAAQ,MAAM,IAAI,SAAC,MAAI;AAAK,WAAA,aAAa,MAAM,MAAM,OAAO,EAAE;EAAlC,CAAwC;AAC1E,SAAO,IAAI,OAAO,MAAA,OAAM,MAAM,KAAK,GAAG,GAAC,GAAA,GAAK,MAAM,OAAO,CAAC;AAC5D;AAKA,SAAS,eACP,MACA,MACA,SAA8C;AAE9C,SAAO,eAAe,MAAM,MAAM,OAAO,GAAG,MAAM,OAAO;AAC3D;AAoCM,SAAU,eACd,QACA,MACA,SAAmC;AAAnC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAmC;AAGjC,MAAAD,MAME,QAAO,QANT,SAAMA,QAAA,SAAG,QAAKA,KACdE,MAKE,QAAO,OALT,QAAKA,QAAA,SAAG,OAAIA,KACZC,MAIE,QAAO,KAJT,MAAGA,QAAA,SAAG,OAAIA,KACV,KAGE,QAAO,QAHT,SAAM,OAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAAC,IACzB,KAEE,QAAO,WAFT,YAAS,OAAA,SAAG,QAAK,IACjB,KACE,QAAO,UADT,WAAQ,OAAA,SAAG,KAAE;AAEf,MAAM,aAAa,IAAA,OAAI,aAAa,QAAQ,GAAC,KAAA;AAC7C,MAAM,cAAc,IAAA,OAAI,aAAa,SAAS,GAAC,GAAA;AAC/C,MAAI,QAAQ,QAAQ,MAAM;AAG1B,WAAoB,KAAA,GAAA,WAAA,QAAA,KAAA,SAAA,QAAA,MAAQ;AAAvB,QAAM,QAAK,SAAA,EAAA;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,eAAS,aAAa,OAAO,KAAK,CAAC;WAC9B;AACL,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAChD,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAEhD,UAAI,MAAM,SAAS;AACjB,YAAI;AAAM,eAAK,KAAK,KAAK;AAEzB,YAAI,UAAU,QAAQ;AACpB,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,gBAAM,MAAM,MAAM,aAAa,MAAM,MAAM;AAC3C,qBAAS,MAAA,OAAM,QAAM,MAAA,EAAA,OAAO,MAAM,SAAO,MAAA,EAAA,OAAO,MAAM,EAAA,OAAG,QAAM,KAAA,EAAA,OAAM,MAAM,SAAO,MAAA,EAAA,OAAO,QAAM,GAAA,EAAA,OAAI,GAAG;iBACjG;AACL,qBAAS,MAAA,OAAM,QAAM,GAAA,EAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;eAE/D;AACL,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,qBAAS,OAAA,OAAO,MAAM,SAAO,GAAA,EAAA,OAAI,MAAM,UAAQ,GAAA;iBAC1C;AACL,qBAAS,IAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,MAAM,QAAQ;;;aAG3C;AACL,iBAAS,MAAA,OAAM,MAAM,EAAA,OAAG,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;;;AAKtD,MAAI,KAAK;AACP,QAAI,CAAC;AAAQ,eAAS,GAAA,OAAG,aAAW,GAAA;AAEpC,aAAS,CAAC,QAAQ,WAAW,MAAM,MAAA,OAAM,YAAU,GAAA;SAC9C;AACL,QAAM,WAAW,OAAO,OAAO,SAAS,CAAC;AACzC,QAAM,iBACJ,OAAO,aAAa,WAChB,YAAY,QAAQ,SAAS,SAAS,SAAS,CAAC,CAAC,IAAI,KACrD,aAAa;AAEnB,QAAI,CAAC,QAAQ;AACX,eAAS,MAAA,OAAM,aAAW,KAAA,EAAA,OAAM,YAAU,KAAA;;AAG5C,QAAI,CAAC,gBAAgB;AACnB,eAAS,MAAA,OAAM,aAAW,GAAA,EAAA,OAAI,YAAU,GAAA;;;AAI5C,SAAO,IAAI,OAAO,OAAO,MAAM,OAAO,CAAC;AACzC;AAcM,SAAU,aACd,MACA,MACA,SAA8C;AAE9C,MAAI,gBAAgB;AAAQ,WAAO,eAAe,MAAM,IAAI;AAC5D,MAAI,MAAM,QAAQ,IAAI;AAAG,WAAO,cAAc,MAAM,MAAM,OAAO;AACjE,SAAO,eAAe,MAAM,MAAM,OAAO;AAC3C;;;ACzmBO,SAAS,gBAAyB;AACvC,MAAI,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAC3E,WAAO;EACT;AAEA,MAAI,OAAO,YAAY,aAAa;AAElC,UAAM,OAAQ,QAAgB;AAC9B,QAAI,SAAS,cAAc,SAAS,UAAU;AAC5C,aAAO;IACT;AAGA,WAAO,CAAC,EACN,QAAQ,YACR,QAAQ,SAAS;EAErB;AAEA,SAAO;AACT;;;ACvBA,IAAMC,mBAAkB;AAExB,SAASC,qBAAoB,YAAiB,MAAmB;AAC/D,UAAQ,MAAM;IAEZ,KAAK;AACH,aAAO;IAGT,KAAK;IACL,KAAK;AACH,aAAO,OAAO,UAAU;IAG1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;IAGlC,KAAK,KAAK;AAER,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;MACT;AAEA,YAAM,OAAO,KAAK,UAAU,UAAU;AAGtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;MACT;AAEA,aAAO;IACT;EACF;AACF;AAEO,SAASC,QAAOC,aAAoB,aAA4B;AACrE,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAOA;EACT;AAEA,MAAI,kBAAkB;AACtB,MAAI,mBAAmBA,SAAQ;IAC7BH;IACA,CAACI,QAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY,eAAA;AAC/B,YAAM,QAAQH,qBAAoB,YAAY,IAAI;AAElD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;MACT;AAEA,aAAOG;IACT;EACF;AAGA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG,CAAA;EACrE;AAEA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAE1D,SAAO;AACT;AC/DA,IAAMC,0BAAyB;AAO/B,SAASC,iBAAgBC,QAAoB;AAC3C,MAAI,CAACA,OAAM,OAAO;AAChB;EACF;AAEA,QAAM,YAAYA,OAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAGF,uBAAsB;AAC1C,EAAAE,OAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AAEO,IAAMC,kBAAN,cAA6B,MAAM;EAGxC,YAA4BL,aAAoB,aAAoB;AAClE,UAAMA,QAAO;AADa,SAAA,UAAAA;AAF5B,SAAA,OAAO;AAIL,SAAK,UAAUD,QAAOC,UAAS,GAAG,WAAW;AAC7C,IAAAG,iBAAgB,IAAI;EACtB;AACF;AA2BO,IAAMG,aAAuB,CAClC,WACAN,aACG,gBACmB;AACtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAIK,gBAAeL,UAAS,GAAG,WAAW;EAClD;AACF;AAEAM,WAAU,KAAK,CAAC,kBAAkB,WAAWN,aAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAMO,iBAAgB,YAAY,WAAW,IAAIP,WAAUD,QAAOC,UAAS,WAAW;AACtF,QAAII;AAEJ,QAAI;AACF,MAAAA,SAAQ,QAAQ,UAAU,kBAA4C,CAACG,cAAa,CAAC;IACvF,SAAQ,KAAN;AACA,MAAAH,SAAS,iBAAwCG,cAAa;IAChE;AAEA,UAAMH;EACR;AACF;;;AC7EA,IAAII,aAAY,OAAO;AACvB,IAAIC,YAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ;AACf,IAAAD,WAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAChE;AAOA,IAAI,iBAAiB,CAAC;AACtBC,UAAS,gBAAgB;AAAA,EACvB,MAAM,MAAM;AAAA,EACZ,MAAM,MAAM;AAAA,EACZ,OAAO,MAAM;AAAA,EACb,KAAK,MAAM;AAAA,EACX,QAAQ,MAAM;AAChB,CAAC;AACD,SAAS,OAAO,MAAM;AACpB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,KAAK,MAAM;AAClB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,KAAK,MAAM;AAClB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,IAAI,MAAM;AACjB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,MAAM,MAAM;AACnB,SAAO,WAAW,IAAI;AACxB;AAGA,IAAI,UAAU,cAAc;AAC5B,IAAI,SAAS,MAAM;AAAA,EACjB,YAAY,MAAM;AAoBlB;AAnBE,SAAK,OAAO;AACZ,SAAK,SAAS,IAAI,KAAK,IAAI;AAC3B,UAAM,cAAc,YAAY,OAAO;AACvC,UAAM,eAAe,YAAY,WAAW;AAC5C,UAAM,mBAAmB,gBAAgB,OAAO,gBAAgB,UAAU,OAAO,gBAAgB,eAAe,KAAK,KAAK,WAAW,WAAW;AAChJ,QAAI,kBAAkB;AACpB,WAAK,QAAQ,sBAAsB,cAAc,OAAO,IAAI,OAAO,KAAK;AACxE,WAAK,OAAO,sBAAsB,cAAc,MAAM,IAAI,OAAO,KAAK;AACtE,WAAK,UAAU,sBAAsB,cAAc,SAAS,IAAI,OAAO,KAAK;AAC5E,WAAK,UAAU,sBAAsB,cAAc,SAAS,IAAI,OAAO,KAAK;AAC5E,WAAK,QAAQ,sBAAsB,cAAc,OAAO,IAAI,OAAO,KAAK;AAAA,IAC1E,OAAO;AACL,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EAEA,OAAO,QAAQ;AACb,WAAO,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,MAAM,EAAE;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAMC,aAAY,aAAa;AAC7B,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,SAAS,KAAKA,QAAO;AAAA,MACrB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAKA,aAAY,aAAa;AAC5B,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,SAAAA;AAAA,MACA;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AACD,UAAM,eAAe,IAAI,iBAAiB;AAC1C,WAAO,CAACC,cAAa,iBAAiB;AACpC,mBAAa,QAAQ;AACrB,WAAK,SAAS;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,GAAGA,SAAQ,IAAI,KAAK,GAAG,aAAa,SAAS,IAAI,CAAC;AAAA,QAC3D,aAAa;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQD,aAAY,aAAa;AAC/B,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,SAAAA;AAAA,MACA;AAAA,MACA,QAAQ,UAAU,KAAK,MAAM;AAAA,MAC7B,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQA,aAAY,aAAa;AAC/B,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,SAAAA;AAAA,MACA;AAAA,MACA,QAAQ,UAAU,KAAK,MAAM;AAAA,MAC7B,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAMA,aAAY,aAAa;AAC7B,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,SAAAA;AAAA,MACA;AAAA,MACA,QAAQ,UAAU,KAAK,MAAM;AAAA,MAC7B,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,UAAU;AACb,aAAS;AAAA,EACX;AAAA,EACA,YAAY,OAAOA,UAAS;AAC1B,WAAO;AAAA,MACL,WAA2B,oBAAI,KAAK;AAAA,MACpC;AAAA,MACA,SAAAA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,MAAM;AACb,UAAM;AAAA,MACJ;AAAA,MACA,SAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,cAAc,CAAC;AAAA,IACjB,IAAI;AACJ,UAAM,QAAQ,KAAK,YAAY,OAAOA,QAAO;AAC7C,UAAM,kBAAiB,6CAAc,cAAa;AAClD,UAAM,eAAc,6CAAc,WAAU;AAC5C,UAAM,WAAW;AAAA,MACf,WAAW,eAAe,cAAc;AAAA,MACxC,QAAQ,eAAe,WAAW;AAAA,IACpC;AACA,UAAM,QAAQ,KAAK,UAAU,KAAK;AAClC;AAAA,MACE,CAAC,SAAS,UAAU,KAAK,gBAAgB,MAAM,SAAS,CAAC,CAAC,EAAE,OAAO,UAAU,OAAO,SAAS,OAAO,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,eAAeA,QAAO,CAAC,EAAE,KAAK,GAAG;AAAA,MAC1J,GAAG,YAAY,IAAI,cAAc;AAAA,IACnC;AAAA,EACF;AAAA,EACA,gBAAgB,WAAW;AACzB,WAAO,GAAG,UAAU;AAAA,MAClB;AAAA,IACF,CAAC,IAAI,UAAU,gBAAgB,CAAC;AAAA,EAClC;AAAA,EACA,UAAU,OAAO;AACf,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,WAAW;AACd,eAAOE;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,eAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,MAAM;AAAA,EAI3B,cAAc;AAHd;AACA;AACA;AAEE,SAAK,YAAY,YAAY,IAAI;AAAA,EACnC;AAAA,EACA,UAAU;AACR,SAAK,UAAU,YAAY,IAAI;AAC/B,UAAM,YAAY,KAAK,UAAU,KAAK;AACtC,SAAK,YAAY,UAAU,QAAQ,CAAC;AAAA,EACtC;AACF;AACA,IAAI,OAAO,MAAM;AACjB,SAAS,IAAIH,aAAY,aAAa;AACpC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAMI,QAAOJ,UAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;AAAA,EACF;AACA,UAAQ,IAAIA,UAAS,GAAG,WAAW;AACrC;AACA,SAASE,MAAKF,aAAY,aAAa;AACrC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAMI,QAAOJ,UAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;AAAA,EACF;AACA,UAAQ,KAAKA,UAAS,GAAG,WAAW;AACtC;AACA,SAASG,OAAMH,aAAY,aAAa;AACtC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAMI,QAAOJ,UAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;AAAA,EACF;AACA,UAAQ,MAAMA,UAAS,GAAG,WAAW;AACvC;AACA,SAAS,YAAY,cAAc;AA9PnC,MAAAK;AA+PE,MAAI,SAAS;AACX,WAAO,QAAQ,IAAI,YAAY;AAAA,EACjC;AACA,UAAOA,MAAA,WAAW,YAAY,MAAvB,gBAAAA,IAA0B;AACnC;AACA,SAAS,sBAAsB,OAAO,UAAU;AAC9C,SAAO,UAAU,UAAU,UAAU;AACvC;AACA,SAAS,eAAeL,UAAS;AAC/B,MAAI,OAAOA,aAAY,aAAa;AAClC,WAAO;AAAA,EACT;AACA,MAAIA,aAAY,MAAM;AACpB,WAAO;AAAA,EACT;AACA,MAAI,OAAOA,aAAY,UAAU;AAC/B,WAAOA;AAAA,EACT;AACA,MAAI,OAAOA,aAAY,UAAU;AAC/B,WAAO,KAAK,UAAUA,QAAO;AAAA,EAC/B;AACA,SAAOA,SAAQ,SAAS;AAC1B;;;ACxQO,IAAM,kCACX;AAEK,SAAS,gBAAmB,QAA+B;AAChE;;IAEE,WAAW,MAAM,KAAK;;AAE1B;AAEA,SAAS,gBAAgB,QAAgB,OAAkB;AAEzD,aAAW,MAAM,IAAI;AACvB;AAEO,SAAS,mBAAmB,QAAsB;AAEvD,SAAO,WAAW,MAAM;AAC1B;AAEO,IAAK,wBAAL,kBAAKM,2BAAL;AACLA,yBAAA,UAAA,IAAW;AACXA,yBAAA,UAAA,IAAW;AACXA,yBAAA,SAAA,IAAU;AACVA,yBAAA,WAAA,IAAY;AACZA,yBAAA,UAAA,IAAW;AALD,SAAAA;AAAA,GAAA,yBAAA,CAAA,CAAA;AAWL,IAAM,cAAN,MAAsD;EAO3D,YAA6B,QAAgB;AAAhB,SAAA,SAAA;AAC3B,SAAK,aAAa;AAElB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,IAAI,OAAO,OAAO,WAAY;AAI5C,SAAK,QAAQ,gBAAgB,CAAC;AAE9B,SAAK,OAAO,KAAK,iCAAiC;EACpD;;;;;EAMU,mBAA4B;AACpC,WAAO;EACT;;;;;EAMO,QAAc;AACnB,UAAMC,UAAS,KAAK,OAAO,OAAO,OAAO;AACzC,IAAAA,QAAO,KAAK,6BAA6B;AAEzC,QAAI,KAAK,eAAe,WAA+B;AACrD,MAAAA,QAAO,KAAK,8BAA8B;AAC1C;IACF;AAEA,UAAM,cAAc,KAAK,iBAAiB;AAE1C,QAAI,CAAC,aAAa;AAChB,MAAAA,QAAO,KAAK,wDAAwD;AACpE;IACF;AAEA,SAAK,aAAa;AAKlB,UAAM,kBAAkB,KAAK,YAAY;AAEzC,QAAI,iBAAiB;AACnB,MAAAA,QAAO,KAAK,sCAAsC;AAGlD,WAAK,KAAK,CAAC,OAAO,aAAa;AAC7B,QAAAA,QAAO,KAAK,8BAA8B,KAAK;AAI/C,wBAAgB,QAAQ,YAAY,OAAO,QAAQ;AAInD,aAAK,cAAc,KAAK,MAAM;AAC5B,0BAAgB,QAAQ,eAAe,OAAO,QAAQ;AACtD,UAAAA,QAAO,KAAK,kCAAkC,KAAK;QACrD,CAAC;AAED,eAAO;MACT;AAEA,WAAK,aAAa;AAElB;IACF;AAEA,IAAAA,QAAO,KAAK,yDAAyD;AAGrE,SAAK,MAAM;AAGX,SAAK,YAAY;AAEjB,SAAK,aAAa;EACpB;;;;;;EAOU,QAAc;EAAC;;;;EAKlB,GACL,OACA,UACM;AACN,UAAMA,UAAS,KAAK,OAAO,OAAO,IAAI;AAEtC,QACE,KAAK,eAAe,eACpB,KAAK,eAAe,YACpB;AACA,MAAAA,QAAO,KAAK,4CAA4C;AACxD,aAAO;IACT;AAEA,IAAAA,QAAO,KAAK,+BAA+B,OAAO,QAAQ;AAE1D,SAAK,QAAQ,GAAG,OAAO,QAAQ;AAC/B,WAAO;EACT;EAEO,KACL,OACA,UACM;AACN,SAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,WAAO;EACT;EAEO,IACL,OACA,UACM;AACN,SAAK,QAAQ,IAAI,OAAO,QAAQ;AAChC,WAAO;EACT;EAEO,mBACL,OACM;AACN,SAAK,QAAQ,mBAAmB,KAAK;AACrC,WAAO;EACT;;;;EAKO,UAAgB;AACrB,UAAMA,UAAS,KAAK,OAAO,OAAO,SAAS;AAE3C,QAAI,KAAK,eAAe,YAAgC;AACtD,MAAAA,QAAO,KAAK,mCAAmC;AAC/C;IACF;AAEA,IAAAA,QAAO,KAAK,8BAA8B;AAC1C,SAAK,aAAa;AAElB,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB,MAAAA,QAAO,KAAK,8CAA8C;AAC1D;IACF;AAIA,SAAK,cAAc;AAEnB,IAAAA,QAAO,KAAK,0BAA0B,gBAAgB,KAAK,MAAM,CAAC;AAElE,QAAI,KAAK,cAAc,SAAS,GAAG;AACjC,MAAAA,QAAO,KAAK,oCAAoC,KAAK,cAAc,MAAM;AAEzE,iBAAW,WAAW,KAAK,eAAe;AACxC,gBAAQ;MACV;AAEA,WAAK,gBAAgB,CAAC;AAEtB,MAAAA,QAAO,KAAK,kCAAkC,KAAK,cAAc,MAAM;IACzE;AAEA,SAAK,QAAQ,mBAAmB;AAChC,IAAAA,QAAO,KAAK,yBAAyB;AAErC,SAAK,aAAa;EACpB;EAEQ,cAAgC;AAzO1C,QAAAC;AA0OI,UAAM,WAAW,gBAAsB,KAAK,MAAM;AAClD,SAAK,OAAO,KAAK,+BAA8BA,MAAA,YAAA,OAAA,SAAA,SAAU,gBAAV,OAAA,SAAAA,IAAuB,IAAI;AAC1E,WAAO;EACT;EAEQ,cAAoB;AAC1B,oBAAgB,KAAK,QAAQ,IAAI;AACjC,SAAK,OAAO,KAAK,wBAAwB,KAAK,OAAO,WAAW;EAClE;EAEQ,gBAAsB;AAC5B,uBAAmB,KAAK,MAAM;AAC9B,SAAK,OAAO,KAAK,4BAA4B,KAAK,OAAO,WAAW;EACtE;AACF;AClPO,SAAS,kBAA0B;AACxC,SAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAC3C;ACAO,SAAS,qBACd,KACA,KACA;AACA,MAAI;AACF,QAAI,GAAG;AACP,WAAO;EACT,SAAQ,GAAN;AACA,WAAO;EACT;AACF;ACZO,IAAM,qCAAqC,oBAAI,IAAI;EACxD;EAAK;EAAK;EAAK;EAAK;AACtB,CAAC;AAMM,SAAS,sBAAsB,QAAyB;AAC7D,SAAO,mCAAmC,IAAI,MAAM;AACtD;AAKO,SAAS,0BAA0B,MAAyB;AACjE,SAAO,IAAI;IACT,KAAK;MACH,gBAAgB,QACZ;QACE,MAAM,KAAK;QACX,SAAS,KAAK;QACd,OAAO,KAAK;MACd,IACA;IACN;IACA;MACE,QAAQ;MACR,YAAY;MACZ,SAAS;QACP,gBAAgB;MAClB;IACF;EACF;AACF;AAUO,SAAS,gBACd,UAC0C;AAC1C,SAAO,qBAAqB,UAAU,MAAM,KAAK,SAAS,SAAS;AACrE;;;AChCO,IAAM,mBAAN,cAGG,YAAoB;EAK5B,YAAY,SAAmD;AAC7D,qBAAiB,SAAS,OAAO,QAAQ,IAAI;AAC7C,UAAM,iBAAiB,MAAM;AAC7B,SAAK,eAAe,QAAQ;EAC9B;EAEU,QAAQ;AAChB,UAAMC,UAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,IAAAA,QAAO,KAAK,mCAAmC,KAAK,aAAa,MAAM;AAEvE,eAAW,eAAe,KAAK,cAAc;AAC3C,MAAAA,QAAO,KAAK,gCAAgC,YAAY,YAAY,IAAI;AACxE,kBAAY,MAAM;AAElB,MAAAA,QAAO,KAAK,yCAAyC;AACrD,WAAK,cAAc,KAAK,MAAM,YAAY,QAAQ,CAAC;IACrD;EACF;EAEO,GACL,OACA,UACM;AAGN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,GAAG,OAAO,QAAQ;IAChC;AAEA,WAAO;EACT;EAEO,KACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,KAAK,OAAO,QAAQ;IAClC;AAEA,WAAO;EACT;EAEO,IACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,IAAI,OAAO,QAAQ;IACjC;AAEA,WAAO;EACT;EAEO,mBACL,OACM;AACN,eAAW,gBAAgB,KAAK,cAAc;AAC5C,mBAAa,mBAAmB,KAAK;IACvC;AAEA,WAAO;EACT;AACF;;;AC9FA,IAAM,UAAU,IAAI,YAAY;AAEzB,SAAS,aAAa,MAA0B;AACrD,SAAO,QAAQ,OAAO,IAAI;AAC5B;AAEO,SAAS,aAAa,QAAqB,UAA2B;AAC3E,QAAM,UAAU,IAAI,YAAY,QAAQ;AACxC,SAAO,QAAQ,OAAO,MAAM;AAC9B;AAOO,SAAS,cAAc,OAAgC;AAC5D,SAAO,MAAM,OAAO;IAClB,MAAM;IACN,MAAM,aAAa,MAAM;EAC3B;AACF;;;ACnBO,IAAM,oBAAmC,OAAO,iBAAiB;;;ACCjE,SAAS,YAAY,KAAU,aAAsB,MAAc;AACxE,SAAO,CAAC,cAAc,IAAI,QAAQ,IAAI,QAAQ,EAAE,OAAO,OAAO,EAAE,KAAK,EAAE;AACzE;;;ACLA,IAAM,2BAA2B;AAE1B,SAAS,gBAAgB,MAAc;AAC5C,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,kBAAkB,EAAE;AACjD;AAMO,SAAS,SAAS,MAAsB;AAG7C,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,WAAO;EACT;AAGA,SAAO,KAAK,QAAQ,0BAA0B,EAAE;AAClD;;;AChBO,SAAS,cAAc,KAAsB;AAClD,SAAO,gCAAgC,KAAK,GAAG;AACjD;;;ACAO,SAAS,eAAe,MAAc,SAA0B;AAErE,MAAI,cAAc,IAAI,GAAG;AACvB,WAAO;EACT;AAGA,MAAI,KAAK,WAAW,GAAG,GAAG;AACxB,WAAO;EACT;AAIA,QAAM,SACJ,WAAY,OAAO,aAAa,eAAe,SAAS;AAE1D,SAAO;;IAEH,UAAU,IAAI,IAAI,UAAU,IAAI,GAAG,MAAM,EAAE,IAAI;MAC/C;AACN;;;ACbO,SAAS,cAAc,MAAY,SAAwB;AAEhE,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;EACT;AAEA,QAAM,mBAAmB,eAAe,MAAM,OAAO;AAErD,SAAO,SAAS,gBAAgB;AAClC;;;ACHO,SAAS,WAAW,MAAsB;AAC/C,SACE,KAMG;IACC;IACA,CAAC,GAAG,eAAmC,aAAqB;AAC1D,YAAM,aAAa;AAEnB,UAAI,CAAC,eAAe;AAClB,eAAO;MACT;AAEA,aAAO,cAAc,WAAW,GAAG,IAC/B,GAAG,aAAa,GAAG,QAAQ,KAC3B,GAAG,aAAa,GAAG,UAAU;IACnC;EACF,EAKC,QAAQ,qBAAqB,QAAQ,EAMrC,QAAQ,wBAAwB,QAAQ;AAE/C;AAKO,SAAS,gBAAgB,KAAU,MAAY,SAAyB;AAC7E,QAAM,iBAAiB,cAAc,MAAM,OAAO;AAClD,QAAM,YACJ,OAAO,mBAAmB,WACtB,WAAW,cAAc,IACzB;AAEN,QAAMC,YAAW,YAAY,GAAG;AAChC,QAAM,SAAS,MAAM,WAAW,EAAE,QAAQ,mBAAmB,CAAC,EAAEA,SAAQ;AACxE,QAAM,SAAU,UAAW,OAAO,UAA0B,CAAC;AAE7D,SAAO;IACL,SAAS,WAAW;IACpB;EACF;AACF;;;ACpEO,SAAS,YAAY,KAA2B;AACrD,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO,IAAI,SAAS;EACtB;AAEA,QAAM,cAAc,eAAe,MAAM,MAAM,IAAI,IAAI,GAAG;AAE1D,SAAO,YAAY,WAAW,SAAS,SACnC,YAAY,WACZ,YAAY,SAAS,YAAY;AACvC;;;ACdA,IAAIC,YAAW,OAAO;AACtB,IAAIC,aAAY,OAAO;AACvB,IAAIC,oBAAmB,OAAO;AAC9B,IAAIC,qBAAoB,OAAO;AAC/B,IAAIC,gBAAe,OAAO;AAC1B,IAAIC,gBAAe,OAAO,UAAU;AACpC,IAAIC,cAAa,CAAC,IAAI,QAAQ,SAAS,YAAY;AACjD,SAAO,QAAQ,GAAG,GAAGH,mBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,GAAG,IAAI;AAC7F;AACA,IAAII,eAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAOJ,mBAAkB,IAAI;AACpC,UAAI,CAACE,cAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,QAAAJ,WAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAOC,kBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAIM,WAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAOR,UAASI,cAAa,GAAG,CAAC,IAAI,CAAC,GAAGG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAaN,WAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,EACzG;AACF;AAGA,IAAI,iBAAiBK,YAAW;AAAA,EAC9B,+BAA+BG,UAAS;AACtC;AACA,IAAAA,SAAQ,QAAQC;AAChB,IAAAD,SAAQ,YAAY;AACpB,QAAI,aAAa,OAAO,UAAU;AAClC,QAAI,qBAAqB;AACzB,aAASC,OAAM,KAAK,SAAS;AAC3B,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AACA,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,MAAM,IAAI,UAAU;AACxB,UAAI,QAAQ;AACZ,aAAO,QAAQ,IAAI,QAAQ;AACzB,YAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK;AAClC,YAAI,UAAU,IAAI;AAChB;AAAA,QACF;AACA,YAAI,SAAS,IAAI,QAAQ,KAAK,KAAK;AACnC,YAAI,WAAW,IAAI;AACjB,mBAAS,IAAI;AAAA,QACf,WAAW,SAAS,OAAO;AACzB,kBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,QACF;AACA,YAAI,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK;AACvC,YAAI,WAAW,IAAI,GAAG,GAAG;AACvB,cAAI,MAAM,IAAI,MAAM,QAAQ,GAAG,MAAM,EAAE,KAAK;AAC5C,cAAI,IAAI,WAAW,CAAC,MAAM,IAAI;AAC5B,kBAAM,IAAI,MAAM,GAAG,EAAE;AAAA,UACvB;AACA,cAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,QAC/B;AACA,gBAAQ,SAAS;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AACA,aAAS,UAAU,MAAM,KAAK,SAAS;AACrC,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,MAAM,IAAI,UAAU;AACxB,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AACA,UAAI,CAAC,mBAAmB,KAAK,IAAI,GAAG;AAClC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AACA,UAAI,QAAQ,IAAI,GAAG;AACnB,UAAI,SAAS,CAAC,mBAAmB,KAAK,KAAK,GAAG;AAC5C,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AACA,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,QAAQ,IAAI,QAAQ;AACtB,YAAI,SAAS,IAAI,SAAS;AAC1B,YAAI,MAAM,MAAM,KAAK,CAAC,SAAS,MAAM,GAAG;AACtC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AACA,eAAO,eAAe,KAAK,MAAM,MAAM;AAAA,MACzC;AACA,UAAI,IAAI,QAAQ;AACd,YAAI,CAAC,mBAAmB,KAAK,IAAI,MAAM,GAAG;AACxC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AACA,eAAO,cAAc,IAAI;AAAA,MAC3B;AACA,UAAI,IAAI,MAAM;AACZ,YAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,GAAG;AACtC,gBAAM,IAAI,UAAU,wBAAwB;AAAA,QAC9C;AACA,eAAO,YAAY,IAAI;AAAA,MACzB;AACA,UAAI,IAAI,SAAS;AACf,YAAI,UAAU,IAAI;AAClB,YAAI,CAAC,OAAO,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAChD,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AACA,eAAO,eAAe,QAAQ,YAAY;AAAA,MAC5C;AACA,UAAI,IAAI,UAAU;AAChB,eAAO;AAAA,MACT;AACA,UAAI,IAAI,QAAQ;AACd,eAAO;AAAA,MACT;AACA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACnF,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AACA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACnF,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,OAAO,KAAK;AACnB,aAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,mBAAmB,GAAG,IAAI;AAAA,IAC7D;AACA,aAAS,OAAO,KAAK;AACnB,aAAO,mBAAmB,GAAG;AAAA,IAC/B;AACA,aAAS,OAAO,KAAK;AACnB,aAAO,WAAW,KAAK,GAAG,MAAM,mBAAmB,eAAe;AAAA,IACpE;AACA,aAAS,UAAU,KAAK,SAAS;AAC/B,UAAI;AACF,eAAO,QAAQ,GAAG;AAAA,MACpB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,IAAI,gBAAgBF,SAAQ,eAAe,GAAG,CAAC;AAC/C,IAAIG,kBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AC1KnC,IAAA,qBAAAC,YAAA;EAAA,mDAAAC,UAAAC,SAAA;AAAA;AAEA,QAAI,sBAAsB;MACxB,cAAc;MACd,KAAK;MACL,QAAQ;IACV;AAEA,aAAS,iBAAiB,KAAK;AAC7B,aAAO,OAAO,QAAQ,YAAY,CAAC,CAAC,IAAI,KAAK;IAC/C;AAEA,aAAS,YAAY,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,eAAe,MAAM,GAAG,EAAE,OAAO,gBAAgB;AAE7D,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,SAAS,mBAAmB,gBAAgB;AAChD,UAAI,OAAO,OAAO;AAClB,UAAI,QAAQ,OAAO;AAEnB,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI;AACF,gBAAQ,QAAQ,eAAe,mBAAmB,KAAK,IAAI;MAC7D,SAAS,GAAP;AACA,gBAAQ;UACN,gFACE,QACA;UACF;QACF;MACF;AAEA,UAAI,SAAS;QACX;QACA;MACF;AAEA,YAAM,QAAQ,SAAU,MAAM;AAC5B,YAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,YAAI,MAAM,MAAM,MAAM,EAAE,SAAS,EAAE,YAAY;AAC/C,YAAIC,SAAQ,MAAM,KAAK,GAAG;AAC1B,YAAI,QAAQ,WAAW;AACrB,iBAAO,UAAU,IAAI,KAAKA,MAAK;QACjC,WAAW,QAAQ,WAAW;AAC5B,iBAAO,SAAS,SAASA,QAAO,EAAE;QACpC,WAAW,QAAQ,UAAU;AAC3B,iBAAO,SAAS;QAClB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAW;QACpB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAWA;QACpB,OAAO;AACL,iBAAO,GAAA,IAAOA;QAChB;MACF,CAAC;AAED,aAAO;IACT;AAEA,aAAS,mBAAmB,kBAAkB;AAG5C,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,eAAe,iBAAiB,MAAM,GAAG;AAC7C,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,MAAM;AAC1B,gBAAQ,aAAa,KAAK,GAAG;MAC/B,OAAO;AACL,gBAAQ;MACV;AAEA,aAAO,EAAE,MAAY,MAAa;IACpC;AAEA,aAASC,OAAM,OAAO,SAAS;AAC7B,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,QAAQ,KAAK;AAChB,iBAAO,CAAC;QACV,OAAO;AACL,iBAAO,CAAC;QACV;MACF;AAEA,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,MAAM,QAAQ,iBAAiB,YAAY;AAGpD,kBAAQ,MAAM,QAAQ,aAAa;QACrC,WAAW,MAAM,QAAQ,YAAA,GAAe;AAEtC,kBAAQ,MAAM,QAAQ,YAAA;QACxB,OAAO;AAEL,cAAI,MACF,MAAM,QACJ,OAAO,KAAK,MAAM,OAAO,EAAE,KAAK,SAAU,KAAK;AAC7C,mBAAO,IAAI,YAAY,MAAM;UAC/B,CAAC,CAAA;AAGL,cAAI,CAAC,OAAO,MAAM,QAAQ,UAAU,CAAC,QAAQ,QAAQ;AACnD,oBAAQ;cACN;YACF;UACF;AACA,kBAAQ;QACV;MACF;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAC,KAAK;MAChB;AAEA,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,QAAQ,KAAK;AAChB,eAAO,MAAM,OAAO,gBAAgB,EAAE,IAAI,SAAU,KAAK;AACvD,iBAAO,YAAY,KAAK,OAAO;QACjC,CAAC;MACH,OAAO;AACL,YAAI,UAAU,CAAC;AACf,eAAO,MAAM,OAAO,gBAAgB,EAAE,OAAO,SAAUC,UAAS,KAAK;AACnE,cAAI,SAAS,YAAY,KAAK,OAAO;AACrCA,mBAAQ,OAAO,IAAA,IAAQ;AACvB,iBAAOA;QACT,GAAG,OAAO;MACZ;IACF;AAaA,aAAS,mBAAmB,eAAe;AACzC,UAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAO;MACT;AACA,UAAI,OAAO,kBAAkB,UAAU;AACrC,eAAO,CAAC;MACV;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,iBAAiB;AACxB,eAAO,MAAM,cAAc,UAAU,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AACzE,iBAAO;QACT;AACA,eAAO,MAAM,cAAc;MAC7B;AAEA,eAAS,iBAAiB;AACxB,aAAK,cAAc,OAAO,GAAG;AAE7B,eAAO,OAAO,OAAO,OAAO,OAAO,OAAO;MAC5C;AAEA,aAAO,MAAM,cAAc,QAAQ;AACjC,gBAAQ;AACR,gCAAwB;AAExB,eAAO,eAAe,GAAG;AACvB,eAAK,cAAc,OAAO,GAAG;AAC7B,cAAI,OAAO,KAAK;AAEd,wBAAY;AACZ,mBAAO;AAEP,2BAAe;AACf,wBAAY;AAEZ,mBAAO,MAAM,cAAc,UAAU,eAAe,GAAG;AACrD,qBAAO;YACT;AAGA,gBAAI,MAAM,cAAc,UAAU,cAAc,OAAO,GAAG,MAAM,KAAK;AAEnE,sCAAwB;AAExB,oBAAM;AACN,6BAAe,KAAK,cAAc,UAAU,OAAO,SAAS,CAAC;AAC7D,sBAAQ;YACV,OAAO;AAGL,oBAAM,YAAY;YACpB;UACF,OAAO;AACL,mBAAO;UACT;QACF;AAEA,YAAI,CAAC,yBAAyB,OAAO,cAAc,QAAQ;AACzD,yBAAe,KAAK,cAAc,UAAU,OAAO,cAAc,MAAM,CAAC;QAC1E;MACF;AAEA,aAAO;IACT;AAEA,IAAAH,QAAO,UAAUE;AACjB,IAAAF,QAAO,QAAQ,QAAQE;AACvB,IAAAF,QAAO,QAAQ,cAAc;AAC7B,IAAAA,QAAO,QAAQ,qBAAqB;EAAA;AAAA,CAAA;ACjOpC,IAAA,2BAA6CI,SAAA,mBAAA,CAAA;AAmBtC,IAAM,kBAAkB;AAE/B,SAAS,uBAAuB;AAC9B,MAAI;AACF,QAAI,gBAAgB,MAAM;AACxB,aAAO;IACT;AAEA,UAAM,UAAU,kBAAkB;AAElC,iBAAa,QAAQ,SAAS,MAAM;AACpC,iBAAa,QAAQ,OAAO;AAC5B,iBAAa,WAAW,OAAO;AAE/B,WAAO;EACT,SAASC,QAAP;AACA,WAAO;EACT;AACF;AAEA,IAAM,yBAAyB,qBAAqB;AAUpD,SAASC,sBACP,QACA,QACA;AACA,MAAI;AACF,WAAO,MAAA;AACP,WAAO;EACT,SAAE;AACA,WAAO;EACT;AACF;AAEA,IAAM,cAAN,MAAkB;EAGhB,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAI;EACvB;EAMA,IAAIC,UAAsB,UAA8B;AACtD,QACED,sBAAqBC,UAAS,aAAa,KAC3CA,SAAQ,gBAAgB,QACxB;AACA;IACF;AAEA,UAAM,aAAa,IAAI,IAAIA,SAAQ,GAAG;AACtC,UAAM,kBAAkB,SAAS,QAAQ,IAAI,YAAY;AAEzD,QAAI,CAAC,iBAAiB;AACpB;IACF;AAEA,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,yBAAA,GAAwB,yBAAAC,OAAY,eAAe,EAAE;MACzD,CAAC,EAAE,QAAA,GAAW,OAAO,OAAO;QAC1B,GAAG;QACH,SACE,WAAW,SAAY,OAAO,UAAU,IAAI,KAAK,MAAM,SAAS,GAAI;QACtE;MACF;IACF;AAEA,UAAM,cACJ,KAAK,MAAM,IAAI,WAAW,MAAM,KAAK,oBAAI,IAAoB;AAE/D,0BAAsB,QAAQ,CAAC,WAAW;AACxC,WAAK,MAAM,IAAI,WAAW,QAAQ,YAAY,IAAI,OAAO,MAAM,MAAM,CAAC;IACxE,CAAC;EACH;EAMA,IAAID,UAAkC;AACpC,SAAK,qBAAqB;AAE1B,UAAM,aAAa,IAAI,IAAIA,SAAQ,GAAG;AACtC,UAAM,gBACJ,KAAK,MAAM,IAAI,WAAW,MAAM,KAAK,oBAAI,IAAoB;AAE/D,QAAI,CAACD,sBAAqBC,UAAS,aAAa,GAAG;AACjD,aAAO;IACT;AAEA,YAAQA,SAAQ,aAAA;MAAA,KACT,WAAW;AAEd,YAAI,OAAO,aAAa,aAAa;AACnC,iBAAO;QACT;AAEA,cAAM,mBAAA,GAAkB,yBAAAC,OAAY,SAAS,MAAM;AAEnD,wBAAgB,QAAQ,CAAC,WAAW;AAClC,wBAAc,IAAI,OAAO,MAAM,MAAM;QACvC,CAAC;AAED,eAAO;MACT;MAAA,KAEK,eAAe;AAClB,eAAO;MACT;MAAA;AAGE,eAAO,oBAAI,IAAI;IAAA;EAErB;EAKA,SAAgB;AACd,SAAK,qBAAqB;AAC1B,WAAO,KAAK;EACd;EAKA,UAAUD,UAA4B;AACpC,UAAM,aAAa,IAAI,IAAIA,SAAQ,GAAG;AACtC,SAAK,MAAM,OAAO,WAAW,MAAM;EACrC;EAKA,QAAc;AACZ,SAAK,MAAM,MAAM;EACnB;EAKA,UAAgB;AACd,QAAI,CAAC,wBAAwB;AAC3B;IACF;AAEA,UAAM,mBAAmB,aAAa,QAAQ,eAAe;AAE7D,QAAI,CAAC,kBAAkB;AACrB;IACF;AAEA,QAAI;AACF,YAAM,gBACJ,KAAK,MAAM,gBAAgB;AAE7B,oBAAc,QAAQ,CAAC,CAAC,QAAQ,OAAO,MAAM;AAC3C,aAAK,MAAM;UACT;UACA,IAAI;YACF,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,SAAA,GAAY,OAAO,CAAC,MAAM;cAC/C;cACA,YAAY,SACR,SACA,EAAE,GAAG,QAAQ,SAAS,IAAI,KAAK,OAAO,EAAE;YAC9C,CAAC;UACH;QACF;MACF,CAAC;IACH,SAASF,QAAP;AACA,cAAQ,KAAK;+EAC4D,eAAA;;;EAG7E,aAAa,QAAQ,eAAe,CAAA;;;EAGpCA,MAAA;;gGAE8F;AAC1F,mBAAa,WAAW,eAAe;IACzC;EACF;EAMA,UAAgB;AACd,QAAI,CAAC,wBAAwB;AAC3B;IACF;AAEA,UAAM,oBAAoB,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,EAAE;MACzD,CAAC,CAAC,QAAQ,OAAO,MAAM;AACrB,eAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,QAAQ,CAAC,CAAC;MAC/C;IACF;AAEA,iBAAa,QAAQ,iBAAiB,KAAK,UAAU,iBAAiB,CAAC;EACzE;EAEQ,uBAAuB;AAC7B,UAAM,MAAM,KAAK,IAAI;AAErB,SAAK,MAAM,QAAQ,CAAC,eAAe,WAAW;AAC5C,oBAAc,QAAQ,CAAC,EAAE,SAAS,KAAK,MAAM;AAC3C,YAAI,YAAY,UAAa,QAAQ,QAAQ,KAAK,KAAK;AACrD,wBAAc,OAAO,IAAI;QAC3B;MACF,CAAC;AAED,UAAI,cAAc,SAAS,GAAG;AAC5B,aAAK,MAAM,OAAO,MAAM;MAC1B;IACF,CAAC;EACH;AACF;AAEO,IAAM,QAAQ,IAAI,YAAY;;;ACtPrC,SAAS,wBAAwB;AAC/B,SAAOI,gBAAY,MAAM,SAAS,MAAM;AAC1C;AAMO,SAAS,kBAAkBC,UAA0C;AAI1E,MAAI,OAAO,aAAa,eAAe,OAAO,aAAa,aAAa;AACtE,WAAO,CAAC;EACV;AAEA,UAAQA,SAAQ,aAAa;IAC3B,KAAK,eAAe;AAClB,YAAM,MAAM,IAAI,IAAIA,SAAQ,GAAG;AAI/B,aAAO,SAAS,WAAW,IAAI,SAAS,sBAAsB,IAAI,CAAC;IACrE;IAEA,KAAK,WAAW;AAEd,aAAO,sBAAsB;IAC/B;IAEA,SAAS;AACP,aAAO,CAAC;IACV;EACF;AACF;AAEO,SAAS,qBAAqBA,UAA0C;AAvC/E,MAAAC;AAwCE,QAAM,uBAAuBD,SAAQ,QAAQ,IAAI,QAAQ;AACzD,QAAM,qBAAqB,uBACvBD,gBAAY,MAAM,oBAAoB,IACtC,CAAC;AAEL,QAAM,QAAQ;AAEd,QAAM,mBAAmB,MAAM,MAAKE,MAAA,MAAM,IAAID,QAAO,MAAjB,gBAAAC,IAAoB,SAAS,EAAE,OAEjE,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM;AAChC,WAAO,OAAO,OAAO,SAAS,EAAE,CAAC,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC;EACxD,GAAG,CAAC,CAAC;AAEL,QAAM,sBAAsB,kBAAkBD,QAAO;AAErD,QAAM,mBAAmB;IACvB,GAAG;IACH,GAAG;EACL;AAQA,aAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,gBAAgB,GAAG;AAC5D,IAAAA,SAAQ,QAAQ,OAAO,UAAUD,gBAAY,UAAU,MAAM,KAAK,CAAC;EACrE;AAEA,SAAO;IACL,GAAG;IACH,GAAG;EACL;AACF;;;AC5CO,IAAK,cAAL,kBAAKG,iBAAL;AACLA,eAAA,MAAA,IAAO;AACPA,eAAA,KAAA,IAAM;AACNA,eAAA,MAAA,IAAO;AACPA,eAAA,KAAA,IAAM;AACNA,eAAA,OAAA,IAAQ;AACRA,eAAA,SAAA,IAAU;AACVA,eAAA,QAAA,IAAS;AAPC,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;AA4BL,IAAM,cAAN,cAA0B,eAI/B;EACA,YACE,QACA,MACA,UACA,SACA;AACA,UAAM;MACJ,MAAM;QACJ,QAAQ,GAAG,MAAM,IAAI,IAAI;QACzB;QACA;MACF;MACA;MACA;IACF,CAAC;AAED,SAAK,8BAA8B;EACrC;EAEQ,gCAAgC;AACtC,UAAM,EAAE,QAAQ,KAAK,IAAI,KAAK;AAE9B,QAAI,gBAAgB,QAAQ;AAC1B;IACF;AAEA,UAAM,MAAM,SAAS,IAAI;AAGzB,QAAI,QAAQ,MAAM;AAChB;IACF;AAEA,UAAM,eAAe,gBAAgB,IAAI;AACzC,UAAM,cAAwB,CAAC;AAE/B,iBAAa,QAAQ,CAAC,GAAG,cAAc;AACrC,kBAAY,KAAK,SAAS;IAC5B,CAAC;AAED,aAAS;MACP,+EAA+E,MAAM,IAAI,IAAI;IAC/F;EACF;EAEA,MAAM,MAAM,MAGT;AA9GL,QAAAC;AA+GI,UAAM,MAAM,IAAI,IAAI,KAAK,QAAQ,GAAG;AACpC,UAAMC,SAAQ;MACZ;MACA,KAAK,KAAK;OACVD,MAAA,KAAK,sBAAL,gBAAAA,IAAwB;IAC1B;AACA,UAAM,UAAU,qBAAqB,KAAK,OAAO;AAEjD,WAAO;MACL,OAAAC;MACA;IACF;EACF;EAEA,UAAU,MAAmE;AAC3E,UAAM,oBAAoB,KAAK,YAAY,KAAK,QAAQ,MAAM;AAC9D,UAAM,iBAAiB,KAAK,aAAa,MAAM;AAC/C,WAAO,qBAAqB;EAC9B;EAEQ,YAAY,cAA+B;AACjD,WAAO,KAAK,KAAK,kBAAkB,SAC/B,KAAK,KAAK,OAAO,KAAK,YAAY,IAClC,cAAc,KAAK,KAAK,QAAQ,YAAY;EAClD;EAEU,mBAAmB,MAG1B;AA5IL,QAAAD;AA6II,WAAO;MACL,UAAQA,MAAA,KAAK,aAAa,UAAlB,gBAAAA,IAAyB,WAAU,CAAC;MAC5C,SAAS,KAAK,aAAa;IAC7B;EACF;EAEA,MAAM,IAAI,MAAgD;AACxD,UAAM,YAAY,YAAY,KAAK,QAAQ,GAAG;AAC9C,UAAM,gBAAgB,MAAM,iBAAiB,KAAK,OAAO;AACzD,UAAM,iBAAiB,MAAM,kBAAkB,KAAK,QAAQ;AAC5D,UAAM,cAAc,mBAAmB,eAAe,MAAM;AAE5D,YAAQ;MACN,SAAS;QACP,GAAG,aAAa,CAAC,IAAI,KAAK,QAAQ,MAAM,IAAI,SAAS,OACnD,eAAe,MACjB,IAAI,eAAe,UAAU;MAC/B;MACA,SAAS,WAAW;MACpB;IACF;AACA,YAAQ,IAAI,WAAW,aAAa;AACpC,YAAQ,IAAI,YAAY,IAAI;AAC5B,YAAQ,IAAI,YAAY,cAAc;AACtC,YAAQ,SAAS;EACnB;AACF;;;ACnIA,SAAS,kBACP,QACoB;AACpB,SAAO,CAAC,MAAM,UAAU,UAAU,CAAC,MAAM;AACvC,WAAO,IAAI,YAAY,QAAQ,MAAM,UAAU,OAAO;EACxD;AACF;AAWO,IAAM,OAAO;EAClB,KAAK,kBAAkB,IAAI;EAC3B,MAAM,kBAAkB,YAAY,IAAI;EACxC,KAAK,kBAAkB,YAAY,GAAG;EACtC,MAAM,kBAAkB,YAAY,IAAI;EACxC,KAAK,kBAAkB,YAAY,GAAG;EACtC,QAAQ,kBAAkB,YAAY,MAAM;EAC5C,OAAO,kBAAkB,YAAY,KAAK;EAC1C,SAAS,kBAAkB,YAAY,OAAO;AAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/DA,IAAAE,sBAAAC,YAAA;EAAA,mDAAAC,UAAAC,SAAA;AAAA;AAEA,QAAI,sBAAsB;MACxB,cAAc;MACd,KAAK;MACL,QAAQ;IACV;AAEA,aAAS,iBAAiB,KAAK;AAC7B,aAAO,OAAO,QAAQ,YAAY,CAAC,CAAC,IAAI,KAAK;IAC/C;AAEA,aAAS,YAAY,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,eAAe,MAAM,GAAG,EAAE,OAAO,gBAAgB;AAE7D,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,SAAS,mBAAmB,gBAAgB;AAChD,UAAI,OAAO,OAAO;AAClB,UAAI,QAAQ,OAAO;AAEnB,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI;AACF,gBAAQ,QAAQ,eAAe,mBAAmB,KAAK,IAAI;MAC7D,SAAS,GAAG;AACV,gBAAQ;UACN,gFACE,QACA;UACF;QACF;MACF;AAEA,UAAI,SAAS;QACX;QACA;MACF;AAEA,YAAM,QAAQ,SAAU,MAAM;AAC5B,YAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,YAAI,MAAM,MAAM,MAAM,EAAE,SAAS,EAAE,YAAY;AAC/C,YAAIC,SAAQ,MAAM,KAAK,GAAG;AAC1B,YAAI,QAAQ,WAAW;AACrB,iBAAO,UAAU,IAAI,KAAKA,MAAK;QACjC,WAAW,QAAQ,WAAW;AAC5B,iBAAO,SAAS,SAASA,QAAO,EAAE;QACpC,WAAW,QAAQ,UAAU;AAC3B,iBAAO,SAAS;QAClB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAW;QACpB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAWA;QACpB,OAAO;AACL,iBAAO,GAAG,IAAIA;QAChB;MACF,CAAC;AAED,aAAO;IACT;AAEA,aAAS,mBAAmB,kBAAkB;AAG5C,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,eAAe,iBAAiB,MAAM,GAAG;AAC7C,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,MAAM;AAC1B,gBAAQ,aAAa,KAAK,GAAG;MAC/B,OAAO;AACL,gBAAQ;MACV;AAEA,aAAO,EAAE,MAAY,MAAa;IACpC;AAEA,aAASC,OAAM,OAAO,SAAS;AAC7B,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,QAAQ,KAAK;AAChB,iBAAO,CAAC;QACV,OAAO;AACL,iBAAO,CAAC;QACV;MACF;AAEA,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,MAAM,QAAQ,iBAAiB,YAAY;AAGpD,kBAAQ,MAAM,QAAQ,aAAa;QACrC,WAAW,MAAM,QAAQ,YAAY,GAAG;AAEtC,kBAAQ,MAAM,QAAQ,YAAY;QACpC,OAAO;AAEL,cAAI,MACF,MAAM,QACJ,OAAO,KAAK,MAAM,OAAO,EAAE,KAAK,SAAU,KAAK;AAC7C,mBAAO,IAAI,YAAY,MAAM;UAC/B,CAAC,CACH;AAEF,cAAI,CAAC,OAAO,MAAM,QAAQ,UAAU,CAAC,QAAQ,QAAQ;AACnD,oBAAQ;cACN;YACF;UACF;AACA,kBAAQ;QACV;MACF;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAC,KAAK;MAChB;AAEA,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,QAAQ,KAAK;AAChB,eAAO,MAAM,OAAO,gBAAgB,EAAE,IAAI,SAAU,KAAK;AACvD,iBAAO,YAAY,KAAK,OAAO;QACjC,CAAC;MACH,OAAO;AACL,YAAI,UAAU,CAAC;AACf,eAAO,MAAM,OAAO,gBAAgB,EAAE,OAAO,SAAUC,UAAS,KAAK;AACnE,cAAI,SAAS,YAAY,KAAK,OAAO;AACrCA,mBAAQ,OAAO,IAAI,IAAI;AACvB,iBAAOA;QACT,GAAG,OAAO;MACZ;IACF;AAaA,aAASC,oBAAmB,eAAe;AACzC,UAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAO;MACT;AACA,UAAI,OAAO,kBAAkB,UAAU;AACrC,eAAO,CAAC;MACV;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,iBAAiB;AACxB,eAAO,MAAM,cAAc,UAAU,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AACzE,iBAAO;QACT;AACA,eAAO,MAAM,cAAc;MAC7B;AAEA,eAAS,iBAAiB;AACxB,aAAK,cAAc,OAAO,GAAG;AAE7B,eAAO,OAAO,OAAO,OAAO,OAAO,OAAO;MAC5C;AAEA,aAAO,MAAM,cAAc,QAAQ;AACjC,gBAAQ;AACR,gCAAwB;AAExB,eAAO,eAAe,GAAG;AACvB,eAAK,cAAc,OAAO,GAAG;AAC7B,cAAI,OAAO,KAAK;AAEd,wBAAY;AACZ,mBAAO;AAEP,2BAAe;AACf,wBAAY;AAEZ,mBAAO,MAAM,cAAc,UAAU,eAAe,GAAG;AACrD,qBAAO;YACT;AAGA,gBAAI,MAAM,cAAc,UAAU,cAAc,OAAO,GAAG,MAAM,KAAK;AAEnE,sCAAwB;AAExB,oBAAM;AACN,6BAAe,KAAK,cAAc,UAAU,OAAO,SAAS,CAAC;AAC7D,sBAAQ;YACV,OAAO;AAGL,oBAAM,YAAY;YACpB;UACF,OAAO;AACL,mBAAO;UACT;QACF;AAEA,YAAI,CAAC,yBAAyB,OAAO,cAAc,QAAQ;AACzD,yBAAe,KAAK,cAAc,UAAU,OAAO,cAAc,MAAM,CAAC;QAC1E;MACF;AAEA,aAAO;IACT;AAEA,IAAAJ,QAAO,UAAUE;AACjB,IAAAF,QAAO,QAAQ,QAAQE;AACvB,IAAAF,QAAO,QAAQ,cAAc;AAC7B,IAAAA,QAAO,QAAQ,qBAAqBI;EAAA;AAAA,CAAA;ACjOpC,IAAAC,4BAAmCC,SAAAT,oBAAA,CAAA;ACAnC,IAAM,6BAA6B;AAE5B,SAAS,oBAAoB,MAAsB;AACxD,MAAI,2BAA2B,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI;AAC/D,UAAM,IAAI,UAAU,wCAAwC;EAC9D;AAEA,SAAO,KAAK,KAAK,EAAE,YAAY;AACjC;ACRA,IAAM,oBAAoB;EACxB,OAAO,aAAa,EAAI;EACxB,OAAO,aAAa,EAAI;EACxB,OAAO,aAAa,CAAI;EACxB,OAAO,aAAa,EAAI;AAC1B;AAEA,IAAM,6BAA6B,IAAI;EACrC,MAAM,kBAAkB,KAAK,EAAE,CAAC,OAAO,kBAAkB,KAAK,EAAE,CAAC;EACjE;AACF;AAMO,SAAS,qBAAqB,OAAuB;AAC1D,QAAM,YAAY,MAAM,QAAQ,4BAA4B,EAAE;AAC9D,SAAO;AACT;ACfO,SAAS,kBAAkB,OAAgB;AAChD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,YAAY,MAAM,WAAW,CAAC;AAEpC,QAAI,YAAY,OAAQ,CAAC,QAAQ,SAAS,GAAG;AAC3C,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,OAAiC;AAChD,SAAO,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,EAAE,SAAS,KAAK;AAClB;AC1CO,SAAS,mBAAmB,OAAyB;AAC1D,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,MAAM,KAAK,MAAM,OAAO;AAC1B,WAAO;EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,YAAY,MAAM,WAAW,CAAC;AAEpC;;MAEE,cAAc;MAEd,cAAc,MACd,cAAc;MACd;AACA,aAAO;IACT;EACF;AAEA,SAAO;AACT;AJrBO,IAAM,qBAAoC,OAAO,mBAAmB;AAEpE,IAAM,mBAAkC,OAAO,gBAAgB;AAEtE,IAAM,yBAAyB;AAX/B,IAAA;AAAA,IAAA;AAAA,IAAA;AAaO,IAAMU,WAAN,MAAM,SAAQ;EAQnB,YAAY,MAAkD;AAN9D,SAAS,EAAA,IAA8C,CAAC;AAIxD,SAAS,EAAA,IAAyC,oBAAI,IAAI;AAmC1D,SAAC,EAAA,IAAsB;AA5BrB,QACE,CAAC,WAAW,iBAAiB,EAAE,SAAS,6BAAM,YAAY,IAAI,KAC9D,gBAAgB,YACf,OAAO,WAAW,YAAY,eAC7B,gBAAgB,WAAW,SAC7B;AACA,YAAM,iBAAiB;AACvB,qBAAe,QAAQ,CAAC,OAAO,SAAS;AACtC,aAAK,OAAO,MAAM,KAAK;MACzB,GAAG,IAAI;IACT,WAAW,MAAM,QAAQ,IAAI,GAAG;AAC9B,WAAK,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9B,aAAK;UACH;UACA,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,sBAAsB,IAAI;QAC9D;MACF,CAAC;IACH,WAAW,MAAM;AACf,aAAO,oBAAoB,IAAI,EAAE,QAAQ,CAAC,SAAS;AACjD,cAAM,QAAQ,KAAK,IAAI;AACvB,aAAK;UACH;UACA,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,sBAAsB,IAAI;QAC9D;MACF,CAAC;IACH;EACF;EAIA,EAzCS,KAAA,oBAIA,KAAA,kBAmCR,KAAA,OAAO,aAEP,OAAO,SAAQ,IAAI;AAClB,WAAO,KAAK,QAAQ;EACtB;EAEA,CAAC,OAAiC;AAChC,eAAW,CAAC,IAAI,KAAK,KAAK,QAAQ,GAAG;AACnC,YAAM;IACR;EACF;EAEA,CAAC,SAAmC;AAClC,eAAW,CAAC,EAAE,KAAK,KAAK,KAAK,QAAQ,GAAG;AACtC,YAAM;IACR;EACF;EAEA,CAAC,UAA8C;AAE7C,QAAI,aAAa,OAAO,KAAK,KAAK,kBAAkB,CAAC,EAAE;MAAK,CAAC,GAAG,MAC9D,EAAE,cAAc,CAAC;IACnB;AACA,eAAW,QAAQ,YAAY;AAC7B,UAAI,SAAS,cAAc;AACzB,mBAAW,SAAS,KAAK,aAAa,GAAG;AACvC,gBAAM,CAAC,MAAM,KAAK;QACpB;MACF,OAAO;AACL,cAAM,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC;MAC7B;IACF;EACF;;;;EAKA,IAAI,MAAuB;AACzB,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,YAAM,IAAI,UAAU,wBAAwB,IAAI,GAAG;IACrD;AAEA,WAAO,KAAK,kBAAkB,EAAE,eAAe,oBAAoB,IAAI,CAAC;EAC1E;;;;EAKA,IAAI,MAA6B;;AAC/B,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,YAAM,UAAU,wBAAwB,IAAI,GAAG;IACjD;AAEA,YAAOC,MAAA,KAAK,kBAAkB,EAAE,oBAAoB,IAAI,CAAC,MAAlD,OAAAA,MAAuD;EAChE;;;;EAKA,IAAI,MAAc,OAAqB;AACrC,QAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,mBAAmB,KAAK,GAAG;AAC1D;IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,UAAM,kBAAkB,qBAAqB,KAAK;AAElD,SAAK,kBAAkB,EAAE,cAAc,IACrC,qBAAqB,eAAe;AACtC,SAAK,gBAAgB,EAAE,IAAI,gBAAgB,IAAI;EACjD;;;;EAKA,OAAO,MAAc,OAAqB;AACxC,QAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,mBAAmB,KAAK,GAAG;AAC1D;IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,UAAM,kBAAkB,qBAAqB,KAAK;AAElD,QAAI,gBAAgB,KAAK,IAAI,cAAc,IACvC,GAAG,KAAK,IAAI,cAAc,CAAC,KAAK,eAAe,KAC/C;AAEJ,SAAK,IAAI,MAAM,aAAa;EAC9B;;;;EAKA,OAAO,MAAoB;AACzB,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B;IACF;AAEA,QAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACnB;IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,WAAO,KAAK,kBAAkB,EAAE,cAAc;AAC9C,SAAK,gBAAgB,EAAE,OAAO,cAAc;EAC9C;;;;;EAMA,QACE,UAMA,SACA;AACA,eAAW,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG;AAC1C,eAAS,KAAK,SAAS,OAAO,MAAM,IAAI;IAC1C;EACF;;;;;;EAOA,eAAyB;AACvB,UAAM,kBAAkB,KAAK,IAAI,YAAY;AAE7C,QAAI,oBAAoB,MAAM;AAC5B,aAAO,CAAC;IACV;AAEA,QAAI,oBAAoB,IAAI;AAC1B,aAAO,CAAC,EAAE;IACZ;AAEA,YAAA,GAAOH,0BAAA,oBAAmB,eAAe;EAC3C;AACF;;;AetLO,IAAM,QAAQ,OAInB,YAC6C;AAC7C,MAAI;AACF,UAAM,OAAO,MAAM,QAAQ,EAAE,MAAM,CAACI,WAAU;AAC5C,YAAMA;IACR,CAAC;AACD,WAAO,EAAE,OAAO,MAAM,KAAK;EAC7B,SAASA,QAAP;AACA,WAAO,EAAE,OAAAA,QAAO,MAAM,KAAK;EAC7B;AACF;;;ACTO,IAAM,kBAAkB,OAA+C;EAC5E,SAAAC;EACA;EACA;EACA;AACF,MAK+C;AAC7C,MAAI,kBAAyC;AAC7C,MAAI,SAAoD;AAExD,aAAW,WAAW,UAAU;AAC9B,aAAS,MAAM,QAAQ,IAAI,EAAE,SAAAA,UAAS,WAAW,kBAAkB,CAAC;AAIpE,QAAI,WAAW,MAAM;AACnB,wBAAkB;IACpB;AAOA,QAAI,iCAAQ,UAAU;AACpB;IACF;EACF;AAEA,MAAI,iBAAiB;AACnB,WAAO;MACL,SAAS;MACT,cAAc,iCAAQ;MACtB,UAAU,iCAAQ;IACpB;EACF;AAEA,SAAO;AACT;;;AC3CA,eAAsB,mBACpBC,UACA,WAAqC,QACtB;AACf,QAAM,MAAM,IAAI,IAAIA,SAAQ,GAAG;AAC/B,QAAM,YAAY,YAAY,GAAG,IAAI,IAAI;AAEzC,QAAM,0BAA0B;;WAAyEA,SAAQ,MAAM,IAAI,SAAS;;;;AAEpI,WAAS,cAAcC,WAAoC;AACzD,YAAQA,WAAU;MAChB,KAAK,SAAS;AAEZ,iBAAS,MAAM,aAAa,uBAAuB;AAGnD,cAAM,IAAI;UACR,SAAS;YACP;UACF;QACF;MACF;MAEA,KAAK,QAAQ;AACX,iBAAS,KAAK,eAAe,uBAAuB;AACpD;MACF;MAEA,KAAK;AACH;MAEF;AACE,cAAM,IAAI;UACR,SAAS;YACP;YACAA;UACF;QACF;IACJ;EACF;AAEA,MAAI,OAAO,aAAa,YAAY;AAClC,aAASD,UAAS;MAChB,SAAS,cAAc,KAAK,MAAM,MAAM;MACxC,OAAO,cAAc,KAAK,MAAM,OAAO;IACzC,CAAC;AACD;EACF;AAQA,MAAI,IAAI,aAAa,SAAS;AAC5B;EACF;AAEA,gBAAc,QAAQ;AACxB;;;AC7EO,SAAS,oBACdE,UACA,UACM;AACN,QAAM,IAAI,EAAE,GAAGA,UAAS,KAAKA,SAAQ,IAAI,SAAS,EAAE,GAAG,QAAQ;AAC/D,QAAM,QAAQ;AAChB;;;ACoCA,eAAsB,cACpBC,UACA,WACA,UACA,SACA,SACA,sBAC+B;AAnDjC,MAAAC,KAAAC,KAAAC,KAAA;AAoDE,UAAQ,KAAK,iBAAiB,EAAE,SAAAH,UAAS,UAAU,CAAC;AAGpD,MAAIA,SAAQ,QAAQ,IAAI,iBAAiB,MAAM,UAAU;AACvD,YAAQ,KAAK,eAAe,EAAE,SAAAA,UAAS,UAAU,CAAC;AAClD,KAAAC,MAAA,6DAAsB,0BAAtB,gBAAAA,IAAA,2BAA8CD;AAC9C;EACF;AAGA,QAAM,eAAe,MAAM,MAAM,MAAM;AACrC,WAAO,gBAAgB;MACrB,SAAAA;MACA;MACA;MACA,mBAAmB,6DAAsB;IAC3C,CAAC;EACH,CAAC;AAED,MAAI,aAAa,OAAO;AAEtB,YAAQ,KAAK,sBAAsB;MACjC,OAAO,aAAa;MACpB,SAAAA;MACA;IACF,CAAC;AACD,UAAM,aAAa;EACrB;AAIA,MAAI,CAAC,aAAa,MAAM;AACtB,UAAM,mBAAmBA,UAAS,QAAQ,kBAAkB;AAC5D,YAAQ,KAAK,qBAAqB,EAAE,SAAAA,UAAS,UAAU,CAAC;AACxD,YAAQ,KAAK,eAAe,EAAE,SAAAA,UAAS,UAAU,CAAC;AAClD,KAAAE,MAAA,6DAAsB,0BAAtB,gBAAAA,IAAA,2BAA8CF;AAC9C;EACF;AAEA,QAAM,EAAE,SAAS,IAAI,aAAa;AAIlC,MAAI,CAAC,UAAU;AACb,YAAQ,KAAK,eAAe,EAAE,SAAAA,UAAS,UAAU,CAAC;AAClD,KAAAG,MAAA,6DAAsB,0BAAtB,gBAAAA,IAAA,2BAA8CH;AAC9C;EACF;AAIA,MACE,SAAS,WAAW,OACpB,SAAS,QAAQ,IAAI,iBAAiB,MAAM,eAC5C;AACA,YAAQ,KAAK,eAAe,EAAE,SAAAA,UAAS,UAAU,CAAC;AAClD,uEAAsB,0BAAtB,8CAA8CA;AAC9C;EACF;AAGA,sBAAoBA,UAAS,QAAQ;AAErC,UAAQ,KAAK,iBAAiB,EAAE,SAAAA,UAAS,UAAU,CAAC;AAEpD,QAAM,uBACJ,aAAa;AAEf,QAAM,wBACJ,kEAAsB,sBAAtB,8CAA0C,cACzC;AAEH,qEAAsB,qBAAtB;;IACE;IACA;;AAGF,UAAQ,KAAK,eAAe,EAAE,SAAAA,UAAS,UAAU,CAAC;AAElD,SAAO;AACT;;;AChIA,IAAM,EAAE,SAAAI,SAAQ,IAAI;AAQb,SAAS,sBACd,OAAyB,CAAC,GACC;AAC3B,QAAM,UAAS,6BAAM,WAAU;AAC/B,QAAM,cAAa,6BAAM,eAAcA,SAAQ,MAAM,KAAK;AAC1D,QAAM,UAAU,IAAI,QAAQ,6BAAM,OAAO;AAEzC,SAAO;IACL,GAAG;IACH;IACA;IACA;EACF;AACF;AAEO,SAAS,iBACd,UACA,MACU;AAEV,MAAI,KAAK,MAAM;AACb,WAAO,eAAe,UAAU,QAAQ;MACtC,OAAO,KAAK;MACZ,YAAY;MACZ,UAAU;IACZ,CAAC;EACH;AAGA,MAAI,OAAO,aAAa,aAAa;AAMnC,UAAM,kBAAkBC,SAAgB,UAAU,aAAa;MAC7D,KAAK;IACP;AAEA,eAAW,gBAAgB,iBAAiB;AAG1C,eAAS,SAAS;IACpB;EACF;AAEA,SAAO;AACT;;;ACtBO,IAAM,eAAN,MAAM,sBAAqB,SAAS;EACzC,YAAY,MAAwB,MAAyB;AAC3D,UAAM,eAAe,sBAAsB,IAAI;AAC/C,UAAM,MAAM,YAAY;AACxB,qBAAiB,MAAM,YAAY;EACrC;;;;;;;EAQA,OAAO,KACL,MACA,MAC0B;AAC1B,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,YAAY;IACvD;AAKA,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;QACnB;QACA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI;MAC5C;IACF;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;EAQA,OAAO,KACL,MACA,MAC0B;AAC1B,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,kBAAkB;IAC7D;AAMA,UAAM,eAAe,KAAK,UAAU,IAAI;AAExC,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;QACnB;QACA,eAAe,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,IAAI;MAC5D;IACF;AAEA,WAAO,IAAI;MACT;MACA;IACF;EACF;;;;;;;EAQA,OAAO,IACL,MACA,MACU;AACV,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,UAAU;IACrD;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;;;;EAWA,OAAO,YAAY,MAAoB,MAAmC;AACxE,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,MAAM;AACR,mBAAa,QAAQ,IAAI,kBAAkB,KAAK,WAAW,SAAS,CAAC;IACvE;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;;;EAUA,OAAO,SAAS,MAAiB,MAAmC;AAClE,WAAO,IAAI,cAAa,MAAM,sBAAsB,IAAI,CAAC;EAC3D;AACF;;;AClGA,aAAa;;;AC3Db,8BAAkC;;;AGe3B,SAAS,yBAG4B;AAC1C,QAAM,WAAoD,CACxD,SACA,WACG;AACH,aAAS,QAAQ;AAEjB,aAAS,UAAU,CAAC,SAAS;AAC3B,UAAI,SAAS,UAAU,WAAW;AAChC;MACF;AAEA,eAAS,SAAS;AAElB,YAAM,cAAc,CAAQ,UAAiB;AAC3C,iBAAS,QAAQ;AACjB,eAAO;MACT;AAEA,aAAO;QACL,gBAAgB,UAAU,OAAO,QAAQ,QAAQ,IAAI,EAAE,KAAK,WAAW;MACzE;IACF;AAEA,aAAS,SAAS,CAAC,WAAW;AAC5B,UAAI,SAAS,UAAU,WAAW;AAChC;MACF;AAEA,qBAAe,MAAM;AACnB,iBAAS,QAAQ;MACnB,CAAC;AAED,aAAO,OAAQ,SAAS,kBAAkB,MAAO;IACnD;EACF;AAEA,SAAO;AACT;;AChDO,IAAM,mBAANC,MAAA,cAAqD,QAAe;EAMzE,YAAY,WAAmC,MAAM;AACnD,UAAM,mBAAmB,uBAAuB;AAChD,UAAM,CAAC,iBAAiB,mBAAmB;AACzC,uBAAiB,iBAAiB,cAAc;AAChD,2CAAW,iBAAiB,SAAS,iBAAiB;IACxD,CAAC;AAgCH;AA1CA;AAEO;AACA;AASL,uBAAK,WAAY;AACjB,SAAK,UAAU,mBAAK,WAAU;AAC9B,SAAK,SAAS,mBAAK,WAAU;EAC/B;EAEA,IAAW,QAAQ;AACjB,WAAO,mBAAK,WAAU;EACxB;EAEA,IAAW,kBAAkB;AAC3B,WAAO,mBAAK,WAAU;EACxB;EAEO,KACL,aACA,YACA;AACA,WAAO,sBAAK,wBAAL,WAAe,MAAM,KAAK,aAAa,UAAU;EAC1D;EAEO,MACL,YACA;AACA,WAAO,sBAAK,wBAAL,WAAe,MAAM,MAAM,UAAU;EAC9C;EAEO,QAAQ,WAAuC;AACpD,WAAO,sBAAK,wBAAL,WAAe,MAAM,QAAQ,SAAS;EAC/C;AAUF,GAlDE,2BA0CA,yCAAA,SACE,SACqC;AACrC,SAAO,OAAO,iBAAiB,SAAS;IACtC,SAAS,EAAE,cAAc,MAAM,OAAO,KAAK,QAAQ;IACnD,QAAQ,EAAE,cAAc,MAAM,OAAO,KAAK,OAAO;EACnD,CAAC;AACH,GAlDKA;;;ACLA,IAAM,oBAAN,MAAwB;EAG7B,YAAsBC,UAAkB;AAAlB,SAAA,UAAAA;AACpB,SAAK,kBAAkB,IAAI,gBAAgB;EAC7C;EAEO,YAAY,UAA2B;AAC5C,IAAAC;MACE,KAAK,gBAAgB,UAAU;MAC/B;MACA,KAAK,QAAQ;MACb,KAAK,QAAQ;IACf;AAEA,SAAK,gBAAgB,QAAQ,QAAQ;EACvC;AACF;ACdO,SAAS,qBAAqBD,UAGnC;AACA,QAAM,oBAAoB,IAAI,kBAAkBA,QAAO;AAEvD,UAAQ;IACNA;IACA;IACA,kBAAkB,YAAY,KAAK,iBAAiB;EACtD;AAEA,SAAO;IACL,oBAAoBA;IACpB;EACF;AACF;ACfA,eAAsB,UAIpB,SACA,cACG,MACY;AACf,QAAM,WAAW,QAAQ,UAAU,SAAS;AAE5C,MAAI,SAAS,WAAW,GAAG;AACzB;EACF;AAEA,aAAW,YAAY,UAAU;AAC/B,UAAM,SAAS,MAAM,SAAS,IAAI;EACpC;AACF;;;ACxBA,IAAAE,eAAiB;AACjB,mBAAkB;ACDlB,IAAAA,eAA6D;AGA7D,IAAAC,eAAgC;AAChC,oBAA4B;AOD5B,IAAAA,eAIO;AACP,IAAAC,gBAIO;AACP,iBAAoD;AEVpD,IAAAC,eAAsB;AXEtB,IAAM,SAAS,IAAI,OAAO,8BAA8B;AAqBjD,SAAS,iCACX,MAC6B;AAChC,SAAO,KAAK,aAAa,IAAI;AAC7B,QAAM,iBAAiB,IAAI,MAAM,CAAC,EAC/B,KAAK,IAAI,EACT,IAAI,CAAC,OAAO,UAAU,KAAK,KAAK,KAAK,KAAK;AAE7C,iBAAe,KAAK,CAAC,GAAG,MAAM;AAE5B,QAAI,OAAO,MAAM,YAAY;AAC3B,aAAO;IACT;AAGA,QAAI,OAAO,MAAM,YAAY;AAC3B,aAAO;IACT;AAGA,QAAI,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAClD,aAAO,eAAe,QAAQ,CAAC,IAAI,eAAe,QAAQ,CAAC;IAC7D;AAEA,WAAO;EACT,CAAC;AAED,SAAO,KAAK,mBAAmB,cAAc;AAC7C,SAAO;AACT;AClDA,IAAMC,UAAS,IAAIC,OAAO,yBAAyB;AAe5C,SAAS,gCACd,MACkC;AAClCD,UAAO,KAAK,gDAAgD,IAAI;AAEhE,QAAM,QAAQ,KAAK,CAAC;AACpB,QAAM,WACJ,OAAO,KAAK,CAAC,MAAM,WAAY,KAAK,CAAC,IAAuB;AAC9D,QAAM,WAAW,OAAO,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,IAAI,KAAK,CAAC;AAEjE,QAAM,YAA8C;IAClD;IACA;IACA;EACF;AACAA,UAAO;IACL;IACA;EACF;AAEA,SAAO;AACT;ACnCO,IAAM,WAAW,OAAO,SAAS;AASjC,SAAS,qBACdE,UACuB;AACvB,QAAM,QAAQA,SAAQ,KAAK,IAAI,0BAAY,CAAC;AAG5C,oBAAkBA,UAAS,KAAK;AAGhC,QAAM,kBAAkB,OAAO,OAAO,6BAAgB,SAAS;AAC/D,gBAAc,KAAK,EAAE,QAAQ,CAAC,cAAc;AAC1C,sBAAkB,WAAW,eAAe;EAC9C,CAAC;AACD,SAAO,eAAe,OAAO,eAAe;AAE5C,SAAO,eAAe,OAAO,UAAU;IACrC,YAAY;IACZ,OAAO;EACT,CAAC;AAED,SAAO;AACT;AAKA,SAAS,cAAc,QAA0B;AAC/C,QAAM,aAAuB,CAAC;AAC9B,MAAI,UAAU;AAEd,SAAQ,UAAU,OAAO,eAAe,OAAO,GAAI;AACjD,eAAW,KAAK,OAAO;EACzB;AAEA,SAAO;AACT;AAQA,SAAS,kBAAkB,QAAgB,QAAsB;AAC/D,QAAM,aAAa;IACjB,GAAG,OAAO,oBAAoB,MAAM;IACpC,GAAG,OAAO,sBAAsB,MAAM;EACxC;AAEA,aAAW,YAAY,YAAY;AACjC,QAAI,OAAO,eAAe,QAAQ,GAAG;AACnC;IACF;AAEA,UAAM,aAAa,OAAO,yBAAyB,QAAQ,QAAQ;AACnE,QAAI,CAAC,YAAY;AACf;IACF;AAEA,WAAO,eAAe,QAAQ,UAAU,UAAU;EACpD;AACF;AClEO,SAAS,eAAeA,UAAoC;AACjE,QAAM,qBAAqB,sBAAsBA,SAAQ,cAAc,GAAG,IACtE,OACA,IAAI,eAAe;IACjB,MAAM,YAAY;AAChB,MAAAA,SAAQ,GAAG,QAAQ,CAAC,UAAU,WAAW,QAAQ,KAAK,CAAC;AACvD,MAAAA,SAAQ,GAAG,OAAO,MAAM,WAAW,MAAM,CAAC;IAO5C;EACF,CAAC;AAEL,SAAO,IAAI,SAAS,oBAAoB;IACtC,QAAQA,SAAQ;IAChB,YAAYA,SAAQ;IACpB,SAAS,qCAAqCA,SAAQ,OAAO;EAC/D,CAAC;AACH;AAEA,SAAS,qCACP,aACS;AACT,QAAM,UAAU,IAAI,QAAQ;AAE5B,aAAW,cAAc,aAAa;AACpC,UAAM,eAAe,YAAY,UAAU;AAE3C,QAAI,OAAO,iBAAiB,aAAa;AACvC;IACF;AAEA,QAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,gBAAQ,OAAO,YAAY,WAAW;MACxC,CAAC;AAED;IACF;AAEA,YAAQ,IAAI,YAAY,YAAY;EACtC;AAEA,SAAO;AACT;ACjDO,SAAS,cAAc,eAA2C;AACvE,QAAM,UAAU,IAAI,QAAQ;AAE5B,QAAM,kBAAkB,cAAc,WAAW;AACjD,aAAW,cAAc,iBAAiB;AACxC,UAAM,cAAc,gBAAgB,UAAU;AAE9C,QAAI,OAAO,gBAAgB,aAAa;AACtC;IACF;AAEA,UAAM,aAAa,MAAM,UAAU,OAAO,CAAC,GAAG,WAAW;AACzD,eAAW,SAAS,YAAY;AAC9B,cAAQ,OAAO,YAAY,MAAM,SAAS,CAAC;IAC7C;EACF;AAOA,MAAI,cAAc,IAAI,YAAY,cAAc,IAAI,UAAU;AAC5D,UAAM,WAAW,mBAAmB,cAAc,IAAI,YAAY,EAAE;AACpE,UAAM,WAAW,mBAAmB,cAAc,IAAI,YAAY,EAAE;AACpE,UAAM,OAAO,GAAG,QAAA,IAAY,QAAA;AAC5B,YAAQ,IAAI,iBAAiB,SAAS,KAAK,IAAI,CAAA,EAAG;AAIlD,kBAAc,IAAI,WAAW;AAC7B,kBAAc,IAAI,WAAW;EAC/B;AAEA,QAAM,SAAS,cAAc,UAAU;AAEvC,SAAO,IAAI,QAAQ,cAAc,KAAK;IACpC;IACA;IACA,aAAa;IACb,MACE,WAAW,UAAU,WAAW,QAC5B,OACA,cAAc;EACtB,CAAC;AACH;AC/CO,SAAS,iBACd,YACA,QACe;AACf,QAAM,aAAa,OAAO,sBAAsB,MAAM;AAEtD,QAAM,SAAS,WAAW,KAAK,CAACC,YAAW;AACzC,WAAOA,QAAO,gBAAgB;EAChC,CAAC;AAED,MAAI,QAAQ;AACV,WAAO,QAAQ,IAAI,QAAQ,MAAM;EACnC;AAEA;AACF;ACfO,SAAS,SAAY,OAAY,QAAQ,OAAmB;AACjE,SAAO,QACH,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,WAAW,UAAU,IAC3D,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAChD;ACQO,SAAS,mBACd,SAC2B;AAC3B,QAAM,cAAc,iBAAyB,gBAAgB,OAAO;AAEpE,MAAI,CAAC,aAAa;AAChB;EACF;AAEA,QAAM,aAAa,iBAEjB,eAAe,WAAW;AAO5B,MAAI,CAAC,cAAc,CAAC,+BAA+B,UAAU,GAAG;AAC9D;EACF;AAGA,QAAM,aAA4B,oBAAI,IAAoB;AAE1D,aAAW,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AACtC,eAAW,IAAI,MAAM,KAAK;EAC5B,CAAC;AAED,SAAO;AACT;AAEA,SAAS,+BACP,YAC6C;AAC7C,SAAO,MAAM;IACX,WAAW,OAAO;EACpB,EAAE,MAAM,CAAC,UAAU;AACjB,WAAO,SAA2B,KAAK,KAAK,UAAU;EACxD,CAAC;AACH;ACvDO,SAAS,gBACdC,QACgC;AAChC,MAAIA,UAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,EAAEA,kBAAiB,QAAQ;AAC7B,WAAO;EACT;AAEA,SAAO,UAAUA,UAAS,WAAWA;AACvC;AToCO,IAAM,qBAAN,cAAgC,2BAAc;EA+BnD,YACE,CAAC,KAAK,gBAAgB,QAAQ,GAC9B,SACA;AACA,UAAM,gBAAgB,QAAQ;AAbhC,SAAQ,SAGH,CAAC;AAYJ,SAAK,SAAS,QAAQ,OAAO;MAC3B,WAAW,eAAe,MAAA,IAAU,IAAI,IAAA;IAC1C;AAEA,SAAK,OAAO,KAAK,6CAA6C;MAC5D;MACA;MACA;IACF,CAAC;AAED,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,UAAU,QAAQ;AAIvB,SAAK,gBAAgB;AAGrB,SAAK,WAAW,IAAIC,aAAAA,gBAAgB,KAAK,MAAO;EAClD;EAEQ,sBACN,OACA,UACM;AACN,QAAI,SAAS,MAAM;AACjB;IACF;AAEA,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,gBAAgB,OAAO,KAAK,CAAC,CAAC;IACrC;AAEA,UAAM,gBAAgB,OAAO,SAAS,KAAK,IACvC,QACA,OAAO,KAAK,OAAO,QAAQ;AAE/B,SAAK,gBAAgB,OAAO,OAAO,CAAC,KAAK,eAAe,aAAa,CAAC;EACxE;EAEA,SAAS,MAAuC;AA9HlD,QAAAC;AA+HI,UAAM,CAAC,OAAO,UAAU,QAAQ,IAAI,gCAAgC,IAAI;AACxE,SAAK,OAAO,KAAK,UAAU,EAAE,OAAO,UAAU,SAAS,CAAC;AACxD,SAAK,OAAO,KAAK,EAAE,OAAO,SAAS,CAAC;AAGpC,SAAK,sBAAsB,OAAO,QAAQ;AAE1C,SAAK,OAAO;MACV;OACAA,MAAA,KAAK,kBAAL,OAAA,SAAAA,IAAoB;IACtB;AAMA,QAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,WAAK,OAAO,KAAK,8CAA8C;IACjE,OAAO;AACL,kBAAA,OAAA,SAAA,SAAA;IACF;AAKA,WAAO;EACT;EAEA,OAAO,MAAiB;AACtB,SAAK,OAAO,KAAK,OAAO,IAAI;AAE5B,UAAM,YAAY,gBAAgB;AAElC,UAAM,CAAC,OAAO,UAAU,QAAQ,IAAI,8BAA8B,GAAG,IAAI;AACzE,SAAK,OAAO,KAAK,yBAAyB,EAAE,OAAO,UAAU,SAAS,CAAC;AAGvE,SAAK,sBAAsB,OAAO,YAAY,MAAS;AAavD,SAAK,QAAQ;AAEb,UAAM,kBAAkB,cAAc,IAAI;AAC1C,UAAM,EAAE,oBAAoB,kBAAkB,IAC5C,qBAAqB,eAAe;AAOtC,WAAO,eAAe,iBAAiB,eAAe;MACpD,OAAO,kBAAkB,YAAY,KAAK,iBAAiB;IAC7D,CAAC;AAMD,QAAI,KAAK,UAAU,+BAA+B,GAAG;AACnD,WAAK,aAAa,+BAA+B;AACjD,aAAO,KAAK,YAAY,OAAO,UAAU,QAAQ;IACnD;AAMA,SAAK,QAAQ,KAAK,WAAW,CAAC,EAAE,WAAW,iBAAiB,MAAM;AAKhE,UAAI,qBAAqB,WAAW;AAClC;MACF;AAEA,UAAI,kBAAkB,gBAAgB,UAAU,WAAW;AACzD,aAAK,OAAO;UACV;QACF;AAEA,0BAAkB,gBAAgB,QAAQ,MAAS;MACrD;IACF,CAAC;AAID,UAAqC,YAAY;AAG/C,WAAK,OAAO;QACV;QACA,KAAK,QAAQ,cAAc,SAAS;MACtC;AAEA,WAAK,QAAQ;AAEb,YAAM,UAAU,KAAK,SAAS,WAAW;QACvC,SAAS;QACT;MACF,CAAC;AAED,WAAK,OAAO,KAAK,+BAA+B;AAEhD,YAAM,iBAAiB,MAAM,kBAAkB;AAC/C,WAAK,OAAO,KAAK,kCAAkC,cAAc;AAEjE,aAAO;IACT,CAAC,EAAE,KAAK,CAAC,mBAAmB;AAC1B,WAAK,OAAO,KAAK,gCAAgC;AAEjD,WAAK,QAAQ;AAQb,UAAI,CAAC,KAAK,aAAa;AAGrB,mBAAW,CAAC,YAAY,WAAW,KAAK,gBAAgB,SAAS;AAC/D,eAAK,UAAU,YAAY,WAAW;QACxC;MACF;AAEA,UAAI,eAAe,OAAO;AACxB,aAAK,OAAO;UACV;UACA,eAAe;QACjB;AAGA,YAAI,eAAe,iBAAiB,UAAU;AAE5C,cAAI,gBAAgB,eAAe,KAAK,GAAG;AACzC,iBAAK,OAAO;cACV;YACF;AAEA,iBAAK,UAAU,IAAI,UAAU,eAAe,CAAC;UAC/C,OAAO;AAEL,iBAAK,YAAY,eAAe,KAAK;UACvC;AAEA;QACF;AAIA,YAAI,gBAAgB,eAAe,KAAK,GAAG;AACzC,eAAK,UAAU,eAAe,KAAK;AACnC,iBAAO;QACT;AAEA,cAAM,YAAY;AAChB,cAAI,KAAK,QAAQ,cAAc,oBAAoB,IAAI,GAAG;AAIxD,kBAAM,UAAU,KAAK,SAAS,sBAAsB;cAClD,OAAO,eAAe;cACtB,SAAS;cACT;cACA,YAAY;gBACV,aAAa,KAAK,YAAY,KAAK,IAAI;gBACvC,WAAW,KAAK,UAAU,KAAK,IAAI;cACrC;YACF,CAAC;AAKD,gBAAI,KAAK,iBAAiB,KAAK,WAAW;AACxC;YACF;UACF;AAKA,eAAK,YAAY,0BAA0B,eAAe,KAAK,CAAC;QAClE,CAAC;AAED,eAAO;MACT;AAEA,YAAM,iBAAiB,eAAe;AAEtC,UAAI,gBAAgB;AAClB,aAAK,OAAO;UACV;UACA,eAAe;UACf,eAAe;QACjB;AAMA,aAAK,YAAY;AAGjB,YAAI,gBAAgB,cAAc,GAAG;AACnC,eAAK,OAAO;YACV;UACF;AAMA,eAAK,UAAU,IAAI,UAAU,eAAe,CAAC;AAE7C,iBAAO;QACT;AAEA,cAAM,gBAAgB,eAAe,MAAM;AAE3C,aAAK,YAAY,cAAc;AAC/B,aAAK,OAAO;UACV,eAAe;UACf,eAAe;UACf;QACF;AAEA,oBAAA,OAAA,SAAA,SAAA;AAEA,aAAK,OAAO,KAAK,yCAAyC;AAC1D,aAAK,QAAQ,KAAK,YAAY;UAC5B,UAAU;UACV,kBAAkB;UAClB,SAAS;UACT;QACF,CAAC;AAED,aAAK,OAAO,KAAK,6BAA6B;AAE9C,eAAO;MACT;AAEA,WAAK,OAAO,KAAK,8BAA8B;AAE/C,WAAK,KAAK,qBAAqB,CAACJ,aAA6B;AAC3D,aAAK,OAAO,KAAKA,SAAQ,YAAYA,SAAQ,aAAa;AAC1D,aAAK,OAAO,KAAK,8BAA8BA,SAAQ,OAAO;AAE9D,aAAK,OAAO,KAAK,yCAAyC;AAC1D,aAAK,QAAQ,KAAK,YAAY;UAC5B,UAAU,eAAeA,QAAO;UAChC,kBAAkB;UAClB,SAAS;UACT;QACF,CAAC;MACH,CAAC;AAED,aAAO,KAAK,YAAY,OAAO,UAAU,QAAQ;IACnD,CAAC;AAED,WAAO;EACT;EAEA,KAAK,UAAkB,MAAa;AAClC,SAAK,OAAO,KAAK,YAAY,KAAK;AAElC,QAAI,UAAU,YAAY;AACxB,WAAK,OAAO,KAAK,iDAAiD;AAElE,UAAI;AASF,cAAM,WAAW,KAAK,CAAC;AACvB,cAAM,aAAa,qBAAqB,QAAQ;AAChD,cAAM,cAAc,qBAAqB,QAAQ;AAEjD,aAAK,KAAK,qBAAqB,WAAW;AAE1C,aAAK,OAAO;UACV;QACF;AACA,eAAO,MAAM,KAAK,OAAO,YAAY,GAAG,KAAK,MAAM,CAAC,CAAC;MACvD,SAASE,QAAP;AACA,aAAK,OAAO,KAAK,gCAAgCA,MAAK;AACtD,eAAO,MAAM,KAAK,OAAO,GAAG,IAAI;MAClC;IACF;AAEA,QAAI,UAAU,SAAS;AACrB,YAAMA,SAAQ,KAAK,CAAC;AACpB,YAAM,YAAYA,OAAM,QAAQ;AAEhC,WAAK,OAAO,KAAK,YAAYA,MAAK;AAGlC,UAAI,mBAAkB,mBAAmB,SAAS,SAAS,GAAG;AAI5D,YAAI,KAAK,QAAQ,GAAuC;AACtD,cAAI,CAAC,KAAK,eAAe;AACvB,iBAAK,gBAAgBA;AACrB,iBAAK,OAAO,KAAK,6BAA6B,KAAK,aAAa;UAClE;AACA,iBAAO;QACT;AAKA,YACE,KAAK,UAAU,KACf,KAAK,iBAAiB,QACtB;AACA,iBAAO;QACT;MACF;IACF;AAEA,WAAO,MAAM,KAAK,OAAO,GAAG,IAAI;EAClC;;;;;;;EAQQ,YACN,OACA,UACA,UACM;AACN,SAAK,QAAQ;AACb,SAAK,eAAe;AAIpB,QAAI,KAAK,eAAe;AACtB,WAAK,KAAK,SAAS,KAAK,aAAa;AACrC,aAAO;IACT;AAEA,SAAK,OAAO,KAAK,6BAA6B,KAAK,MAAM;AAMzD,eAAW,EAAE,OAAAG,QAAO,UAAAC,UAAS,KAAK,KAAK,QAAQ;AAC7C,UAAIA,WAAU;AACZ,cAAM,MAAMD,QAAOC,SAAQ;MAC7B,OAAO;AACL,cAAM,MAAMD,MAAK;MACnB;IACF;AAEA,SAAK,KAAK,SAAS,CAACH,WAAU;AAC5B,WAAK,OAAO,KAAK,2BAA2BA,MAAK;IACnD,CAAC;AAED,SAAK,KAAK,SAAS,MAAM;AACvB,WAAK,OAAO,KAAK,2BAA2B;IAC9C,CAAC;AAED,SAAK,KAAK,qBAAqB,CAACF,aAA6B;AAC3D,WAAK,OAAO,KAAKA,SAAQ,YAAYA,SAAQ,aAAa;AAC1D,WAAK,OAAO,KAAK,8BAA8BA,SAAQ,OAAO;IAChE,CAAC;AAED,SAAK,OAAO,KAAK,gCAAgC;AAGjD,WAAO,MAAM,IAAI,GAAG,CAAC,OAAO,UAAiB,QAAQ,EAAE,OAAO,OAAO,CAAC;EACxE;;;;EAKQ,YAAY,gBAAgC;AAClD,SAAK,OAAO,KAAK,wCAAwC,cAAc;AAEvE,SAAK,QAAQ;AACb,SAAK,eAAe;AAUpB,WAAO,iBAAiB,MAAM;MAC5B,kBAAkB,EAAE,OAAO,KAAK;MAChC,eAAe,EAAE,OAAO,KAAK;IAC/B,CAAC;AACD,SAAK,KAAK,QAAQ;AAElB,UAAM,EAAE,QAAQ,YAAY,SAAS,KAAK,IAAI;AAC9C,SAAK,SAAS,aAAa;AAC3B,SAAK,SAAS,gBAAgB,cAAc,0BAAa,MAAM;AAI/D,UAAM,aAAa,mBAAmB,OAAO,KAAK;AAElD,QAAI,YAAY;AACd,WAAK,SAAS,UAAU,CAAC;AAEzB,iBAAW,QAAQ,CAAC,aAAa,eAAe;AAI9C,aAAK,SAAS,WAAW,KAAK,YAAY,WAAW;AAErD,cAAM,wBAAwB,WAAW,YAAY;AACrD,cAAM,cAAc,KAAK,SAAS,QAAQ,qBAAqB;AAC/D,aAAK,SAAS,QAAQ,qBAAqB,IAAI,cAC3C,MAAM,UAAU,OAAO,CAAC,GAAG,aAAa,WAAW,IACnD;MACN,CAAC;IACH;AACA,SAAK,OAAO,KAAK,kCAAkC,OAAO;AAY1D,SAAK,MAAM,KAAK;AAChB,SAAK,KAAK,YAAY,KAAK,QAAQ;AAEnC,UAAM,2BAA2B,IAAI,gBAAsB;AAE3D,UAAM,uBAAuB,MAAM;AACjC,WAAK,OAAO,KAAK,2BAA2B;AAI5C,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,SAAS,WAAW;AAEzB,+BAAyB,QAAQ;IACnC;AAEA,QAAI,MAAM;AACR,YAAM,aAAa,KAAK,UAAU;AAClC,YAAM,gBAAgB,YAA2B;AAC/C,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,WAAW,KAAK;AAE9C,YAAI,MAAM;AACR,+BAAqB;AACrB;QACF;AAEA,aAAK,SAAS,KAAK,QAAQ,KAAK;AAEhC,eAAO,cAAc;MACvB;AAEA,oBAAc;IAChB,OAAO;AACL,2BAAqB;IACvB;AAEA,6BAAyB,KAAK,MAAM;AAClC,WAAK,OAAO,KAAK,wBAAwB;AACzC,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,UAAU;AAEf,WAAK,OAAO,KAAK,mBAAmB;IACtC,CAAC;EACH;EAEQ,UAAUE,QAAoB;AACpC,SAAK,YAAY;AACjB,SAAK,KAAK,SAASA,MAAK;AACxB,SAAK,UAAU;EACjB;;;;EAKQ,YAAkB;AA1nB5B,QAAAE;AAkoBI,KAAAA,MAAA,KAAK,UAAL,OAAA,SAAAA,IAAY,QAAA;EACd;AACF;AAplBO,IAAM,oBAAN;AAAM,kBAKJ,qBAAqB;EAC1B;EACA;EACA;EACA;EACA;EACA;AACF;AWrDK,SAAS,uBAAuB,KAA0B;AAC/D,QAAM,UAA0B;IAC9B,QAAQ;IACR,UAAU,IAAI;IACd,UACE,OAAO,IAAI,aAAa,YAAY,IAAI,SAAS,WAAW,GAAG,IAC3D,IAAI,SAAS,MAAM,GAAG,EAAE,IACxB,IAAI;IACV,MAAM,IAAI;IACV,MAAM,GAAG,IAAI,QAAA,GAAW,IAAI,UAAU,EAAA;EACxC;AAEA,MAAI,CAAC,CAAC,IAAI,MAAM;AACd,YAAQ,OAAO,OAAO,IAAI,IAAI;EAChC;AAEA,MAAI,IAAI,YAAY,IAAI,UAAU;AAChC,YAAQ,OAAO,GAAG,IAAI,QAAA,IAAY,IAAI,QAAA;EACxC;AAEA,SAAO;AACT;ACxBA,IAAMN,UAAS,IAAIC,OAAO,8BAA8B;AAWjD,IAAM,eAAe;AAC5B,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,WAAW;AAEjB,SAAS,SACP,SACgC;AAChC,SAAO,QAAQ,iBAAiB,qBAAQ,QAAQ,QAAQ;AAC1D;AAEA,SAAS,4BAA4B,SAAyC;AA1B9E,MAAAK;AA2BE,MAAI,QAAQ,UAAU;AACpB,WAAO,QAAQ;EACjB;AAEA,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,gBAAiB,SAAA,OAAA,SAAA,MAA0B;AAEjD,MAAI,eAAe;AACjB,WAAO;EACT;AAEA,QAAM,OAAO,wBAAwB,OAAO;AAC5C,QAAM,kBAAkB,QAAQ,QAAQ,SAAS;AAEjD,SAAO,kBAAkB,aAAWA,MAAA,QAAQ,QAAR,OAAA,SAAAA,IAAa,aAAY;AAC/D;AAEA,SAAS,wBACP,SACoB;AAEpB,MAAI,QAAQ,MAAM;AAChB,WAAO,OAAO,QAAQ,IAAI;EAC5B;AAGA,QAAM,QAAQ,SAAS,OAAO;AAE9B,MAAK,SAAA,OAAA,SAAA,MAAsB,QAAQ,MAAM;AACvC,WAAO,OAAQ,MAAqB,QAAQ,IAAI;EAClD;AAEA,MAAK,SAAA,OAAA,SAAA,MAA0B,aAAa;AAC1C,WAAO,OAAQ,MAAyB,WAAW;EACrD;AAIA,SAAO;AACT;AAOA,SAAS,wBACP,SACyB;AACzB,MAAI,QAAQ,MAAM;AAChB,UAAM,CAAC,UAAU,QAAQ,IAAI,QAAQ,KAAK,MAAM,GAAG;AACnD,WAAO,EAAE,UAAU,SAAS;EAC9B;AACF;AAOA,SAAS,iBAAiB,MAAuB;AAC/C,SAAO,KAAK,SAAS,GAAG,KAAK,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,SAAS,GAAG;AAC1E;AAEA,SAAS,YAAY,SAAqD;AACxE,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAEvC,MAAI,MAAM;AACR,QAAI,iBAAiB,IAAI,GAAG;AACzB,aAAO,IAAI,IAAA;IACd;AAIA,WAAO,IAAI,IAAI,UAAU,IAAA,EAAM,EAAE;EACnC;AAEA,SAAO;AACT;AAKO,SAAS,uBAAuB,SAAsC;AAC3EN,UAAO,KAAK,mBAAmB,OAAO;AAEtC,MAAI,QAAQ,KAAK;AACfA,YAAO;MACL;MACA,QAAQ;IACV;AACA,WAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;EACjC;AAEAA,UAAO,KAAK,0CAA0C;AAEtD,QAAM,WAAW,4BAA4B,OAAO;AACpDA,UAAO,KAAK,YAAY,QAAQ;AAEhC,QAAM,OAAO,wBAAwB,OAAO;AAC5CA,UAAO,KAAK,QAAQ,IAAI;AAExB,QAAM,WAAW,YAAY,OAAO;AACpCA,UAAO,KAAK,YAAY,QAAQ;AAEhC,QAAM,OAAO,QAAQ,QAAQ;AAC7BA,UAAO,KAAK,QAAQ,IAAI;AAExB,QAAM,cAAc,wBAAwB,OAAO;AACnDA,UAAO,KAAK,eAAe,WAAW;AAEtC,QAAM,aAAa,cACf,GAAG,YAAY,QAAA,IAAY,YAAY,QAAA,MACvC;AACJA,UAAO,KAAK,gBAAgB,UAAU;AAEtC,QAAM,aAAa,OAAO,SAAS,cAAc,IAAI,IAAA,KAAS;AAC9D,QAAM,MAAM,IAAI,IAAI,GAAG,QAAA,KAAa,QAAA,GAAW,UAAA,GAAa,IAAA,EAAM;AAClE,MAAI,YAAW,eAAA,OAAA,SAAA,YAAa,aAAY;AACxC,MAAI,YAAW,eAAA,OAAA,SAAA,YAAa,aAAY;AAExCA,UAAO,KAAK,gBAAgB,GAAG;AAE/B,SAAO;AACT;ACrJA,IAAMA,UAAS,IAAIC,OAAO,aAAa;AAEvC,SAAS,cAAc,KAAoC;AAJ3D,MAAAK;AAKEN,UAAO,KAAK,oBAAoB,GAAG;AAEnC,MAAI,OAAO,QAAQ,GAACM,MAAA,IAAI,gBAAJ,OAAA,SAAAA,IAAiB,OAAM;AACzCN,YAAO,KAAK,kDAAkD;AAC9D,WAAO;EACT;AAEAA,UAAO,KAAK,oCAAoC,IAAI,YAAY,IAAI;AACpE,SAAO,IAAI,YAAY,SAAS;AAClC;AAEO,SAAS,YACd,KACY;AACZA,UAAO,KAAK,mBAAmB,GAAG;AAElC,QAAM,uBAAuB,OAAO,QAAQ,GAAG,EAAE;IAC/C,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrBA,cAAO,KAAK,6BAA6B,KAAK,KAAK;AAGnD,UAAI,GAAG,IAAI,cAAc,KAAK,IAAI,YAAY,KAAK,IAAI;AACvD,aAAO;IACT;IACA,CAAC;EACH;AAEA,SAAO,cAAc,GAAG,IACpB,uBACA,OAAO,OAAO,OAAO,eAAe,GAAG,GAAG,oBAAoB;AACpE;AHfA,IAAMA,UAAS,IAAIC,OAAO,iCAAiC;AAW3D,SAAS,sBACP,MACA,KACgB;AAGhB,MAAI,OAAO,KAAK,CAAC,MAAM,eAAe,OAAO,KAAK,CAAC,MAAM,YAAY;AACnED,YAAO,KAAK,uDAAuD,GAAG;AACtE,WAAO,uBAAuB,GAAG;EACnC;AAEA,MAAI,KAAK,CAAC,GAAG;AACXA,YAAO,KAAK,8BAA8B,KAAK,CAAC,CAAC;AACjD,UAAM,wBAAwB,uBAAuB,GAAG;AAExDA,YAAO,KAAK,wCAAwC,qBAAqB;AAOzEA,YAAO,KAAK,2BAA2B;AACvC,UAAM,uBAAuB,YAAY,KAAK,CAAC,CAAC;AAChDA,YAAO,KAAK,uCAAuC,oBAAoB;AAEvE,WAAO;MACL,GAAG;MACH,GAAG;IACL;EACF;AAEAA,UAAO,KAAK,0CAA0C;AACtD,SAAO,CAAC;AACV;AAOA,SAAS,4BAA4B,KAAU,SAA8B;AAC3E,MAAI,OAAO,QAAQ,QAAQ,IAAI;AAC/B,MAAI,WAAW,QAAQ,YAAY,IAAI;AACvC,MAAI,OAAO,QAAQ,OAAO,QAAQ,KAAK,SAAS,IAAI,IAAI;AAExD,MAAI,QAAQ,MAAM;AAChB,UAAM,wBAAoB,WAAAS,OAAS,QAAQ,MAAM,KAAK;AACtD,QAAI,WAAW,kBAAkB,YAAY;AAC7C,QAAI,SAAS,kBAAkB,UAAU;EAC3C;AAEA,SAAO;AACT;AAEA,SAAS,gBACP,MACiC;AACjC,SAAO,OAAO,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,IAAI,KAAK,CAAC;AACzD;AAYO,SAAS,2BACd,oBACG,MAC0B;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJT,UAAO,KAAK,aAAa,IAAI;AAC7BA,UAAO,KAAK,2BAA2B,eAAe;AAItD,MAAI,KAAK,WAAW,GAAG;AACrB,UAAMU,OAAM,IAAI,IAAI,kBAAkB;AACtC,UAAMC,WAAU,sBAAsB,MAAMD,IAAG;AAC/C,WAAO,CAACA,MAAKC,QAAO;EACtB;AAIA,MAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/BX,YAAO,KAAK,wCAAwC,KAAK,CAAC,CAAC;AAE3D,UAAM,IAAI,IAAI,KAAK,CAAC,CAAC;AACrBA,YAAO,KAAK,kBAAkB,GAAG;AAEjC,UAAM,wBAAwB,uBAAuB,GAAG;AACxDA,YAAO,KAAK,6BAA6B,qBAAqB;AAE9D,cAAU,sBAAsB,MAAM,GAAG;AACzCA,YAAO,KAAK,6BAA6B,OAAO;AAEhD,eAAW,gBAAgB,IAAI;EACjC,WAGS,KAAK,CAAC,aAAa,KAAK;AAC/B,UAAM,KAAK,CAAC;AACZA,YAAO,KAAK,4BAA4B,GAAG;AAO3C,QAAI,OAAO,KAAK,CAAC,MAAM,eAAe,SAAyB,KAAK,CAAC,CAAC,GAAG;AACvE,YAAM,4BAA4B,KAAK,KAAK,CAAC,CAAC;IAChD;AAEA,cAAU,sBAAsB,MAAM,GAAG;AACzCA,YAAO,KAAK,4BAA4B,OAAO;AAE/C,eAAW,gBAAgB,IAAI;EACjC,WAGS,UAAU,KAAK,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,IAAI;AACpD,UAAM,CAAC,SAAS,IAAI;AACpBA,YAAO,KAAK,mCAAmC,SAAS;AAExD,QAAI,UAAU,aAAa,MAAM;AAQ/BA,cAAO,KAAK,4CAA4C;AAExD,aAAO,SAAS,KAAK,CAAC,CAAC,IACnB;QACE;QACA,EAAE,MAAM,UAAU,MAAM,GAAG,KAAK,CAAC,EAAE;QACnC,KAAK,CAAC;MACR,IACA;QACE;QACA,EAAE,MAAM,UAAU,KAAK;QACvB,KAAK,CAAC;MACR;IACN;AAEAA,YAAO,KAAK,8BAA8B;AAG1C,UAAM,cAAc,IAAI,IAAI,UAAU,IAAI;AAE1C,WAAO,KAAK,CAAC,MAAM,SACf,2BAA2B,iBAAiB,WAAW,IACvD,OAAO,KAAK,CAAC,MAAM,aACnB,2BAA2B,iBAAiB,aAAa,KAAK,CAAC,CAAC,IAChE;MACE;MACA;MACA,KAAK,CAAC;MACN,KAAK,CAAC;IACR;EACN,WAGS,SAAS,KAAK,CAAC,CAAC,GAAG;AAC1B,cAAU,KAAK,CAAC;AAChBA,YAAO,KAAK,qCAAqC,OAAO;AAIxD,YAAQ,WAAW,QAAQ,YAAY;AACvCA,YAAO,KAAK,+BAA+B,OAAO;AAElD,UAAM,uBAAuB,OAAO;AACpCA,YAAO,KAAK,sCAAsC,IAAI,IAAI;AAE1D,eAAW,gBAAgB,IAAI;EACjC,OAAO;AACL,UAAM,IAAI;MACR,4DAA4D,IAAA;IAC9D;EACF;AAEA,UAAQ,WAAW,QAAQ,YAAY,IAAI;AAC3C,UAAQ,SAAS,QAAQ,UAAU;AAUnC,MAAI,OAAO,QAAQ,UAAU,aAAa;AACxC,UAAM,QACJ,QAAQ,aAAa,WACjB,IAAI,cAAAY,MAAW;MACb,oBAAoB,QAAQ;IAC9B,CAAC,IACD,IAAI,aAAAC,MAAU;AAEpB,YAAQ,QAAQ;AAChBb,YAAO,KAAK,4BAA4B,KAAK;EAC/C;AAUA,MAAI,CAAC,QAAQ,eAAe;AAC1BA,YAAO;MACL;MACA,QAAQ;IACV;AAEA,YAAQ,gBACN,QAAQ,aAAa,WAAW,cAAAc,cAAmB,aAAAC;EACvD;AAEAf,UAAO,KAAK,8BAA8B,IAAI,IAAI;AAClDA,UAAO,KAAK,kCAAkC,OAAO;AACrDA,UAAO,KAAK,mCAAmC,QAAQ;AAEvD,SAAO,CAAC,KAAK,SAAS,QAAQ;AAChC;AIlQO,SAAS,IAAI,UAAoB,SAA4B;AAClE,SAAO,SAAS,uBACX,MACY;AACf,UAAM,oBAAoB;MACxB,GAAG,QAAA;MACH,GAAG;IACL;AACA,UAAMgB,WAAU,IAAI,kBAAkB,mBAAmB,OAAO;AAMhEA,aAAQ,IAAI;AAEZ,WAAOA;EACT;AACF;ACjBA,IAAMhB,UAAS,IAAIC,OAAO,cAAc;AAEjC,SAAS,QAAQ,UAAoB,SAA4B;AACtE,SAAO,SAAS,2BACX,MACY;AACfD,YAAO,KAAK,iCAAiC,UAAU,IAAI;AAE3D,UAAM,oBAAoB;MACxB,GAAG,QAAA;MACH,GAAG;IACL;AACA,WAAO,IAAI,kBAAkB,mBAAmB,OAAO;EACzD;AACF;AhBTO,IAAM,4BAAN,cAAuC,YAAiC;EAI7E,cAAc;AACZ,UAAM,0BAAyB,iBAAiB;AAEhD,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,QAAQ,IAAI,QAAQ,aAAAiB,OAAI;AAC7B,SAAK,QAAQ,IAAI,SAAS,aAAAC,OAAK;EACjC;EAEU,QAAc;AACtB,UAAMlB,UAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,eAAW,CAAC,UAAU,aAAa,KAAK,KAAK,SAAS;AACpD,YAAM,EAAE,SAAS,aAAa,KAAK,QAAQ,IAAI;AAE/C,WAAK,cAAc,KAAK,MAAM;AAC5B,sBAAc,UAAU;AACxB,sBAAc,MAAM;AAEpBA,gBAAO,KAAK,gCAAgC,QAAQ;MACtD,CAAC;AAED,YAAM,UAA6B;QACjC,SAAS,KAAK;QACd,QAAQ,KAAK;MACf;AAGA,oBAAc;MAEZ,QAAQ,UAAU,OAAO;AAG3B,oBAAc;MAEZ,IAAI,UAAU,OAAO;AAEvBA,cAAO,KAAK,+BAA+B,QAAQ;IACrD;EACF;AACF;AA3CO,IAAM,2BAAN;AAAM,yBACJ,oBAAoB,OAAO,MAAM;;;AoBfnC,SAAS,kBACd,MACA,OACY;AACZ,QAAM,SAAS,IAAI,WAAW,KAAK,aAAa,MAAM,UAAU;AAChE,SAAO,IAAI,MAAM,CAAC;AAClB,SAAO,IAAI,OAAO,KAAK,UAAU;AACjC,SAAO;AACT;ACXO,IAAM,gBAAN,MAAqC;EAwB1C,YACE,MACA,SACA;AA1BF,SAAS,YAAoB;AAC7B,SAAS,iBAAyB;AAClC,SAAS,kBAA0B;AACnC,SAAS,OAAe;AAExB,SAAO,OAAe;AACtB,SAAO,aAAiC;AAExC,SAAO,gBAAoC;AAC3C,SAAO,aAAqB;AAE5B,SAAO,YAAqB;AAC5B,SAAO,WAAoB;AAC3B,SAAO,aAAsB;AAC7B,SAAO,mBAA4B;AACnC,SAAO,UAAmB;AAC1B,SAAO,mBAA4B;AACnC,SAAO,SAAiB;AACxB,SAAO,QAAgB;AAEvB,SAAA,eAAwB;AACxB,SAAA,cAAuB;AAMrB,SAAK,OAAO;AACZ,SAAK,UAAS,WAAA,OAAA,SAAA,QAAS,WAAU;AACjC,SAAK,iBAAgB,WAAA,OAAA,SAAA,QAAS,kBAAiB;AAC/C,SAAK,YAAY,KAAK,IAAI;EAC5B;EAEO,eAA8B;AACnC,WAAO,CAAC;EACV;EAEO,UAAU,MAAc,SAAmB,YAAsB;AACtE,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC,CAAC;AACjB,SAAK,aAAa,CAAC,CAAC;EACtB;EAEO,iBAAiB;AACtB,SAAK,mBAAmB;EAC1B;EAEO,kBAAkB;EAAC;EACnB,2BAA2B;EAAC;AACrC;AChDO,IAAM,wBAAN,cAAoC,cAAc;EAMvD,YAAY,MAAc,MAA0B;AAClD,UAAM,IAAI;AAEV,SAAK,oBAAmB,QAAA,OAAA,SAAA,KAAM,qBAAoB;AAClD,SAAK,YAAW,QAAA,OAAA,SAAA,KAAM,aAAY;AAClC,SAAK,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU;AAC9B,SAAK,SAAQ,QAAA,OAAA,SAAA,KAAM,UAAS;EAC9B;AACF;ACbA,IAAM,0BAA0B,OAAO,kBAAkB;AAElD,SAAS,YACd,QACA,MACA,MACe;AACf,QAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AAMA,QAAM,qBAAqB,0BACvB,gBACA;AAEJ,QAAM,QAAQ,eAAe,SAAS,IAAI,IACtC,IAAI,mBAAmB,MAAM;IAC3B,kBAAkB;IAClB,SAAQ,QAAA,OAAA,SAAA,KAAM,WAAU;IACxB,QAAO,QAAA,OAAA,SAAA,KAAM,UAAS;EACxB,CAAC,IACD,IAAI,cAAc,MAAM;IACtB;IACA,eAAe;EACjB,CAAC;AAEL,SAAO;AACT;ACpCO,SAAS,mBACd,QACA,cACe;AACf,MAAI,EAAE,gBAAgB,SAAS;AAC7B,WAAO;EACT;AAEA,QAAM,cAAc,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY;AAC7E,MAAI,aAAa;AACf,WAAO;EACT;AAEA,QAAM,YAAY,QAAQ,eAAe,MAAM;AAC/C,SAAO,YAAY,mBAAmB,WAAW,YAAY,IAAI;AACnE;ACKO,SAAS,YACd,QACA,SACQ;AACR,QAAM,QAAQ,IAAI,MAAM,QAAQ,sBAAsB,OAAO,CAAC;AAE9D,SAAO;AACT;AAEA,SAAS,sBACP,SACiB;AACjB,QAAM,EAAE,iBAAiB,YAAY,aAAa,YAAY,IAAI;AAClE,QAAM,UAA2B,CAAC;AAElC,MAAI,OAAO,oBAAoB,aAAa;AAC1C,YAAQ,YAAY,SAAU,QAAQ,MAAM,WAAW;AACrD,YAAM,OAAO,QAAQ,UAAU,KAAK,MAAM,QAAe,MAAM,SAAS;AACxE,aAAO,gBAAgB,KAAK,WAAW,MAAM,IAAI;IACnD;EACF;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,WAAW;AACvD,UAAM,OAAO,MAAM;AACjB,YAAM,iBAAiB,mBAAmB,QAAQ,YAAY,KAAK;AACnE,YAAM,iBAAiB,QAAQ;QAC7B;QACA;MACF;AAGA,UAAI,QAAO,kBAAA,OAAA,SAAA,eAAgB,SAAQ,aAAa;AAC9C,uBAAe,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC;AAC5C,eAAO;MACT;AAGA,aAAO,QAAQ,eAAe,gBAAgB,cAAc;QAC1D,UAAU;QACV,YAAY;QACZ,cAAc;QACd,OAAO;MACT,CAAC;IACH;AAEA,QAAI,OAAO,gBAAgB,aAAa;AACtC,aAAO,YAAY,KAAK,QAAQ,CAAC,cAAc,SAAS,GAAG,IAAI;IACjE;AAEA,WAAO,KAAK;EACd;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,UAAU;AAItD,UAAM,OAAO,MAAM,OAAO,YAAmB;AAE7C,UAAM,QACJ,OAAO,gBAAgB,cACnB,YAAY,KAAK,QAAQ,CAAC,cAAc,QAAQ,GAAG,IAAI,IACvD,KAAK;AAEX,QAAI,OAAO,UAAU,YAAY;AAC/B,aAAO,IAAI,SAAqB;AAC9B,cAAMmB,QAAO,MAAM,KAAK,QAAQ,GAAG,IAAI;AAEvC,YAAI,OAAO,eAAe,aAAa;AACrC,iBAAO,WAAW,KAAK,QAAQ,CAAC,cAAqB,IAAI,GAAGA,KAAI;QAClE;AAEA,eAAOA,MAAK;MACd;IACF;AAEA,WAAO;EACT;AAEA,SAAO;AACT;ACvGO,SAAS,yBACd,MACgC;AAChC,QAAM,iBAAgD;IACpD;IACA;IACA;IACA;IACA;EACF;AACA,SAAO,eAAe,KAAK,CAAC,kBAAkB;AAC5C,WAAO,KAAK,WAAW,aAAa;EACtC,CAAC;AACH;ACTO,SAAS,UAAU,MAA8C;AACtE,MAAI;AACF,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,WAAO;EACT,SAAS,GAAP;AACA,WAAO;EACT;AACF;ACLO,SAASC,gBACdC,UACA,MACU;AASV,QAAM,qBAAqB,sBAAsBA,SAAQ,MAAM,IAAI,OAAO;AAE1E,SAAO,IAAI,SAAS,oBAAoB;IACtC,QAAQA,SAAQ;IAChB,YAAYA,SAAQ;IACpB,SAAS;MACPA,SAAQ,sBAAsB;IAChC;EACF,CAAC;AACH;AAEA,SAAS,sCAAsC,eAAgC;AAC7E,QAAM,UAAU,IAAI,QAAQ;AAE5B,QAAM,QAAQ,cAAc,MAAM,SAAS;AAC3C,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,KAAK,MAAM,IAAI;AACtB;IACF;AAEA,UAAM,CAAC,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,IAAI;AACxC,UAAM,QAAQ,MAAM,KAAK,IAAI;AAE7B,YAAQ,OAAO,MAAM,KAAK;EAC5B;AAEA,SAAO;AACT;AT5BA,IAAM,qBAAqB,OAAO,kBAAkB;AACpD,IAAMC,WAAU,cAAc;AAMvB,IAAM,2BAAN,MAA+B;EA2BpC,YAAqB,gBAAuCC,SAAgB;AAAvD,SAAA,iBAAA;AAAuC,SAAA,SAAAA;AAP5D,SAAQ,SAAiB;AACzB,SAAQ,MAAW;AAOjB,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,YAAY,gBAAgB;AACjC,SAAK,iBAAiB,IAAI,QAAQ;AAClC,SAAK,iBAAiB,IAAI,WAAW;AAErC,SAAK,UAAU,YAAY,gBAAgB;MACzC,aAAa,CAAC,CAAC,cAAc,SAAS,GAAG,WAAW;AAClD,gBAAQ,cAAc;UACpB,KAAK,aAAa;AAChB,kBAAM,YAAY,aAAa;cAC7B;YACF;AAOA,iBAAK,QAAQ,iBAAiB,WAAW,SAAgB;AAEzD,mBAAO,OAAO;UAChB;UAEA,SAAS;AACP,mBAAO,OAAO;UAChB;QACF;MACF;MACA,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAhFlD,YAAAC;AAiFQ,gBAAQ,YAAY;UAClB,KAAK,QAAQ;AACX,kBAAM,CAAC,QAAQ,GAAG,IAAI;AAEtB,gBAAI,OAAO,QAAQ,aAAa;AAC9B,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,MAAM;YACjC,OAAO;AACL,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,GAAG;YAC9B;AAEA,iBAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,MAAA,IAAU,KAAK,IAAI,IAAA,EAAM;AAClE,iBAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,IAAI;AAEnD,mBAAO,OAAO;UAChB;UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,WAAW,QAAQ,IAAI;AAK9B,iBAAK,cAAc,WAAW,QAAQ;AACtC,iBAAK,OAAO,KAAK,oBAAoB,WAAW,QAAQ;AAExD,mBAAO,OAAO;UAChB;UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,MAAM,KAAK,IAAI;AACtB,iBAAK,eAAe,IAAI,MAAM,KAAK;AAEnC,iBAAK,OAAO,KAAK,oBAAoB,MAAM,KAAK;AAEhD,mBAAO,OAAO;UAChB;UAEA,KAAK,QAAQ;AACX,kBAAM,CAAC,IAAI,IAAI;AAIf,gBAAI,QAAQ,MAAM;AAChB,mBAAK,cACH,OAAO,SAAS,WAAW,aAAa,IAAI,IAAI;YACpD;AAEA,iBAAK,QAAQ,iBAAiB,QAAQ,MAAM;AAC1C,kBAAI,OAAO,KAAK,eAAe,aAAa;AAI1C,sBAAM,gBAAgBJ;kBACpB,KAAK;;;;;;kBAML,KAAK,QAAQ;gBACf;AAGA,qBAAK,WAAW,KAAK,MAAM;kBACzB,UAAU;kBACV,kBAAkB,sBAAsB,KAAK;kBAC7C,SAAS;kBACT,WAAW,KAAK;gBAClB,CAAC;cACH;YACF,CAAC;AAGD,kBAAM,eAAe,KAAK,kBAAkB;AAC5C,kBAAM,uBACJI,MAAA,KAAK,cAAL,OAAA,SAAAA,IAAgB,KAAK,MAAM;cACzB,SAAS;cACT,WAAW,KAAK;YAClB,CAAA,MAAM,QAAQ,QAAQ;AAExB,+BAAmB,QAAQ,MAAM;AAI/B,kBAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,SAAS;AAClD,qBAAK,OAAO;kBACV;kBACA,KAAK,QAAQ;gBACf;AAWA,oBAAIF,UAAS;AACX,uBAAK,QAAQ;oBACX;oBACA,KAAK;kBACP;gBACF;AAEA,uBAAO,OAAO;cAChB;YACF,CAAC;AAED;UACF;UAEA,SAAS;AACP,mBAAO,OAAO;UAChB;QACF;MACF;IACF,CAAC;EACH;EAEQ,cACN,WACA,UACM;AACN,UAAM,aAAa,KAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAClD,UAAM,aAAa,WAAW,OAAO,QAAQ;AAC7C,SAAK,OAAO,IAAI,WAAW,UAAU;AAErC,SAAK,OAAO,KAAK,yBAAyB,WAAW,QAAQ;EAC/D;;;;;EAMO,YAAY,UAA0B;AAC3C,SAAK,OAAO;MACV;MACA,SAAS;MACT,SAAS;IACX;AAOA,WAAO,KAAK,SAAS,oBAAoB,IAAI;AAE7C,WAAO,KAAK,SAAS,UAAU,SAAS,MAAM;AAC9C,WAAO,KAAK,SAAS,cAAc,SAAS,UAAU;AACtD,WAAO,KAAK,SAAS,eAAe,KAAK,IAAI,IAAI;AAEjD,SAAK,QAAQ,oBAAoB,IAAI,MAAM,KAAK,QAAQ,mBAAmB;MACzE,OAAO,CAAC,GAAG,IAAI,SAAyB;AACtC,aAAK,OAAO,KAAK,qBAAqB,KAAK,CAAC,CAAC;AAE7C,YAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,eAAK,OAAO,KAAK,0CAA0C;AAG3D,iBAAO;QACT;AAEA,cAAM,cAAc,SAAS,QAAQ,IAAI,KAAK,CAAC,CAAC;AAChD,aAAK,OAAO;UACV;UACA,KAAK,CAAC;UACN;QACF;AAEA,eAAO;MACT;IACF,CAAC;AAED,SAAK,QAAQ,wBAAwB,IAAI;MACvC,KAAK,QAAQ;MACb;QACE,OAAO,MAAM;AACX,eAAK,OAAO,KAAK,uBAAuB;AAExC,cAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,iBAAK,OAAO,KAAK,kDAAkD;AAGnE,mBAAO;UACT;AAEA,gBAAM,cAAc,MAAM,KAAK,SAAS,QAAQ,QAAQ,CAAC;AACzD,gBAAM,aAAa,YAChB,IAAI,CAAC,CAAC,YAAY,WAAW,MAAM;AAClC,mBAAO,GAAG,UAAA,KAAe,WAAA;UAC3B,CAAC,EACA,KAAK,MAAM;AAEd,eAAK,OAAO,KAAK,oCAAoC,UAAU;AAE/D,iBAAO;QACT;MACF;IACF;AAGA,WAAO,iBAAiB,KAAK,SAAS;MACpC,UAAU;QACR,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;MACA,cAAc;QACZ,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;MACA,aAAa;QACX,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;IACF,CAAC;AAED,UAAM,0BAA0B,SAAS,QAAQ,IAAI,gBAAgB,IACjE,OAAO,SAAS,QAAQ,IAAI,gBAAgB,CAAC;;;;MAI7C;;AAEJ,SAAK,OAAO,KAAK,mCAAmC,uBAAuB;AAE3E,SAAK,QAAQ,aAAa;MACxB,QAAQ;MACR,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,QAAQ,gBAAgB;AAChD,SAAK,cAAc,KAAK,QAAQ,OAAO;AAEvC,UAAM,mBAAmB,MAAM;AAC7B,WAAK,OAAO,KAAK,mCAAmC;AAEpD,WAAK,cAAc,KAAK,QAAQ,IAAI;AAEpC,WAAK,QAAQ,QAAQ;QACnB,QAAQ,KAAK,eAAe;QAC5B,OAAO;MACT,CAAC;AAED,WAAK,QAAQ,WAAW;QACtB,QAAQ,KAAK,eAAe;QAC5B,OAAO;MACT,CAAC;IACH;AAEA,QAAI,SAAS,MAAM;AACjB,WAAK,OAAO,KAAK,wCAAwC;AAEzD,YAAM,SAAS,SAAS,KAAK,UAAU;AAEvC,YAAM,4BAA4B,YAAY;AAC5C,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAE1C,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,4BAA4B;AAC7C,2BAAiB;AACjB;QACF;AAEA,YAAI,OAAO;AACT,eAAK,OAAO,KAAK,6BAA6B,KAAK;AACnD,eAAK,iBAAiB,kBAAkB,KAAK,gBAAgB,KAAK;AAElE,eAAK,QAAQ,YAAY;YACvB,QAAQ,KAAK,eAAe;YAC5B,OAAO;UACT,CAAC;QACH;AAEA,kCAA0B;MAC5B;AAEA,gCAA0B;IAC5B,OAAO;AACL,uBAAiB;IACnB;EACF;EAEQ,uBAA+B;AACrC,WAAO,aAAa,KAAK,cAAc;EACzC;EAEA,IAAI,WAAoB;AACtB,SAAK,OAAO;MACV;MACA,KAAK,QAAQ;IACf;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;IACT;AAEA,YAAQ,KAAK,QAAQ,cAAc;MACjC,KAAK,QAAQ;AACX,cAAM,eAAe,UAAU,KAAK,qBAAqB,CAAC;AAC1D,aAAK,OAAO,KAAK,0BAA0B,YAAY;AAEvD,eAAO;MACT;MAEA,KAAK,eAAe;AAClB,cAAM,cAAc,cAAc,KAAK,cAAc;AACrD,aAAK,OAAO,KAAK,iCAAiC,WAAW;AAE7D,eAAO;MACT;MAEA,KAAK,QAAQ;AACX,cAAM,WACJ,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AACpD,cAAM,eAAe,IAAI,KAAK,CAAC,KAAK,qBAAqB,CAAC,GAAG;UAC3D,MAAM;QACR,CAAC;AAED,aAAK,OAAO;UACV;UACA;UACA;QACF;AAEA,eAAO;MACT;MAEA,SAAS;AACP,cAAM,eAAe,KAAK,qBAAqB;AAC/C,aAAK,OAAO;UACV;UACA,KAAK,QAAQ;UACb;QACF;AAEA,eAAO;MACT;IACF;EACF;EAEA,IAAI,eAAuB;AAMzB,IAAAG;MACE,KAAK,QAAQ,iBAAiB,MAAM,KAAK,QAAQ,iBAAiB;MAClE;IACF;AAEA,QACE,KAAK,QAAQ,eAAe,KAAK,QAAQ,WACzC,KAAK,QAAQ,eAAe,KAAK,QAAQ,MACzC;AACA,aAAO;IACT;AAEA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,SAAK,OAAO,KAAK,yBAAyB,YAAY;AAEtD,WAAO;EACT;EAEA,IAAI,cAA+B;AACjC,IAAAA;MACE,KAAK,QAAQ,iBAAiB,MAC5B,KAAK,QAAQ,iBAAiB;MAChC;IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;IACT;AAEA,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AAEtE,QAAI,OAAO,cAAc,aAAa;AACpC,cAAQ;QACN;MACF;AACA,aAAO;IACT;AAEA,QAAI,yBAAyB,WAAW,GAAG;AACzC,aAAO,IAAI,UAAU,EAAE;QACrB,KAAK,qBAAqB;QAC1B;MACF;IACF;AAEA,WAAO;EACT;EAEO,UAAUC,QAAoB;AACnC,SAAK,OAAO,KAAK,0BAA0B;AAE3C,SAAK,cAAc,KAAK,QAAQ,IAAI;AACpC,SAAK,QAAQ,OAAO;AACpB,SAAK,QAAQ,SAAS;EACxB;;;;EAKQ,cAAc,gBAA8B;AAClD,SAAK,OAAO;MACV;MACA,KAAK,QAAQ;MACb;IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,gBAAgB;AAC9C,WAAK,OAAO,KAAK,+CAA+C;AAChE;IACF;AAEA,WAAO,KAAK,SAAS,cAAc,cAAc;AAEjD,SAAK,OAAO,KAAK,yBAAyB,cAAc;AAExD,QAAI,mBAAmB,KAAK,QAAQ,QAAQ;AAC1C,WAAK,OAAO,KAAK,yCAAyC;AAE1D,WAAK,QAAQ,kBAAkB;IACjC;EACF;;;;EAKQ,QAIN,WAAsB,SAAmC;AACzD,UAAM,WAAW,KAAK,QAAQ,KAAK,SAAA,EAAW;AAC9C,UAAM,QAAQ,YAAY,KAAK,SAAS,WAAW,OAAO;AAE1D,SAAK,OAAO,KAAK,gBAAgB,WAAW,WAAW,EAAE;AAGzD,QAAI,OAAO,aAAa,YAAY;AAClC,WAAK,OAAO,KAAK,4CAA4C,SAAS;AACtE,eAAS,KAAK,KAAK,SAAS,KAAK;IACnC;AAGA,eAAW,CAAC,qBAAqB,SAAS,KAAK,KAAK,QAAQ;AAC1D,UAAI,wBAAwB,WAAW;AACrC,aAAK,OAAO;UACV;UACA,UAAU;UACV;QACF;AAEA,kBAAU,QAAQ,CAAC,aAAa,SAAS,KAAK,KAAK,SAAS,KAAK,CAAC;MACpE;IACF;EACF;;;;EAKO,oBAA6B;AAClC,SAAK,OAAO,KAAK,8CAA8C;AAE/D,UAAM,eAAe,IAAI,QAAQ,KAAK,IAAI,MAAM;MAC9C,QAAQ,KAAK;MACb,SAAS,KAAK;;;;MAId,aAAa,KAAK,QAAQ,kBAAkB,YAAY;MACxD,MAAM,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,MAAM,IACtC,OACC,KAAK;IACZ,CAAC;AAED,UAAM,eAAe,YAAY,aAAa,SAAS;MACrD,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAI1C,gBAAQ,YAAY;UAClB,KAAK;UACL,KAAK,OAAO;AACV,kBAAM,CAAC,YAAY,WAAW,IAAI;AAClC,iBAAK,QAAQ,iBAAiB,YAAY,WAAW;AACrD;UACF;UAEA,KAAK,UAAU;AACb,kBAAM,CAAC,UAAU,IAAI;AACrB,oBAAQ;cACN,oCAAoC,UAAA,sDAAgE,aAAa,MAAA,IAAU,aAAa,GAAA;YAC1I;AACA;UACF;QACF;AAEA,eAAO,OAAO;MAChB;IACF,CAAC;AACD,WAAO,cAAc,WAAW,YAAY;AAE5C,SAAK,OAAO,KAAK,6CAA6C,YAAY;AAE1E,WAAO;EACT;AACF;AAEA,SAAS,cAAc,KAAwB;AAQ7C,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO,IAAI,IAAI,GAAG;EACpB;AAEA,SAAO,IAAI,IAAI,IAAI,SAAS,GAAG,SAAS,IAAI;AAC9C;AAEA,SAAS,OACP,QACA,UACA,OACM;AACN,UAAQ,eAAe,QAAQ,UAAU;;IAEvC,UAAU;IACV,YAAY;IACZ;EACF,CAAC;AACH;AD9lBO,SAAS,0BAA0B;EACxC;EACA,QAAAH;AACF,GAA+B;AAC7B,QAAM,sBAAsB,IAAI,MAAM,WAAW,gBAAgB;IAC/D,UAAU,QAAQ,MAAM,WAAW;AACjC,MAAAA,QAAO,KAAK,gCAAgC;AAE5C,YAAM,kBAAkB,QAAQ;QAC9B;QACA;QACA;MACF;AASA,YAAM,uBAAuB,OAAO;QAClC,OAAO;MACT;AACA,iBAAW,gBAAgB,sBAAsB;AAC/C,gBAAQ;UACN;UACA;UACA,qBAAqB,YAAY;QACnC;MACF;AAEA,YAAM,uBAAuB,IAAI;QAC/B;QACAA;MACF;AAEA,2BAAqB,YAAY,eAAgB,EAAE,SAAAF,UAAS,UAAU,GAAG;AACvE,cAAM,EAAE,oBAAoB,kBAAkB,IAC5C,qBAAqBA,QAAO;AAE9B,aAAK,OAAO,KAAK,6BAA6B;AAE9C,gBAAQ,KAAK,WAAW,CAAC,EAAE,WAAW,iBAAiB,MAAM;AAC3D,cAAI,qBAAqB,WAAW;AAClC;UACF;AAEA,cAAI,kBAAkB,gBAAgB,UAAU,WAAW;AACzD,8BAAkB,YAAY,MAAS;UACzC;QACF,CAAC;AAED,cAAM,iBAAiB,MAAM,MAAM,YAAY;AAC7C,eAAK,OAAO;YACV;YACA,QAAQ,cAAc,SAAS;UACjC;AAEA,gBAAM,UAAU,SAAS,WAAW;YAClC,SAAS;YACT;UACF,CAAC;AAED,eAAK,OAAO,KAAK,kCAAkC;AAEnD,gBAAMM,kBAAiB,MAAM,kBAAkB;AAE/C,eAAK,OAAO,KAAK,kCAAkCA,eAAc;AAEjE,iBAAOA;QACT,CAAC;AAED,YAAI,eAAe,OAAO;AACxB,eAAK,OAAO;YACV;YACA,eAAe;UACjB;AAGA,cAAI,eAAe,iBAAiB,UAAU;AAC5C,gBAAI,gBAAgB,eAAe,KAAK,GAAG;AACzC,mCAAqB,UAAU,IAAI,UAAU,eAAe,CAAC;YAC/D,OAAO;AACL,mBAAK,YAAY,eAAe,KAAK;YACvC;AAEA;UACF;AAEA,cAAI,QAAQ,cAAc,oBAAoB,IAAI,GAAG;AAGnD,kBAAM,UAAU,SAAS,sBAAsB;cAC7C,OAAO,eAAe;cACtB,SAAAN;cACA;cACA,YAAY;gBACV,aACE,qBAAqB,YAAY,KAAK,oBAAoB;gBAC5D,WACE,qBAAqB,UAAU,KAAK,oBAAoB;cAC5D;YACF,CAAC;AAMD,gBAAI,gBAAgB,aAAa,eAAe,QAAQ;AACtD;YACF;UACF;AAKA,+BAAqB;YACnB,0BAA0B,eAAe,KAAK;UAChD;AAEA;QACF;AAEA,cAAM,iBAAiB,eAAe;AAEtC,YAAI,OAAO,mBAAmB,aAAa;AACzC,eAAK,OAAO;YACV;YACA,eAAe;YACf,eAAe;UACjB;AAEA,cAAI,gBAAgB,cAAc,GAAG;AACnC,iBAAK,OAAO;cACV;YACF;AAEA,iCAAqB,UAAU,IAAI,UAAU,eAAe,CAAC;AAC7D;UACF;AAEA,iBAAO,qBAAqB,YAAY,cAAc;QACxD;AAEA,aAAK,OAAO;UACV;QACF;MACF;AAEA,2BAAqB,aAAa,eAAgB;QAChD;QACA;QACA,SAAAA;QACA;MACF,GAAG;AACD,aAAK,OAAO;UACV;UACA,QAAQ,cAAc,UAAU;QAClC;AAEA,gBAAQ,KAAK,YAAY;UACvB;UACA;UACA,SAAAA;UACA;QACF,CAAC;MACH;AAKA,aAAO,qBAAqB;IAC9B;EACF,CAAC;AAED,SAAO;AACT;ADxLO,IAAM,6BAAN,cAAwC,YAAiC;EAG9E,cAAc;AACZ,UAAM,2BAA0B,iBAAiB;EACnD;EAEU,mBAAmB;AAC3B,WAAO,OAAO,WAAW,mBAAmB;EAC9C;EAEU,QAAQ;AAChB,UAAME,UAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,IAAAA,QAAO,KAAK,qCAAqC;AAEjD,UAAM,qBAAqB,WAAW;AAEtCE,IAAAA;MACE,CAAE,mBAA2B,iBAAiB;MAC9C;IACF;AAEA,eAAW,iBAAiB,0BAA0B;MACpD,SAAS,KAAK;MACd,QAAQ,KAAK;IACf,CAAC;AAED,IAAAF,QAAO;MACL;MACA,WAAW,eAAe;IAC5B;AAEA,WAAO,eAAe,WAAW,gBAAgB,mBAAmB;MAClE,YAAY;MACZ,cAAc;MACd,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO,eAAe,WAAW,gBAAgB,mBAAmB;QAClE,OAAO;MACT,CAAC;AAED,iBAAW,iBAAiB;AAC5B,MAAAA,QAAO;QACL;QACA,WAAW,eAAe;MAC5B;IACF,CAAC;EACH;AACF;AAnDO,IAAM,4BAAN;AAAM,0BACJ,oBAAoB,OAAO,KAAK;;;AaVlC,SAAS,YAAY,KAAsB;AAChD,MAAI;AACF,QAAI,IAAI,GAAG;AACX,WAAO;EACT,SAAS,QAAP;AACA,WAAO;EACT;AACF;ADEO,IAAM,oBAAN,cAA+B,YAAiC;EAGrE,cAAc;AACZ,UAAM,kBAAiB,MAAM;EAC/B;EAEU,mBAAmB;AAC3B,WACE,OAAO,eAAe,eACtB,OAAO,WAAW,UAAU;EAEhC;EAEA,MAAgB,QAAQ;AACtB,UAAM,YAAY,WAAW;AAE7B,IAAAK;MACE,CAAE,UAAkB,iBAAiB;MACrC;IACF;AAEA,eAAW,QAAQ,OAAO,OAAO,SAAS;AApC9C,UAAAC;AAqCM,YAAM,YAAY,gBAAgB;AAQlC,YAAM,gBACJ,OAAO,UAAU,YACjB,OAAO,aAAa,eACpB,CAAC,YAAY,KAAK,IACd,IAAI,IAAI,OAAO,SAAS,MAAM,IAC9B;AAEN,YAAMC,WAAU,IAAI,QAAQ,eAAe,IAAI;AAE/C,WAAK,OAAO,KAAK,WAAWA,SAAQ,QAAQA,SAAQ,GAAG;AAEvD,YAAM,EAAE,oBAAoB,kBAAkB,IAC5C,qBAAqBA,QAAO;AAE9B,WAAK,OAAO;QACV;QACA,KAAK,QAAQ,cAAc,SAAS;MACtC;AAEA,WAAK,QAAQ,KAAK,WAAW,CAAC,EAAE,WAAW,iBAAiB,MAAM;AAChE,YAAI,qBAAqB,WAAW;AAClC;QACF;AAEA,YAAI,kBAAkB,gBAAgB,UAAU,WAAW;AACzD,4BAAkB,gBAAgB,QAAQ,MAAS;QACrD;MACF,CAAC;AAED,WAAK,OAAO,KAAK,qCAAqC;AAEtD,YAAM,SAAS,mBAAmB;AAClC,YAAM,iBAAiB,IAAI,gBAAgB;AAG3C,UAAI,QAAQ;AACV,eAAO;UACL;UACA,MAAM;AACJ,2BAAe,OAAO,OAAO,MAAM;UACrC;UACA,EAAE,MAAM,KAAK;QACf;MACF;AAEA,YAAM,kBAAkB,IAAI,gBAA0B;AAEtD,YAAM,cAAc,CAAC,aAA6B;AAChD,aAAK,OAAO,KAAK,oCAAoC,QAAQ;AAE7D,YAAI,KAAK,QAAQ,cAAc,UAAU,IAAI,GAAG;AAC9C,eAAK,OAAO,KAAK,kCAAkC;AAKnD,gBAAM,gBAAgB,SAAS,MAAM;AAErC,eAAK,QAAQ,KAAK,YAAY;YAC5B,UAAU;YACV,kBAAkB;YAClB,SAAS;YACT;UACF,CAAC;QACH;AAGA,eAAO,eAAe,UAAU,OAAO;UACrC,UAAU;UACV,YAAY;UACZ,cAAc;UACd,OAAOA,SAAQ;QACjB,CAAC;AAED,wBAAgB,QAAQ,QAAQ;MAClC;AAEA,YAAM,YAAY,CAAC,WAA0B;AAC3C,wBAAgB,OAAO,MAAM;MAC/B;AAEA,YAAM,iBAAiB,MAAM;QAC3B,YAAY;AACV,gBAAM,oBAAoB,UAAU,KAAK,SAAS,WAAW;YAC3D,SAAS;YACT;UACF,CAAC;AAED,gBAAM,QAAQ,KAAK;YACjB;;;;YAIA;YACA,kBAAkB;UACpB,CAAC;AAED,eAAK,OAAO,KAAK,2CAA2C;AAE5D,gBAAMC,kBAAiB,MAAM,kBAAkB;AAC/C,eAAK,OAAO,KAAK,kCAAkCA,eAAc;AAEjE,iBAAOA;QACT;MACF;AAEA,UAAI,eAAe,UAAU,YAAY;AACvC,aAAK,OAAO;UACV;UACA,eAAe;QACjB;AAEA,wBAAgB,OAAO,eAAe,eAAe;AACrD,eAAO;MACT;AAEA,UAAI,eAAe,OAAO;AACxB,aAAK,OAAO;UACV;UACA,eAAe;QACjB;AAGA,YAAI,eAAe,iBAAiB,UAAU;AAE5C,cAAI,gBAAgB,eAAe,KAAK,GAAG;AACzC,sBAAU,mBAAmB,eAAe,KAAK,CAAC;UACpD,OAAO;AAEL,wBAAY,eAAe,KAAK;UAClC;QACF;AAKA,YAAI,KAAK,QAAQ,cAAc,oBAAoB,IAAI,GAAG;AACxD,gBAAM,UAAU,KAAK,SAAS,sBAAsB;YAClD,OAAO,eAAe;YACtB,SAAAD;YACA;YACA,YAAY;cACV;cACA;YACF;UACF,CAAC;AAED,cAAI,gBAAgB,UAAU,WAAW;AACvC,mBAAO;UACT;QACF;AAKA,oBAAY,0BAA0B,eAAe,KAAK,CAAC;AAC3D,eAAO;MACT;AAEA,YAAM,iBAAiB,eAAe;AAEtC,UAAI,kBAAkB,GAACD,MAAAC,SAAQ,WAAR,OAAA,SAAAD,IAAgB,UAAS;AAC9C,aAAK,OAAO,KAAK,6BAA6B,cAAc;AAG5D,YAAI,gBAAgB,cAAc,GAAG;AACnC,eAAK,OAAO;YACV;UACF;AAUA,oBAAU,mBAAmB,cAAc,CAAC;QAC9C,OAAO;AACL,sBAAY,cAAc;QAC5B;AAEA,eAAO;MACT;AAEA,WAAK,OAAO,KAAK,8BAA8B;AAE/C,aAAO,UAAUC,QAAO,EAAE,KAAK,CAAC,aAAa;AAC3C,aAAK,OAAO,KAAK,4BAA4B,QAAQ;AAErD,YAAI,KAAK,QAAQ,cAAc,UAAU,IAAI,GAAG;AAC9C,eAAK,OAAO,KAAK,kCAAkC;AAEnD,gBAAM,gBAAgB,SAAS,MAAM;AAErC,eAAK,QAAQ,KAAK,YAAY;YAC5B,UAAU;YACV,kBAAkB;YAClB,SAAS;YACT;UACF,CAAC;QACH;AAEA,eAAO;MACT,CAAC;IACH;AAEA,WAAO,eAAe,WAAW,OAAO,mBAAmB;MACzD,YAAY;MACZ,cAAc;MACd,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO,eAAe,WAAW,OAAO,mBAAmB;QACzD,OAAO;MACT,CAAC;AAED,iBAAW,QAAQ;AAEnB,WAAK,OAAO;QACV;QACA,WAAW,MAAM;MACnB;IACF,CAAC;EACH;AACF;AAlQO,IAAM,mBAAN;AAAM,iBACJ,SAAS,OAAO,OAAO;AAmQhC,SAAS,mBAAmB,OAAgB;AAC1C,SAAO,OAAO,OAAO,IAAI,UAAU,iBAAiB,GAAG;IACrD;EACF,CAAC;AACH;;;AEnRO,SAASE,UAAS,OAAqB;AAC5C,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK;AAC3E;;;ACCO,SAAS,WACd,MACA,OACA;AACA,SAAO,OAAO,QAAQ,KAAK,EAAE;IAC3B,CAAC,QAAQ,CAAC,KAAK,UAAU,MAAM;AAC7B,YAAM,YAAY,OAAO,GAAG;AAE5B,UAAI,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,UAAU,GAAG;AACzD,eAAO,GAAG,IAAI,UAAU,OAAO,UAAU;AACzC,eAAO;MACT;AAEA,UAAIC,UAAS,SAAS,KAAKA,UAAS,UAAU,GAAG;AAC/C,eAAO,GAAG,IAAI,WAAW,WAAW,UAAU;AAC9C,eAAO;MACT;AAEA,aAAO,GAAG,IAAI;AACd,aAAO;IACT;IACA,OAAO,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;;;AvCTO,IAAM,yBAAsD;EACjE,oBAAoB;AACtB;AAEO,IAAM,uBAAN,cACG,SAEV;EAOE,YACE,cACA,UACA;AACA,UAAM,GAAG,QAAQ;AAVA;AAIX;AAQN,SAAK,cAAc,IAAI,iBAAiB;MACtC,MAAM;MACN,cAAc,aAAa,IAAI,CAACC,iBAAgB,IAAIA,aAAY,CAAC;IACnE,CAAC;AAED,SAAK,kBAAkB,CAAC;AAExB,SAAK,KAAK;EACZ;;;;EAKQ,OAAa;AACnB,SAAK,YAAY,GAAG,WAAW,OAAO,EAAE,SAAAC,UAAS,UAAU,MAAM;AAC/D,YAAM,WAAW,MAAM;QACrBA;QACA;QACA,KAAK,mBAAmB,gBAAgB;QACxC,KAAK;QACL,KAAK;MACP;AAEA,UAAI,UAAU;AACZ,QAAAA,SAAQ,YAAY,QAAQ;MAC9B;AAEA;IACF,CAAC;AAED,SAAK,YAAY,GAAG,sBAAsB,CAAC,EAAE,OAAAC,OAAM,MAAM;AACvD,UAAIA,kBAAiB,eAAe;AAClC,cAAMA;MACR;IACF,CAAC;AAED,SAAK,YAAY;MACf;MACA,CAAC,EAAE,UAAU,kBAAkB,SAAAD,UAAS,UAAU,MAAM;AACtD,aAAK,QAAQ;UACX,mBAAmB,oBAAoB;UACvC;YACE;YACA,SAAAA;YACA;UACF;QACF;MACF;IACF;EACF;EAEO,OAAO,UAAkC,CAAC,GAAS;AACxD,SAAK,kBAAkB;MACrB;MACA;IACF;AAGA,SAAK,YAAY,MAAM;AAEvB,SAAK,cAAc,KAAK,MAAM;AAC5B,WAAK,YAAY,QAAQ;IAC3B,CAAC;AAKD;MACE,CAAC,sBAAsB,UAAU,sBAAsB,OAAO,EAAE;QAC9D,KAAK,YAAY;MACnB;MACA,SAAS;QACP;MACF;MACA;IACF;EACF;EAEO,QAAc;AACnB,SAAK,QAAQ;EACf;AACF;ADhHA,IAAME,SAAQ,IAAI,0CAA0C;AAY5D,IAAM,0BAAN,MAA4D;EAG1D,YAAY,iBAAwC;AAF5C;AAGN,SAAK,cAAc,EAAE,iBAAiB,UAAU,CAAC,EAAE;EACrD;EAEA,IAAI,UAAkC;AACpC,WAAOA,OAAM,SAAS,KAAK,KAAK;EAClC;EAEO,QAAQ,iBAAwC;AACrD,SAAK,QAAQ,SAAS,QAAQ,GAAG,eAAe;EAClD;EAEO,MAAM,cAAqC;AAChD,UAAM,UAAU,KAAK;AACrB,YAAQ,WAAW,CAAC;AACpB,YAAQ,kBACN,aAAa,SAAS,IAAI,eAAe,QAAQ;EACrD;EAEO,kBAAyC;AAC9C,UAAM,EAAE,iBAAiB,SAAS,IAAI,KAAK;AAC3C,WAAO,SAAS,OAAO,eAAe;EACxC;AACF;AAEO,IAAM,iBAAN,cACG,qBAEV;EACE,YAAY,UAAiC;AAC3C;MACE,CAAC,0BAA0B,2BAA2B,gBAAgB;MACtE;IACF;AAEA,SAAK,qBAAqB,IAAI,wBAAwB,QAAQ;EAChE;EAEO,SACL,UACsB;AACtB,WAAO,IAAI,SAAkB;AAC3B,aAAOA,OAAM;QACX;UACE,iBAAiB,KAAK,mBAAmB,gBAAgB;UACzD,UAAU,CAAC;QACb;QACA;QACA,GAAG;MACL;IACF;EACF;EAEO,QAAc;AACnB,UAAM,MAAM;AACZ,IAAAA,OAAM,QAAQ;EAChB;AACF;AExEO,IAAM,cAAc,IACtB,aACgB;AACnB,SAAO,IAAI,eAAe,QAAQ;AACpC;;;AuCVO,IAAM,iBAAN,MAAqB;AAAA,EAQ1B,YAAY,KAAa;AALzB,2BAA0C,CAAC;AAC3C,4BAAwB,CAAC;AAKvB,UAAM,mBAAmB,MAAM,KAAK;AAEpC,SAAK,SAAS;AAAA,MACZ,KAAK,KAAK,KAAK,CAAC,EAAE,SAAAC,SAAQ,MAAM;AAC9B,aAAK,UAAUA;AAEf,eAAO,aAAa,KAAK,iBAAiB,GAAG;AAAA,UAC3C,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,GAAG,KAAK;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB;AACzB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,KAAK,MAAM,MAAM,KAAK,QAAS,KAAK,CAAC;AAAA,EAC9C;AAAA,EAEA,MAAM,oBAAoB;AACxB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,UAAM,iBAAiB,KAAK,QAAS;AAGrC,UAAM,gBAAwC,CAAC;AAC/C,mBAAe,QAAQ,CAAC,OAAO,QAAQ;AACrC,oBAAc,GAAG,IAAI;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,4BAA4B;AAChC,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,IAAI,IAAI,KAAK,QAAS,GAAG,EAAE;AAAA,EACpC;AAAA,EAEA,MAAM,gBAAgB;AACpB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,IAAI,IAAI,KAAK,QAAS,GAAG,EAAE,SAAS;AAAA,EAC7C;AAAA,EAEA,uBAAuB;AACrB,cAAU,MAAM,KAAK,OAAO,OAAO,CAAC;AACpC,eAAW,MAAM;AACf,WAAK,mBAAmB,CAAC;AACzB,WAAK,UAAU;AAAA,IACjB,CAAC;AACD,cAAU,MAAM,KAAK,OAAO,cAAc,CAAC;AAC3C,aAAS,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,EACpC;AACF;;;AC9DO,IAAM,sBAAN,MAA0B;AAAA,EAQ/B,YAAY,KAAa;AALzB,2BAA0C,CAAC;AAC3C,0BAAwB,CAAC;AAKvB,UAAM,iBAAiB,MAAM,KAAK;AAElC,SAAK,SAAS;AAAA,MACZ,KAAK,KAAK,KAAK,CAAC,EAAE,SAAAC,SAAQ,MAAM;AAC9B,aAAK,UAAUA;AAEf,cAAMC,WAAU,IAAI,YAAY;AAChC,cAAM,SAAS,IAAI,eAAe;AAAA,UAChC,MAAM,MAAM,YAAY;AACtB,gBAAI;AACF,yBAAW,SAAS,eAAe,GAAG;AACpC,2BAAW,QAAQA,SAAQ,OAAO,KAAK,CAAC;AAAA,cAC1C;AAAA,YACF,UAAE;AACA,yBAAW,MAAM;AAAA,YACnB;AAAA,UACF;AAAA,QACF,CAAC;AAED,eAAO,IAAI,aAAa,QAAQ;AAAA,UAC9B,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,YAAY;AAAA,YACZ,GAAG,KAAK;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB;AACzB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,KAAK,MAAM,MAAM,KAAK,QAAS,KAAK,CAAC;AAAA,EAC9C;AAAA,EAEA,MAAM,oBAAoB;AACxB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,UAAM,iBAAiB,KAAK,QAAS;AAGrC,UAAM,gBAAwC,CAAC;AAC/C,mBAAe,QAAQ,CAAC,OAAO,QAAQ;AACrC,oBAAc,GAAG,IAAI;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,4BAA4B;AAChC,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,IAAI,IAAI,KAAK,QAAS,GAAG,EAAE;AAAA,EACpC;AAAA,EAEA,uBAAuB;AACrB,cAAU,MAAM,KAAK,OAAO,OAAO,CAAC;AACpC,eAAW,MAAM;AACf,WAAK,iBAAiB,CAAC;AACvB,WAAK,UAAU;AAAA,IACjB,CAAC;AACD,cAAU,MAAM,KAAK,OAAO,cAAc,CAAC;AAC3C,aAAS,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,EACpC;AACF;;;AC/CA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAoBC,UAAkB;AAAlB,mBAAAA;AAAA,EAAmB;AAAA,EAEvC,MAAM,qBAAqB;AACzB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,KAAK,MAAM,MAAM,KAAK,QAAS,KAAK,CAAC;AAAA,EAC9C;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,UAAM,iBAAiB,KAAK,QAAS;AAGrC,UAAM,gBAAwC,CAAC;AAC/C,mBAAe,QAAQ,CAAC,OAAO,QAAQ;AACrC,oBAAc,GAAG,IAAI;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,4BAA4B;AAC1B,WAAO,KAAK,OAAO,EAAE,YAAY;AACjC,WAAO,IAAI,IAAI,KAAK,QAAS,GAAG,EAAE;AAAA,EACpC;AACF;AAEA,SAAS,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF,GAOG;AAED,QAAM,iBAAiB,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AACxE,QAAM,iBAAiB,eAAe,OAAO,CAACC,iBAAgB,aAAa;AACzE,QAAI,CAACA,gBAAe,SAAS,GAAG,GAAG;AACjC,MAAAA,gBAAe,SAAS,GAAG,IAAI,CAAC;AAAA,IAClC;AACA,IAAAA,gBAAe,SAAS,GAAG,EAAE,KAAK,QAAQ;AAC1C,WAAOA;AAAA,EACT,GAAG,CAAC,CAA8C;AAGlD,QAAM,UAAU,CAAC;AACjB,iBACG;AAAA,IACC,CACE,aAEA,SAAS,SAAS;AAAA,EACtB,EACC,QAAQ,cAAY;AAvFzB,QAAAC,KAAAC;AAwFM,QAAI;AAEJ,UAAM,SAAS,IAAI,eAAuB;AAAA,MACxC,MAAM,YAAY;AAChB,2BAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AAED,oBAAeD,MAAA,SAAS,OAAT,OAAAA,MAAe,IAAI,MAAM,gBAAgB;AACxD,aAAQC,MAAA,SAAS,OAAT,OAAAA,MAAe,EAAE,IAAI;AAAA,EAC/B,CAAC;AAGH,QAAM,sBAAsB,OAAO;AAAA,IACjC,OAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAAA,EACxD;AAEA,SAAO;AAAA,IACL,GAAG,OAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,KAAKC,UAAS,MAAM;AAC1D,aAAO,KAAK,KAAK,KAAK,CAAC,EAAE,SAAAJ,SAAQ,MAAM;AA3G7C,YAAAE,KAAAC;AA4GQ,iBAAS,IAAI,eAAeH,QAAO,CAAC;AAEpC,cAAM,kBAAkB,oBAAoB,GAAG;AAC/C,cAAM,WACJI,WACE,kBAAkBA,WAAU,SACxBA,WAAU,SAAS,IACnB,eACN;AAEF,gBAAQ,SAAS,MAAM;AAAA,UACrB,KAAK;AACH,mBAAO,aAAa,KAAK,SAAS,SAAS;AAAA,cACzC,QAAQ;AAAA,cACR,SAAS;AAAA,gBACP,gBAAgB;AAAA,gBAChB,GAAG,SAAS;AAAA,cACd;AAAA,YACF,CAAC;AAAA,UAEH,KAAK;AACH,mBAAO,IAAI;AAAA,cACT,6BAA6B,SAAS,OAAO,EAAE;AAAA,gBAC7C,IAAI,kBAAkB;AAAA,cACxB;AAAA,cACA;AAAA,gBACE,QAAQ;AAAA,gBACR,SAAS;AAAA,kBACP,gBAAgB;AAAA,kBAChB,iBAAiB;AAAA,kBACjB,YAAY;AAAA,kBACZ,GAAG,SAAS;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UAEF,KAAK,qBAAqB;AACxB,mBAAO,IAAI;AAAA,cACT,SAAQF,MAAA,SAAS,OAAT,OAAAA,MAAe,EAAE,EAAE,YAAY,IAAI,kBAAkB,CAAC;AAAA,cAC9D;AAAA,gBACE,QAAQ;AAAA,gBACR,SAAS;AAAA,kBACP,gBAAgB;AAAA,kBAChB,iBAAiB;AAAA,kBACjB,YAAY;AAAA,kBACZ,GAAG,SAAS;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA,KAAK;AACH,mBAAO,aAAa,MAAKC,MAAA,SAAS,YAAT,OAAAA,MAAoB,SAAS;AAAA,cACpD,QAAQ,SAAS;AAAA,cACjB,SAAS;AAAA,gBACP,GAAG,SAAS;AAAA,cACd;AAAA,YACF,CAAC;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAEO,SAAS,eACd,WACA,cAQA;AACA,SAAO,YAAY;AACjB,UAAM,QAA+B,CAAC;AACtC,UAAM,cAGF,CAAC;AACL,UAAM,SAAS,aAAa;AAAA,MAC1B;AAAA,MACA,UAAU,UAAQ,MAAM,KAAK,IAAI;AAAA,MACjC,gBAAgB,CAAC,IAAI,eAAe;AAClC,oBAAY,EAAE,IAAI;AAAA,MACpB;AAAA,IACF,CAAC;AAED,QAAI;AACF,aAAO,OAAO;AAEd,YAAM,aAAa;AAAA,QACjB,OAAO,MAAM;AAAA,QACb,MAAM,CAAC,UAAkB,MAAM,KAAK;AAAA,QACpC,qBAAqB,CAAC,OAAe;AACnC,iBAAO,YAAY,EAAE,EAAE;AAAA,QACzB;AAAA,QACA,IAAI,mBAAmB;AACrB,iBAAO,YAAY,EAAE,EAAE;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,UAAE;AACA,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACF;AAEO,SAAS,uBACd,aACA,WACA,cAQA;AACA,WAAS,aAAa,MAAM;AAC1B,QAAI;AACJ,QAAI;AAIJ,QAAI;AAEJ,cAAU,MAAM;AACd,eAAS,aAAa;AAAA,QACpB;AAAA,QACA,UAAU,UAAQ,MAAM,KAAK,IAAI;AAAA,QACjC,gBAAgB,CAAC,IAAI,eAAe;AAClC,sBAAY,EAAE,IAAI;AAAA,QACpB;AAAA,MACF,CAAC;AACD,aAAO,OAAO;AAAA,IAChB,CAAC;AAED,eAAW,MAAM;AACf,cAAQ,CAAC;AACT,oBAAc,CAAC;AACf,aAAO,cAAc;AAAA,IACvB,CAAC;AAED,aAAS,MAAM;AACb,aAAO,MAAM;AAAA,IACf,CAAC;AAED,iBAAa;AAAA,MACX,OAAO,MAAM;AAAA,MACb,MAAM,CAAC,UAAkB,MAAM,KAAK;AAAA,MACpC,qBAAqB,CAAC,OAAe,YAAY,EAAE,EAAE;AAAA,MACrD,IAAI,mBAAmB;AACrB,eAAO,YAAY,EAAE,EAAE;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;ArH5PO,IAAM,uBAAuB;", "names": ["message", "match", "error", "formatMessage", "message", "sourceEmit", "error", "request", "_a", "_b", "request", "__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__copyProps", "__toESM", "exports", "module", "message", "_a", "value", "result", "_a", "i", "_b", "_c", "POSITIONALS_EXP", "serializePositional", "format", "message", "match", "STACK_FRAMES_TO_IGNORE", "cleanErrorStack", "error", "InvariantError", "invariant", "formatMessage", "__defProp", "__export", "message", "message2", "warn", "error", "format", "_a", "InterceptorReadyState", "logger", "_a", "logger", "cleanUrl", "__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__commonJS", "__copyProps", "__toESM", "exports", "parse", "source_default", "__commonJS", "exports", "module", "value", "parse", "cookies", "__toESM", "error", "isPropertyAccessible", "request", "parse<PERSON><PERSON><PERSON>", "source_default", "request", "_a", "HttpMethods", "_a", "match", "require_set_cookie", "__commonJS", "exports", "module", "value", "parse", "cookies", "splitCookiesString", "import_set_cookie_parser", "__toESM", "Headers", "_a", "error", "request", "request", "strategy", "request", "request", "_a", "_b", "_c", "message", "Headers", "_a", "request", "invariant", "import_http", "import_http", "import_https", "import_http", "logger", "<PERSON><PERSON>", "message", "symbol", "error", "IncomingMessage", "_a", "chunk", "encoding", "parseUrl", "url", "options", "HttpsAgent", "HttpAgent", "httpsGlobalAgent", "httpGlobalAgent", "request", "http", "https", "next", "createResponse", "request", "IS_NODE", "logger", "_a", "invariant", "error", "mockedResponse", "invariant", "_a", "request", "mockedResponse", "isObject", "isObject", "Interceptor", "request", "error", "store", "request", "request", "encoder", "request", "responsesByUrl", "_a", "_b", "responses"]}