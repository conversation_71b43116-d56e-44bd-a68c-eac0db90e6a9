import { LanguageModelV1FinishReason, JSONValue as JSONValue$1 } from '@ai-sdk/provider';
import { FetchFunction, ToolCall as ToolCall$1, Too<PERSON>R<PERSON><PERSON>, Validator } from '@ai-sdk/provider-utils';
export { generateId } from '@ai-sdk/provider-utils';
import { z } from 'zod';
import { JSONSchema7 } from 'json-schema';

/**
Represents the number of tokens used in a prompt and completion.
 */
type CompletionTokenUsage = {
    /**
  The number of tokens used in the prompt.
     */
    promptTokens: number;
    /**
  The number of tokens used in the completion.
   */
    completionTokens: number;
    /**
  The total number of tokens used (promptTokens + completionTokens).
     */
    totalTokens: number;
};

type AssistantStatus = 'in_progress' | 'awaiting_message';
type UseAssistantOptions = {
    /**
     * The API endpoint that accepts a `{ threadId: string | null; message: string; }` object and returns an `AssistantResponse` stream.
     * The threadId refers to an existing thread with messages (or is `null` to create a new thread).
     * The message is the next message that should be appended to the thread and sent to the assistant.
     */
    api: string;
    /**
     * An optional string that represents the ID of an existing thread.
     * If not provided, a new thread will be created.
     */
    threadId?: string;
    /**
     * An optional literal that sets the mode of credentials to be used on the request.
     * Defaults to "same-origin".
     */
    credentials?: RequestCredentials;
    /**
     * An optional object of headers to be passed to the API endpoint.
     */
    headers?: Record<string, string> | Headers;
    /**
     * An optional, additional body object to be passed to the API endpoint.
     */
    body?: object;
    /**
     * An optional callback that will be called when the assistant encounters an error.
     */
    onError?: (error: Error) => void;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
};

/**
 * @deprecated use AI SDK 3.1 CoreTool / ToolResult instead
 */
interface FunctionCall {
    /**
     * The arguments to call the function with, as generated by the model in JSON
     * format. Note that the model does not always generate valid JSON, and may
     * hallucinate parameters not defined by your function schema. Validate the
     * arguments in your code before calling your function.
     */
    arguments?: string;
    /**
     * The name of the function to call.
     */
    name?: string;
}
/**
 * @deprecated use AI SDK 3.1 CoreTool / ToolResult instead
 *
 * The tool calls generated by the model, such as function calls.
 */
interface ToolCall {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: string;
    /**
     * The function that the model called.
     */
    function: {
        /**
         * The name of the function.
         */
        name: string;
        /**
         * The arguments to call the function with, as generated by the model in JSON
         */
        arguments: string;
    };
}
/**
 * @deprecated use AI SDK 3.1 CoreTool / ToolChoice instead
 *
 * Controls which (if any) function is called by the model.
 * - none means the model will not call a function and instead generates a message.
 * - auto means the model can pick between generating a message or calling a function.
 * - Specifying a particular function via {"type: "function", "function": {"name": "my_function"}} forces the model to call that function.
 * none is the default when no functions are present. auto is the default if functions are present.
 */
type ToolChoice = 'none' | 'auto' | {
    type: 'function';
    function: {
        name: string;
    };
};
/**
 * @deprecated use AI SDK 3.1 CoreTool instead
 *
 * A list of tools the model may call. Currently, only functions are supported as a tool.
 * Use this to provide a list of functions the model may generate JSON inputs for.
 */
interface Tool {
    type: 'function';
    function: Function;
}
/**
 * @deprecated use AI SDK 3.1 CoreTool instead
 */
interface Function {
    /**
     * The name of the function to be called. Must be a-z, A-Z, 0-9, or contain
     * underscores and dashes, with a maximum length of 64.
     */
    name: string;
    /**
     * The parameters the functions accepts, described as a JSON Schema object. See the
     * [guide](/docs/guides/gpt/function-calling) for examples, and the
     * [JSON Schema reference](https://json-schema.org/understanding-json-schema/) for
     * documentation about the format.
     *
     * To describe a function that accepts no parameters, provide the value
     * `{"type": "object", "properties": {}}`.
     */
    parameters: Record<string, unknown>;
    /**
     * A description of what the function does, used by the model to choose when and
     * how to call the function.
     */
    description?: string;
}
type IdGenerator = () => string;
/**
Tool invocations are either tool calls or tool results. For each assistant tool call,
there is one tool invocation. While the call is in progress, the invocation is a tool call.
Once the call is complete, the invocation is a tool result.
 */
type ToolInvocation = ({
    state: 'partial-call';
} & ToolCall$1<string, any>) | ({
    state: 'call';
} & ToolCall$1<string, any>) | ({
    state: 'result';
} & ToolResult<string, any, any>);
/**
 * An attachment that can be sent along with a message.
 */
interface Attachment {
    /**
     * The name of the attachment, usually the file name.
     */
    name?: string;
    /**
     * A string indicating the [media type](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Type).
     * By default, it's extracted from the pathname's extension.
     */
    contentType?: string;
    /**
     * The URL of the attachment. It can either be a URL to a hosted file or a [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs).
     */
    url: string;
}
/**
 * AI SDK UI Messages. They are used in the client and to communicate between the frontend and the API routes.
 */
interface Message {
    /**
  A unique identifier for the message.
     */
    id: string;
    /**
  The timestamp of the message.
     */
    createdAt?: Date;
    /**
  Text content of the message.
     */
    content: string;
    /**
     * Additional attachments to be sent along with the message.
     */
    experimental_attachments?: Attachment[];
    /**
     * @deprecated Use AI SDK 3.1 `toolInvocations` instead.
     */
    tool_call_id?: string;
    /**
  @deprecated Use AI SDK RSC instead: https://sdk.vercel.ai/docs/ai-sdk-rsc
   */
    ui?: string | JSX.Element | JSX.Element[] | null | undefined;
    /**
     * `function` and `tool` roles are deprecated.
     */
    role: 'system' | 'user' | 'assistant' | 'function' | 'data' | 'tool';
    /**
     * @deprecated
     *
     * If the message has a role of `function`, the `name` field is the name of the function.
     * Otherwise, the name field should not be set.
     */
    name?: string;
    /**
     * @deprecated Use AI SDK 3.1 `toolInvocations` instead.
     *
     * If the assistant role makes a function call, the `function_call` field
     * contains the function call name and arguments. Otherwise, the field should
     * not be set. (Deprecated and replaced by tool_calls.)
     */
    function_call?: string | FunctionCall;
    data?: JSONValue;
    /**
     * @deprecated Use AI SDK 3.1 `toolInvocations` instead.
     *
     * If the assistant role makes a tool call, the `tool_calls` field contains
     * the tool call name and arguments. Otherwise, the field should not be set.
     */
    tool_calls?: string | ToolCall[];
    /**
     * Additional message-specific information added on the server via StreamData
     */
    annotations?: JSONValue[] | undefined;
    /**
  Tool invocations (that can be tool calls or tool results, depending on whether or not the invocation has finished)
  that the assistant made as part of this message.
     */
    toolInvocations?: Array<ToolInvocation>;
}
type CreateMessage = Omit<Message, 'id'> & {
    id?: Message['id'];
};
type ChatRequest = {
    /**
  An optional object of headers to be passed to the API endpoint.
   */
    headers?: Record<string, string> | Headers;
    /**
  An optional object to be passed to the API endpoint.
  */
    body?: object;
    /**
  The messages of the chat.
     */
    messages: Message[];
    /**
  Additional data to be sent to the server.
     */
    data?: JSONValue;
    /**
  The options to be passed to the fetch call.
  
  @deprecated use `headers` and `body` directly
     */
    options?: RequestOptions;
    /**
     * @deprecated
     */
    functions?: Array<Function>;
    /**
     * @deprecated
     */
    function_call?: FunctionCall;
    /**
     * @deprecated
     */
    tools?: Array<Tool>;
    /**
     * @deprecated
     */
    tool_choice?: ToolChoice;
};
/**
 * @deprecated Use AI SDK 3.1 `streamText` and `onToolCall` instead.
 */
type FunctionCallHandler = (chatMessages: Message[], functionCall: FunctionCall) => Promise<ChatRequest | void>;
/**
 * @deprecated Use AI SDK 3.1 `streamText` and `onToolCall` instead.
 */
type ToolCallHandler = (chatMessages: Message[], toolCalls: ToolCall[]) => Promise<ChatRequest | void>;
type RequestOptions = {
    /**
  An optional object of headers to be passed to the API endpoint.
   */
    headers?: Record<string, string> | Headers;
    /**
  An optional object to be passed to the API endpoint.
     */
    body?: object;
};
type ChatRequestOptions = {
    /**
  Additional headers that should be to be passed to the API endpoint.
   */
    headers?: Record<string, string> | Headers;
    /**
  Additional body JSON properties that should be sent to the API endpoint.
   */
    body?: object;
    /**
  Additional data to be sent to the API endpoint.
     */
    data?: JSONValue;
    /**
     * Additional files to be sent to the server.
     */
    experimental_attachments?: FileList | Array<Attachment>;
    /**
     * Allow submitting an empty message. Defaults to `false`.
     */
    allowEmptySubmit?: boolean;
    /**
  The options to be passed to the fetch call.
  
  @deprecated use `headers` and `body` directly
     */
    options?: RequestOptions;
    /**
  @deprecated
  */
    functions?: Array<Function>;
    /**
  @deprecated
  */
    function_call?: FunctionCall;
    /**
  @deprecated
  */
    tools?: Array<Tool>;
    /**
  @deprecated
  */
    tool_choice?: ToolChoice;
};
type UseChatOptions = {
    /**
  Keeps the last message when an error happens. This will be the default behavior
  starting with the next major release.
  The flag was introduced for backwards compatibility and currently defaults to `false`.
  Please enable it and update your error handling/resubmit behavior.
     */
    keepLastMessageOnError?: boolean;
    /**
     * The API endpoint that accepts a `{ messages: Message[] }` object and returns
     * a stream of tokens of the AI chat response. Defaults to `/api/chat`.
     */
    api?: string;
    /**
     * A unique identifier for the chat. If not provided, a random one will be
     * generated. When provided, the `useChat` hook with the same `id` will
     * have shared states across components.
     */
    id?: string;
    /**
     * Initial messages of the chat. Useful to load an existing chat history.
     */
    initialMessages?: Message[];
    /**
     * Initial input of the chat.
     */
    initialInput?: string;
    /**
     * @deprecated Use AI SDK 3.1 `streamText` and `onToolCall` instead.
     *
     * Callback function to be called when a function call is received.
     * If the function returns a `ChatRequest` object, the request will be sent
     * automatically to the API and will be used to update the chat.
     */
    experimental_onFunctionCall?: FunctionCallHandler;
    /**
     * @deprecated Use AI SDK 3.1 `streamText` and `onToolCall` instead.
     *
     * Callback function to be called when a tool call is received.
     * If the function returns a `ChatRequest` object, the request will be sent
     * automatically to the API and will be used to update the chat.
     */
    experimental_onToolCall?: ToolCallHandler;
    /**
  Optional callback function that is invoked when a tool call is received.
  Intended for automatic client-side tool execution.
  
  You can optionally return a result for the tool call,
  either synchronously or asynchronously.
     */
    onToolCall?: ({ toolCall, }: {
        toolCall: ToolCall$1<string, unknown>;
    }) => void | Promise<unknown> | unknown;
    /**
     * Callback function to be called when the API response is received.
     */
    onResponse?: (response: Response) => void | Promise<void>;
    /**
     * Optional callback function that is called when the assistant message is finished streaming.
     *
     * @param message The message that was streamed.
     * @param options.usage The token usage of the message.
     * @param options.finishReason The finish reason of the message.
     */
    onFinish?: (message: Message, options: {
        usage: CompletionTokenUsage;
        finishReason: LanguageModelV1FinishReason;
    }) => void;
    /**
     * Callback function to be called when an error is encountered.
     */
    onError?: (error: Error) => void;
    /**
     * A way to provide a function that is going to be used for ids for messages.
     * If not provided nanoid is used by default.
     */
    generateId?: IdGenerator;
    /**
     * The credentials mode to be used for the fetch request.
     * Possible values are: 'omit', 'same-origin', 'include'.
     * Defaults to 'same-origin'.
     */
    credentials?: RequestCredentials;
    /**
     * HTTP headers to be sent with the API request.
     */
    headers?: Record<string, string> | Headers;
    /**
     * Extra body object to be sent with the API request.
     * @example
     * Send a `sessionId` to the API along with the messages.
     * ```js
     * useChat({
     *   body: {
     *     sessionId: '123',
     *   }
     * })
     * ```
     */
    body?: object;
    /**
     * Whether to send extra message fields such as `message.id` and `message.createdAt` to the API.
     * Defaults to `false`. When set to `true`, the API endpoint might need to
     * handle the extra fields before forwarding the request to the AI service.
     */
    sendExtraMessageFields?: boolean;
    /**
     * Stream mode (default to "stream-data")
     *
     * @deprecated Use `streamProtocol` instead.
     */
    streamMode?: 'stream-data' | 'text';
    /**
  Streaming protocol that is used. Defaults to `data`.
     */
    streamProtocol?: 'data' | 'text';
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
};
type UseCompletionOptions = {
    /**
     * The API endpoint that accepts a `{ prompt: string }` object and returns
     * a stream of tokens of the AI completion response. Defaults to `/api/completion`.
     */
    api?: string;
    /**
     * An unique identifier for the chat. If not provided, a random one will be
     * generated. When provided, the `useChat` hook with the same `id` will
     * have shared states across components.
     */
    id?: string;
    /**
     * Initial prompt input of the completion.
     */
    initialInput?: string;
    /**
     * Initial completion result. Useful to load an existing history.
     */
    initialCompletion?: string;
    /**
     * Callback function to be called when the API response is received.
     */
    onResponse?: (response: Response) => void | Promise<void>;
    /**
     * Callback function to be called when the completion is finished streaming.
     */
    onFinish?: (prompt: string, completion: string) => void;
    /**
     * Callback function to be called when an error is encountered.
     */
    onError?: (error: Error) => void;
    /**
     * The credentials mode to be used for the fetch request.
     * Possible values are: 'omit', 'same-origin', 'include'.
     * Defaults to 'same-origin'.
     */
    credentials?: RequestCredentials;
    /**
     * HTTP headers to be sent with the API request.
     */
    headers?: Record<string, string> | Headers;
    /**
     * Extra body object to be sent with the API request.
     * @example
     * Send a `sessionId` to the API along with the prompt.
     * ```js
     * useChat({
     *   body: {
     *     sessionId: '123',
     *   }
     * })
     * ```
     */
    body?: object;
    /**
     * Stream mode (default to "stream-data")
     *
     * @deprecated Use `streamProtocol` instead.
     */
    streamMode?: 'stream-data' | 'text';
    /**
  Streaming protocol that is used. Defaults to `data`.
     */
    streamProtocol?: 'data' | 'text';
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
};
/**
A JSON value can be a string, number, boolean, object, array, or null.
JSON values can be serialized and deserialized by the JSON.stringify and JSON.parse methods.
 */
type JSONValue = null | string | number | boolean | {
    [value: string]: JSONValue;
} | Array<JSONValue>;
type AssistantMessage = {
    id: string;
    role: 'assistant';
    content: Array<{
        type: 'text';
        text: {
            value: string;
        };
    }>;
};
type DataMessage = {
    id?: string;
    role: 'data';
    data: JSONValue;
};

declare const getOriginalFetch$1: () => typeof fetch;
declare function callChatApi({ api, body, streamProtocol, credentials, headers, abortController, restoreMessagesOnFailure, onResponse, onUpdate, onFinish, onToolCall, generateId, fetch, }: {
    api: string;
    body: Record<string, any>;
    streamProtocol: 'data' | 'text' | undefined;
    credentials: RequestCredentials | undefined;
    headers: HeadersInit | undefined;
    abortController: (() => AbortController | null) | undefined;
    restoreMessagesOnFailure: () => void;
    onResponse: ((response: Response) => void | Promise<void>) | undefined;
    onUpdate: (newMessages: Message[], data: JSONValue[] | undefined) => void;
    onFinish: UseChatOptions['onFinish'];
    onToolCall: UseChatOptions['onToolCall'];
    generateId: IdGenerator;
    fetch: ReturnType<typeof getOriginalFetch$1> | undefined;
}): Promise<{
    messages: Message[];
    data: JSONValue[];
} | {
    messages: {
        id: string;
        createdAt: Date;
        role: "assistant";
        content: string;
    }[];
    data: never[];
}>;

declare const getOriginalFetch: () => typeof fetch;
declare function callCompletionApi({ api, prompt, credentials, headers, body, streamProtocol, setCompletion, setLoading, setError, setAbortController, onResponse, onFinish, onError, onData, fetch, }: {
    api: string;
    prompt: string;
    credentials: RequestCredentials | undefined;
    headers: HeadersInit | undefined;
    body: Record<string, any>;
    streamProtocol: 'data' | 'text' | undefined;
    setCompletion: (completion: string) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: Error | undefined) => void;
    setAbortController: (abortController: AbortController | null) => void;
    onResponse: ((response: Response) => void | Promise<void>) | undefined;
    onFinish: ((prompt: string, completion: string) => void) | undefined;
    onError: ((error: Error) => void) | undefined;
    onData: ((data: JSONValue[]) => void) | undefined;
    fetch: ReturnType<typeof getOriginalFetch> | undefined;
}): Promise<string | null | undefined>;

type StreamString = `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\n`;
interface StreamPart<CODE extends string, NAME extends string, TYPE> {
    code: CODE;
    name: NAME;
    parse: (value: JSONValue) => {
        type: NAME;
        value: TYPE;
    };
}
declare const textStreamPart: StreamPart<'0', 'text', string>;
declare const functionCallStreamPart: StreamPart<'1', 'function_call', {
    function_call: FunctionCall;
}>;
declare const dataStreamPart: StreamPart<'2', 'data', Array<JSONValue>>;
declare const errorStreamPart: StreamPart<'3', 'error', string>;
declare const assistantMessageStreamPart: StreamPart<'4', 'assistant_message', AssistantMessage>;
declare const assistantControlDataStreamPart: StreamPart<'5', 'assistant_control_data', {
    threadId: string;
    messageId: string;
}>;
declare const dataMessageStreamPart: StreamPart<'6', 'data_message', DataMessage>;
declare const toolCallsStreamPart: StreamPart<'7', 'tool_calls', {
    tool_calls: ToolCall[];
}>;
declare const messageAnnotationsStreamPart: StreamPart<'8', 'message_annotations', Array<JSONValue>>;
declare const toolCallStreamPart: StreamPart<'9', 'tool_call', ToolCall$1<string, any>>;
declare const toolResultStreamPart: StreamPart<'a', 'tool_result', Omit<ToolResult<string, any, any>, 'args' | 'toolName'>>;
declare const toolCallStreamingStartStreamPart: StreamPart<'b', 'tool_call_streaming_start', {
    toolCallId: string;
    toolName: string;
}>;
declare const toolCallDeltaStreamPart: StreamPart<'c', 'tool_call_delta', {
    toolCallId: string;
    argsTextDelta: string;
}>;
declare const finishMessageStreamPart: StreamPart<'d', 'finish_message', {
    finishReason: LanguageModelV1FinishReason;
    usage?: {
        promptTokens: number;
        completionTokens: number;
    };
}>;
declare const finishStepStreamPart: StreamPart<'e', 'finish_step', {
    isContinued: boolean;
    finishReason: LanguageModelV1FinishReason;
    usage?: {
        promptTokens: number;
        completionTokens: number;
    };
}>;
type StreamParts = typeof textStreamPart | typeof functionCallStreamPart | typeof dataStreamPart | typeof errorStreamPart | typeof assistantMessageStreamPart | typeof assistantControlDataStreamPart | typeof dataMessageStreamPart | typeof toolCallsStreamPart | typeof messageAnnotationsStreamPart | typeof toolCallStreamPart | typeof toolResultStreamPart | typeof toolCallStreamingStartStreamPart | typeof toolCallDeltaStreamPart | typeof finishMessageStreamPart | typeof finishStepStreamPart;
/**
 * Maps the type of a stream part to its value type.
 */
type StreamPartValueType = {
    [P in StreamParts as P['name']]: ReturnType<P['parse']>['value'];
};
type StreamPartType = ReturnType<typeof textStreamPart.parse> | ReturnType<typeof functionCallStreamPart.parse> | ReturnType<typeof dataStreamPart.parse> | ReturnType<typeof errorStreamPart.parse> | ReturnType<typeof assistantMessageStreamPart.parse> | ReturnType<typeof assistantControlDataStreamPart.parse> | ReturnType<typeof dataMessageStreamPart.parse> | ReturnType<typeof toolCallsStreamPart.parse> | ReturnType<typeof messageAnnotationsStreamPart.parse> | ReturnType<typeof toolCallStreamPart.parse> | ReturnType<typeof toolResultStreamPart.parse> | ReturnType<typeof toolCallStreamingStartStreamPart.parse> | ReturnType<typeof toolCallDeltaStreamPart.parse> | ReturnType<typeof finishMessageStreamPart.parse> | ReturnType<typeof finishStepStreamPart.parse>;
/**
 * The map of prefixes for data in the stream
 *
 * - 0: Text from the LLM response
 * - 1: (OpenAI) function_call responses
 * - 2: custom JSON added by the user using `Data`
 * - 6: (OpenAI) tool_call responses
 *
 * Example:
 * ```
 * 0:Vercel
 * 0:'s
 * 0: AI
 * 0: AI
 * 0: SDK
 * 0: is great
 * 0:!
 * 2: { "someJson": "value" }
 * 1: {"function_call": {"name": "get_current_weather", "arguments": "{\\n\\"location\\": \\"Charlottesville, Virginia\\",\\n\\"format\\": \\"celsius\\"\\n}"}}
 * 6: {"tool_call": {"id": "tool_0", "type": "function", "function": {"name": "get_current_weather", "arguments": "{\\n\\"location\\": \\"Charlottesville, Virginia\\",\\n\\"format\\": \\"celsius\\"\\n}"}}}
 *```
 */
declare const StreamStringPrefixes: {
    readonly text: "0";
    readonly function_call: "1";
    readonly data: "2";
    readonly error: "3";
    readonly assistant_message: "4";
    readonly assistant_control_data: "5";
    readonly data_message: "6";
    readonly tool_calls: "7";
    readonly message_annotations: "8";
    readonly tool_call: "9";
    readonly tool_result: "a";
    readonly tool_call_streaming_start: "b";
    readonly tool_call_delta: "c";
    readonly finish_message: "d";
    readonly finish_step: "e";
};
/**
Parses a stream part from a string.

@param line The string to parse.
@returns The parsed stream part.
@throws An error if the string cannot be parsed.
 */
declare const parseStreamPart: (line: string) => StreamPartType;
/**
Prepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,
and appends a new line.

It ensures type-safety for the part type and value.
 */
declare function formatStreamPart<T extends keyof StreamPartValueType>(type: T, value: StreamPartValueType[T]): StreamString;

declare function createChunkDecoder(): (chunk: Uint8Array | undefined) => string;
declare function createChunkDecoder(complex: false): (chunk: Uint8Array | undefined) => string;
declare function createChunkDecoder(complex: true): (chunk: Uint8Array | undefined) => StreamPartType[];
declare function createChunkDecoder(complex?: boolean): (chunk: Uint8Array | undefined) => StreamPartType[] | string;

/**
 * Converts a data URL of type text/* to a text string.
 */
declare function getTextFromDataUrl(dataUrl: string): string;

/**
Create a type from an object with all keys and nested keys set to optional.
The helper supports normal objects and Zod schemas (which are resolved automatically).
It always recurses into arrays.

Adopted from [type-fest](https://github.com/sindresorhus/type-fest/tree/main) PartialDeep.
 */
type DeepPartial<T> = T extends z.ZodTypeAny ? DeepPartialInternal<z.infer<T>> : DeepPartialInternal<T>;
type DeepPartialInternal<T> = T extends null | undefined | string | number | boolean | symbol | bigint | void | Date | RegExp | ((...arguments_: any[]) => unknown) | (new (...arguments_: any[]) => unknown) ? T : T extends Map<infer KeyType, infer ValueType> ? PartialMap<KeyType, ValueType> : T extends Set<infer ItemType> ? PartialSet<ItemType> : T extends ReadonlyMap<infer KeyType, infer ValueType> ? PartialReadonlyMap<KeyType, ValueType> : T extends ReadonlySet<infer ItemType> ? PartialReadonlySet<ItemType> : T extends object ? T extends ReadonlyArray<infer ItemType> ? ItemType[] extends T ? readonly ItemType[] extends T ? ReadonlyArray<DeepPartialInternal<ItemType | undefined>> : Array<DeepPartialInternal<ItemType | undefined>> : PartialObject<T> : PartialObject<T> : unknown;
type PartialMap<KeyType, ValueType> = {} & Map<DeepPartialInternal<KeyType>, DeepPartialInternal<ValueType>>;
type PartialSet<T> = {} & Set<DeepPartialInternal<T>>;
type PartialReadonlyMap<KeyType, ValueType> = {} & ReadonlyMap<DeepPartialInternal<KeyType>, DeepPartialInternal<ValueType>>;
type PartialReadonlySet<T> = {} & ReadonlySet<DeepPartialInternal<T>>;
type PartialObject<ObjectType extends object> = {
    [KeyType in keyof ObjectType]?: DeepPartialInternal<ObjectType[KeyType]>;
};

/**
 * Performs a deep-equal comparison of two parsed JSON objects.
 *
 * @param {any} obj1 - The first object to compare.
 * @param {any} obj2 - The second object to compare.
 * @returns {boolean} - Returns true if the two objects are deeply equal, false otherwise.
 */
declare function isDeepEqualData(obj1: any, obj2: any): boolean;

declare function processDataProtocolResponse({ reader, abortControllerRef, update, onToolCall, onFinish, generateId, getCurrentDate, }: {
    reader: ReadableStreamDefaultReader<Uint8Array>;
    abortControllerRef?: {
        current: AbortController | null;
    };
    update: (newMessages: Message[], data: JSONValue[] | undefined) => void;
    onToolCall?: UseChatOptions['onToolCall'];
    onFinish?: (options: {
        message: Message | undefined;
        finishReason: LanguageModelV1FinishReason;
        usage: {
            completionTokens: number;
            promptTokens: number;
            totalTokens: number;
        };
    }) => void;
    generateId?: () => string;
    getCurrentDate?: () => Date;
}): Promise<{
    messages: Message[];
    data: JSONValue[];
}>;

declare function parsePartialJson(jsonText: string | undefined): {
    value: JSONValue$1 | undefined;
    state: 'undefined-input' | 'successful-parse' | 'repaired-parse' | 'failed-parse';
};

declare function processChatStream({ getStreamedResponse, experimental_onFunctionCall, experimental_onToolCall, updateChatRequest, getCurrentMessages, }: {
    getStreamedResponse: () => Promise<Message | {
        messages: Message[];
        data: JSONValue[];
    }>;
    experimental_onFunctionCall?: (chatMessages: Message[], functionCall: FunctionCall) => Promise<void | ChatRequest>;
    experimental_onToolCall?: (chatMessages: Message[], toolCalls: ToolCall[]) => Promise<void | ChatRequest>;
    updateChatRequest: (chatRequest: ChatRequest) => void;
    getCurrentMessages: () => Message[];
}): Promise<void>;

/**
Converts a ReadableStreamDefaultReader into an async generator that yields
StreamPart objects.

@param reader
       Reader for the stream to read from.
@param isAborted
       Optional function that returns true if the request has been aborted.
       If the function returns true, the generator will stop reading the stream.
       If the function is not provided, the generator will not stop reading the stream.
 */
declare function readDataStream(reader: ReadableStreamDefaultReader<Uint8Array>, { isAborted, }?: {
    isAborted?: () => boolean;
}): AsyncGenerator<StreamPartType>;

/**
 * Used to mark schemas so we can support both Zod and custom schemas.
 */
declare const schemaSymbol: unique symbol;
type Schema<OBJECT = unknown> = Validator<OBJECT> & {
    /**
     * Used to mark schemas so we can support both Zod and custom schemas.
     */
    [schemaSymbol]: true;
    /**
     * Schema type for inference.
     */
    _type: OBJECT;
    /**
     * The JSON Schema for the schema. It is passed to the providers.
     */
    readonly jsonSchema: JSONSchema7;
};
/**
 * Create a schema using a JSON Schema.
 *
 * @param jsonSchema The JSON Schema for the schema.
 * @param options.validate Optional. A validation function for the schema.
 */
declare function jsonSchema<OBJECT = unknown>(jsonSchema: JSONSchema7, { validate, }?: {
    validate?: (value: unknown) => {
        success: true;
        value: OBJECT;
    } | {
        success: false;
        error: Error;
    };
}): Schema<OBJECT>;
declare function asSchema<OBJECT>(schema: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>): Schema<OBJECT>;
declare function zodSchema<OBJECT>(zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>): Schema<OBJECT>;

export { type AssistantMessage, type AssistantStatus, type Attachment, type ChatRequest, type ChatRequestOptions, type CreateMessage, type DataMessage, type DeepPartial, type Function, type FunctionCall, type FunctionCallHandler, type IdGenerator, type JSONValue, type Message, type RequestOptions, type Schema, type StreamPart, type StreamString, type Tool, type ToolCall, type ToolCallHandler, type ToolChoice, type ToolInvocation, type UseAssistantOptions, type UseChatOptions, type UseCompletionOptions, asSchema, callChatApi, callCompletionApi, createChunkDecoder, formatStreamPart, getTextFromDataUrl, isDeepEqualData, jsonSchema, parsePartialJson, parseStreamPart, processChatStream, processDataProtocolResponse, readDataStream, zodSchema };
