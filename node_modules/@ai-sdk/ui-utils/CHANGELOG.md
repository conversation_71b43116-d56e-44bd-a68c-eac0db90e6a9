# @ai-sdk/ui-utils

## 0.0.50

### Patch Changes

- a85c965: fix (ai/ui): send message annotations from onChunk

## 0.0.49

### Patch Changes

- 3bf8da0: fix (ai/ui): update latest message with stream data message annotations until new message starts

## 0.0.48

### Patch Changes

- aa98cdb: chore: more flexible dependency versioning
- 811a317: feat (ai/core): multi-part tool results (incl. images)
- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26

## 0.0.47

### Patch Changes

- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/provider-utils@1.0.21

## 0.0.46

### Patch Changes

- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/provider-utils@1.0.20

## 0.0.45

### Patch Changes

- cd77c5d: feat (ai/core): add isContinued to steps

## 0.0.44

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19

## 0.0.43

### Patch Changes

- 1f590ef: chore (ai): rename roundtrips to steps

## 0.0.42

### Patch Changes

- 14210d5: feat (ai/core): add sendUsage information to streamText data stream methods

## 0.0.41

### Patch Changes

- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23

## 0.0.40

### Patch Changes

- aa2dc58: feat (ai/core): add maxToolRoundtrips to streamText

## 0.0.39

### Patch Changes

- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/provider-utils@1.0.17

## 0.0.38

### Patch Changes

- d151349: feat (ai/core): array output for generateObject / streamObject

## 0.0.37

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16

## 0.0.36

### Patch Changes

- b5a82b7: chore (ai): update zod-to-json-schema to 3.23.2

## 0.0.35

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15

## 0.0.34

### Patch Changes

- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/provider-utils@1.0.14

## 0.0.33

### Patch Changes

- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/provider-utils@1.0.13

## 0.0.32

### Patch Changes

- dd712ac: fix: use FetchFunction type to prevent self-reference
- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12

## 0.0.31

### Patch Changes

- Updated dependencies [dd4a0f5]
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11

## 0.0.30

### Patch Changes

- e9c891d: feat (ai/react): useObject supports non-Zod schemas
- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18

## 0.0.29

### Patch Changes

- e5b58f3: fix (ai/ui): forward streaming errors in useChat

## 0.0.28

### Patch Changes

- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/provider-utils@1.0.9

## 0.0.27

### Patch Changes

- Updated dependencies [d58517b]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/provider-utils@1.0.8

## 0.0.26

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/provider-utils@1.0.7

## 0.0.25

### Patch Changes

- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6

## 0.0.24

### Patch Changes

- 5be25124: fix (ai/ui): useChat messages have stable ids with streamProtocol: "text"

## 0.0.23

### Patch Changes

- fea7b604: chore (ai/ui): remove "args" and "toolName" from tool result stream part.

## 0.0.22

### Patch Changes

- 1d93d716: fix (ai/ui): parse null as NaN in finish message stream parts

## 0.0.21

### Patch Changes

- c450fcf7: feat (ui): invoke useChat onFinish with finishReason and tokens
- e4a1719f: chore (ai/ui): rename streamMode to streamProtocol

## 0.0.20

### Patch Changes

- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5

## 0.0.19

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4

## 0.0.18

### Patch Changes

- @ai-sdk/provider-utils@1.0.3

## 0.0.17

### Patch Changes

- f63829fe: feat (ai/ui): add allowEmptySubmit flag to handleSubmit

## 0.0.16

### Patch Changes

- 5b7b3bbe: fix (ai/ui): tool call streaming

## 0.0.15

### Patch Changes

- 1f67fe49: feat (ai/ui): stream tool calls with streamText and useChat

## 0.0.14

### Patch Changes

- 99ddbb74: feat (ai/react): add experimental support for managing attachments to useChat

## 0.0.13

### Patch Changes

- a6cb2c8b: feat (ai/ui): add keepLastMessageOnError option to useChat

## 0.0.12

### Patch Changes

- 56bbc2a7: feat (ai/ui): set body and headers directly on options for handleSubmit and append

## 0.0.11

### Patch Changes

- @ai-sdk/provider-utils@1.0.2

## 0.0.10

### Patch Changes

- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1

## 0.0.9

### Patch Changes

- 1894f811: feat (ai/ui): allow JSONValue as data in useChat handleSubmit

## 0.0.8

### Patch Changes

- d3100b9c: feat (ai/ui): support custom fetch function in useChat, useCompletion, useAssistant, useObject

## 0.0.7

### Patch Changes

- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider-utils@1.0.0

## 0.0.6

### Patch Changes

- 54bf4083: feat (ai/react): control request body in useChat

## 0.0.5

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16

## 0.0.4

### Patch Changes

- 008725ec: chore (@ai-sdk/ui-utils): move functions

## 0.0.3

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15

## 0.0.2

### Patch Changes

- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14

## 0.0.1

### Patch Changes

- 85f209a4: chore: extracted ui library support into separate modules
