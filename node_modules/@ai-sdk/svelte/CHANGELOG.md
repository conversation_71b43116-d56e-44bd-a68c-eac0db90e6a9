# @ai-sdk/svelte

## 0.0.57

### Patch Changes

- Updated dependencies [a85c965]
  - @ai-sdk/ui-utils@0.0.50

## 0.0.56

### Patch Changes

- d92fd9f: feat (ui/svelte): support Svelte 5 peer dependency

## 0.0.55

### Patch Changes

- Updated dependencies [3bf8da0]
  - @ai-sdk/ui-utils@0.0.49

## 0.0.54

### Patch Changes

- aa98cdb: chore: more flexible dependency versioning
- Updated dependencies [aa98cdb]
- Updated dependencies [7b937c5]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/ui-utils@0.0.48

## 0.0.53

### Patch Changes

- @ai-sdk/provider-utils@1.0.21
- @ai-sdk/ui-utils@0.0.47

## 0.0.52

### Patch Changes

- caedcda: feat (ai/ui): add setData helper to useChat

## 0.0.51

### Patch Changes

- @ai-sdk/provider-utils@1.0.20
- @ai-sdk/ui-utils@0.0.46

## 0.0.50

### Patch Changes

- Updated dependencies [cd77c5d]
  - @ai-sdk/ui-utils@0.0.45

## 0.0.49

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19
  - @ai-sdk/ui-utils@0.0.44

## 0.0.48

### Patch Changes

- Updated dependencies [1f590ef]
  - @ai-sdk/ui-utils@0.0.43

## 0.0.47

### Patch Changes

- Updated dependencies [14210d5]
  - @ai-sdk/ui-utils@0.0.42

## 0.0.46

### Patch Changes

- Updated dependencies [03313cd]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/ui-utils@0.0.41

## 0.0.45

### Patch Changes

- Updated dependencies [aa2dc58]
  - @ai-sdk/ui-utils@0.0.40

## 0.0.44

### Patch Changes

- @ai-sdk/provider-utils@1.0.17
- @ai-sdk/ui-utils@0.0.39

## 0.0.43

### Patch Changes

- Updated dependencies [d151349]
  - @ai-sdk/ui-utils@0.0.38

## 0.0.42

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16
  - @ai-sdk/ui-utils@0.0.37

## 0.0.41

### Patch Changes

- Updated dependencies [b5a82b7]
  - @ai-sdk/ui-utils@0.0.36

## 0.0.40

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15
  - @ai-sdk/ui-utils@0.0.35

## 0.0.39

### Patch Changes

- @ai-sdk/provider-utils@1.0.14
- @ai-sdk/ui-utils@0.0.34

## 0.0.38

### Patch Changes

- @ai-sdk/provider-utils@1.0.13
- @ai-sdk/ui-utils@0.0.33

## 0.0.37

### Patch Changes

- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12
  - @ai-sdk/ui-utils@0.0.32

## 0.0.36

### Patch Changes

- @ai-sdk/provider-utils@1.0.11
- @ai-sdk/ui-utils@0.0.31

## 0.0.35

### Patch Changes

- Updated dependencies [e9c891d]
- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/ui-utils@0.0.30
  - @ai-sdk/provider-utils@1.0.10

## 0.0.34

### Patch Changes

- Updated dependencies [e5b58f3]
  - @ai-sdk/ui-utils@0.0.29

## 0.0.33

### Patch Changes

- @ai-sdk/provider-utils@1.0.9
- @ai-sdk/ui-utils@0.0.28

## 0.0.32

### Patch Changes

- @ai-sdk/provider-utils@1.0.8
- @ai-sdk/ui-utils@0.0.27

## 0.0.31

### Patch Changes

- @ai-sdk/provider-utils@1.0.7
- @ai-sdk/ui-utils@0.0.26

## 0.0.30

### Patch Changes

- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6
  - @ai-sdk/ui-utils@0.0.25

## 0.0.29

### Patch Changes

- Updated dependencies [5be25124]
  - @ai-sdk/ui-utils@0.0.24

## 0.0.28

### Patch Changes

- Updated dependencies [fea7b604]
  - @ai-sdk/ui-utils@0.0.23

## 0.0.27

### Patch Changes

- Updated dependencies [1d93d716]
  - @ai-sdk/ui-utils@0.0.22

## 0.0.26

### Patch Changes

- b694f2f9: feat (ai/svelte): add tool calling support to useChat

## 0.0.25

### Patch Changes

- c450fcf7: feat (ui): invoke useChat onFinish with finishReason and tokens
- e4a1719f: chore (ai/ui): rename streamMode to streamProtocol
- Updated dependencies [c450fcf7]
- Updated dependencies [e4a1719f]
  - @ai-sdk/ui-utils@0.0.21

## 0.0.24

### Patch Changes

- b2bee4c5: fix (ai/ui): send data, body, headers in useChat().reload

## 0.0.23

### Patch Changes

- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5
  - @ai-sdk/ui-utils@0.0.20

## 0.0.22

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4
  - @ai-sdk/ui-utils@0.0.19

## 0.0.21

### Patch Changes

- @ai-sdk/provider-utils@1.0.3
- @ai-sdk/ui-utils@0.0.18

## 0.0.20

### Patch Changes

- f63829fe: feat (ai/ui): add allowEmptySubmit flag to handleSubmit
- 4b2c09d9: feat (ai/ui): add mutator function support to useChat / setMessages
- Updated dependencies [f63829fe]
  - @ai-sdk/ui-utils@0.0.17

## 0.0.19

### Patch Changes

- Updated dependencies [5b7b3bbe]
  - @ai-sdk/ui-utils@0.0.16

## 0.0.18

### Patch Changes

- Updated dependencies [1f67fe49]
  - @ai-sdk/ui-utils@0.0.15

## 0.0.17

### Patch Changes

- Updated dependencies [99ddbb74]
  - @ai-sdk/ui-utils@0.0.14

## 0.0.16

### Patch Changes

- a6cb2c8b: feat (ai/ui): add keepLastMessageOnError option to useChat
- Updated dependencies [a6cb2c8b]
  - @ai-sdk/ui-utils@0.0.13

## 0.0.15

### Patch Changes

- 56bbc2a7: feat (ai/ui): set body and headers directly on options for handleSubmit and append
- Updated dependencies [56bbc2a7]
  - @ai-sdk/ui-utils@0.0.12

## 0.0.14

### Patch Changes

- @ai-sdk/provider-utils@1.0.2
- @ai-sdk/ui-utils@0.0.11

## 0.0.13

### Patch Changes

- 3db90c3d: allow empty handleSubmit submissions for useChat
- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1
  - @ai-sdk/ui-utils@0.0.10

## 0.0.12

### Patch Changes

- Updated dependencies [1894f811]
  - @ai-sdk/ui-utils@0.0.9

## 0.0.11

### Patch Changes

- d3100b9c: feat (ai/ui): support custom fetch function in useChat, useCompletion, useAssistant, useObject
- Updated dependencies [d3100b9c]
  - @ai-sdk/ui-utils@0.0.8

## 0.0.10

### Patch Changes

- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider-utils@1.0.0
  - @ai-sdk/ui-utils@0.0.7

## 0.0.9

### Patch Changes

- 827ef450: feat (ai/ui): improve error handling in useAssistant

## 0.0.8

### Patch Changes

- 82d9c8de: feat (ai/ui): make event in useAssistant submitMessage optional

## 0.0.7

### Patch Changes

- Updated dependencies [54bf4083]
  - @ai-sdk/ui-utils@0.0.6

## 0.0.6

### Patch Changes

- d42b8907: feat (ui): make event in handleSubmit optional

## 0.0.5

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16
  - @ai-sdk/ui-utils@0.0.5

## 0.0.4

### Patch Changes

- Updated dependencies [008725ec]
  - @ai-sdk/ui-utils@0.0.4

## 0.0.3

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15
  - @ai-sdk/ui-utils@0.0.3

## 0.0.2

### Patch Changes

- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14
  - @ai-sdk/ui-utils@0.0.2

## 0.0.1

### Patch Changes

- 85f209a4: chore: extracted ui library support into separate modules
- Updated dependencies [85f209a4]
  - @ai-sdk/ui-utils@0.0.1
