{"version": 3, "sources": ["../src/use-chat.ts", "../src/use-completion.ts", "../src/use-assistant.ts"], "sourcesContent": ["import { FetchFunction } from '@ai-sdk/provider-utils';\nimport type {\n  ChatRequest,\n  ChatRequestOptions,\n  CreateMessage,\n  IdGenerator,\n  JSONValue,\n  Message,\n  UseChatOptions as SharedUseChatOptions,\n} from '@ai-sdk/ui-utils';\nimport {\n  callChatApi,\n  generateId as generateIdFunc,\n  processChatStream,\n} from '@ai-sdk/ui-utils';\nimport { useSWR } from 'sswr';\nimport { Readable, Writable, derived, get, writable } from 'svelte/store';\nexport type { CreateMessage, Message };\n\nexport type UseChatOptions = SharedUseChatOptions & {\n  /**\n  Maximum number of automatic roundtrips for tool calls.\n\n  An automatic tool call roundtrip is a call to the server with the\n  tool call results when all tool calls in the last assistant\n  message have results.\n\n  A maximum number is required to prevent infinite loops in the\n  case of misconfigured tools.\n\n  By default, it's set to 0, which will disable the feature.\n\n@deprecated Use `maxSteps` instead (which is `maxToolRoundtrips` + 1).\n     */\n  maxToolRoundtrips?: number;\n\n  /**\nMaximum number of sequential LLM calls (steps), e.g. when you use tool calls. Must be at least 1.\n\nA maximum number is required to prevent infinite loops in the case of misconfigured tools.\n\nBy default, it's set to 1, which means that only a single LLM call is made.\n */\n  maxSteps?: number;\n};\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: Readable<Message[]>;\n  /** The error object of the API request */\n  error: Readable<undefined | Error>;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param chatRequestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (\n    messages: Message[] | ((messages: Message[]) => Message[]),\n  ) => void;\n\n  /** The current value of the input */\n  input: Writable<string>;\n  /** Form submission handler to automatically reset input and append a user message  */\n  handleSubmit: (\n    event?: { preventDefault?: () => void },\n    chatRequestOptions?: ChatRequestOptions,\n  ) => void;\n  metadata?: Object;\n  /** Whether the API request is in progress */\n  isLoading: Readable<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData */\n  data: Readable<JSONValue[] | undefined>;\n  /** Set the data of the chat. You can use this to transform or clear the chat data. */\n  setData: (\n    data:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => void;\n};\n\nconst getStreamedResponse = async (\n  api: string,\n  chatRequest: ChatRequest,\n  mutate: (messages: Message[]) => void,\n  mutateStreamData: (data: JSONValue[] | undefined) => void,\n  existingData: JSONValue[] | undefined,\n  extraMetadata: {\n    credentials?: RequestCredentials;\n    headers?: Record<string, string> | Headers;\n    body?: any;\n  },\n  previousMessages: Message[],\n  abortControllerRef: AbortController | null,\n  generateId: IdGenerator,\n  streamProtocol: UseChatOptions['streamProtocol'],\n  onFinish: UseChatOptions['onFinish'],\n  onResponse: ((response: Response) => void | Promise<void>) | undefined,\n  onToolCall: UseChatOptions['onToolCall'] | undefined,\n  sendExtraMessageFields: boolean | undefined,\n  fetch: FetchFunction | undefined,\n  keepLastMessageOnError: boolean | undefined,\n) => {\n  // Do an optimistic update to the chat state to show the updated messages\n  // immediately.\n  mutate(chatRequest.messages);\n\n  const constructedMessagesPayload = sendExtraMessageFields\n    ? chatRequest.messages\n    : chatRequest.messages.map(\n        ({\n          role,\n          content,\n          name,\n          data,\n          annotations,\n          function_call,\n          tool_calls,\n          tool_call_id,\n          toolInvocations,\n        }) => ({\n          role,\n          content,\n          ...(name !== undefined && { name }),\n          ...(data !== undefined && { data }),\n          ...(annotations !== undefined && { annotations }),\n          ...(toolInvocations !== undefined && { toolInvocations }),\n          // outdated function/tool call handling (TODO deprecate):\n          tool_call_id,\n          ...(function_call !== undefined && { function_call }),\n          ...(tool_calls !== undefined && { tool_calls }),\n        }),\n      );\n\n  return await callChatApi({\n    api,\n    body: {\n      messages: constructedMessagesPayload,\n      data: chatRequest.data,\n      ...extraMetadata.body,\n      ...chatRequest.body,\n      ...(chatRequest.functions !== undefined && {\n        functions: chatRequest.functions,\n      }),\n      ...(chatRequest.function_call !== undefined && {\n        function_call: chatRequest.function_call,\n      }),\n      ...(chatRequest.tools !== undefined && {\n        tools: chatRequest.tools,\n      }),\n      ...(chatRequest.tool_choice !== undefined && {\n        tool_choice: chatRequest.tool_choice,\n      }),\n    },\n    streamProtocol,\n    credentials: extraMetadata.credentials,\n    headers: {\n      ...extraMetadata.headers,\n      ...chatRequest.headers,\n    },\n    abortController: () => abortControllerRef,\n    restoreMessagesOnFailure() {\n      if (!keepLastMessageOnError) {\n        mutate(previousMessages);\n      }\n    },\n    onResponse,\n    onUpdate(merged, data) {\n      mutate([...chatRequest.messages, ...merged]);\n      mutateStreamData([...(existingData || []), ...(data || [])]);\n    },\n    onFinish,\n    generateId,\n    onToolCall,\n    fetch,\n  });\n};\n\nlet uniqueId = 0;\n\nconst store: Record<string, Message[] | undefined> = {};\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe message must have at least one tool invocation and all tool invocations\nmust have a result.\n */\nfunction isAssistantMessageWithCompletedToolCalls(message: Message) {\n  return (\n    message.role === 'assistant' &&\n    message.toolInvocations &&\n    message.toolInvocations.length > 0 &&\n    message.toolInvocations.every(toolInvocation => 'result' in toolInvocation)\n  );\n}\n\n/**\nReturns the number of trailing assistant messages in the array.\n */\nfunction countTrailingAssistantMessages(messages: Message[]) {\n  let count = 0;\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === 'assistant') {\n      count++;\n    } else {\n      break;\n    }\n  }\n\n  return count;\n}\n\nexport function useChat({\n  api = '/api/chat',\n  id,\n  initialMessages = [],\n  initialInput = '',\n  sendExtraMessageFields,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  streamMode,\n  streamProtocol,\n  onResponse,\n  onFinish,\n  onError,\n  onToolCall,\n  credentials,\n  headers,\n  body,\n  generateId = generateIdFunc,\n  fetch,\n  keepLastMessageOnError = false,\n  maxToolRoundtrips = 0,\n  maxSteps = maxToolRoundtrips != null ? maxToolRoundtrips + 1 : 1,\n}: UseChatOptions = {}): UseChatHelpers & {\n  addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n} {\n  // streamMode is deprecated, use streamProtocol instead.\n  if (streamMode) {\n    streamProtocol ??= streamMode === 'text' ? 'text' : undefined;\n  }\n\n  // Generate a unique id for the chat if not provided.\n  const chatId = id || `chat-${uniqueId++}`;\n\n  const key = `${api}|${chatId}`;\n  const {\n    data,\n    mutate: originalMutate,\n    isLoading: isSWRLoading,\n  } = useSWR<Message[]>(key, {\n    fetcher: () => store[key] || initialMessages,\n    fallbackData: initialMessages,\n  });\n\n  const streamData = writable<JSONValue[] | undefined>(undefined);\n\n  const loading = writable<boolean>(false);\n\n  // Force the `data` to be `initialMessages` if it's `undefined`.\n  data.set(initialMessages);\n\n  const mutate = (data: Message[]) => {\n    store[key] = data;\n    return originalMutate(data);\n  };\n\n  // Because of the `fallbackData` option, the `data` will never be `undefined`.\n  const messages = data as Writable<Message[]>;\n\n  // Abort controller to cancel the current API call.\n  let abortController: AbortController | null = null;\n\n  const extraMetadata = {\n    credentials,\n    headers,\n    body,\n  };\n\n  const error = writable<undefined | Error>(undefined);\n\n  // Actual mutation hook to send messages to the API endpoint and update the\n  // chat state.\n  async function triggerRequest(chatRequest: ChatRequest) {\n    const messagesSnapshot = get(messages);\n    const messageCount = messagesSnapshot.length;\n\n    try {\n      error.set(undefined);\n      loading.set(true);\n      abortController = new AbortController();\n\n      await processChatStream({\n        getStreamedResponse: () =>\n          getStreamedResponse(\n            api,\n            chatRequest,\n            mutate,\n            data => {\n              streamData.set(data);\n            },\n            get(streamData),\n            extraMetadata,\n            get(messages),\n            abortController,\n            generateId,\n            streamProtocol,\n            onFinish,\n            onResponse,\n            onToolCall,\n            sendExtraMessageFields,\n            fetch,\n            keepLastMessageOnError,\n          ),\n        experimental_onFunctionCall,\n        experimental_onToolCall,\n        updateChatRequest: chatRequestParam => {\n          chatRequest = chatRequestParam;\n        },\n        getCurrentMessages: () => get(messages),\n      });\n\n      abortController = null;\n    } catch (err) {\n      // Ignore abort errors as they are expected.\n      if ((err as any).name === 'AbortError') {\n        abortController = null;\n        return null;\n      }\n\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      error.set(err as Error);\n    } finally {\n      loading.set(false);\n    }\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const newMessagesSnapshot = get(messages);\n\n    const lastMessage = newMessagesSnapshot[newMessagesSnapshot.length - 1];\n    if (\n      // ensure we actually have new messages (to prevent infinite loops in case of errors):\n      newMessagesSnapshot.length > messageCount &&\n      // ensure there is a last message:\n      lastMessage != null &&\n      // check if the feature is enabled:\n      maxSteps > 1 &&\n      // check that next step is possible:\n      isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n      // limit the number of automatic steps:\n      countTrailingAssistantMessages(newMessagesSnapshot) < maxSteps\n    ) {\n      await triggerRequest({ messages: newMessagesSnapshot });\n    }\n  }\n\n  const append: UseChatHelpers['append'] = async (\n    message: Message | CreateMessage,\n    {\n      options,\n      functions,\n      function_call,\n      tools,\n      tool_choice,\n      data,\n      headers,\n      body,\n    }: ChatRequestOptions = {},\n  ) => {\n    if (!message.id) {\n      message.id = generateId();\n    }\n\n    const requestOptions = {\n      headers: headers ?? options?.headers,\n      body: body ?? options?.body,\n    };\n\n    const chatRequest: ChatRequest = {\n      messages: get(messages).concat(message as Message),\n      options: requestOptions,\n      headers: requestOptions.headers,\n      body: requestOptions.body,\n      data,\n      ...(functions !== undefined && { functions }),\n      ...(function_call !== undefined && { function_call }),\n      ...(tools !== undefined && { tools }),\n      ...(tool_choice !== undefined && { tool_choice }),\n    };\n    return triggerRequest(chatRequest);\n  };\n\n  const reload: UseChatHelpers['reload'] = async ({\n    options,\n    functions,\n    function_call,\n    tools,\n    tool_choice,\n    data,\n    headers,\n    body,\n  }: ChatRequestOptions = {}) => {\n    const messagesSnapshot = get(messages);\n    if (messagesSnapshot.length === 0) return null;\n\n    const requestOptions = {\n      headers: headers ?? options?.headers,\n      body: body ?? options?.body,\n    };\n\n    // Remove last assistant message and retry last user message.\n    const lastMessage = messagesSnapshot.at(-1);\n    if (lastMessage?.role === 'assistant') {\n      const chatRequest: ChatRequest = {\n        messages: messagesSnapshot.slice(0, -1),\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n        ...(functions !== undefined && { functions }),\n        ...(function_call !== undefined && { function_call }),\n        ...(tools !== undefined && { tools }),\n        ...(tool_choice !== undefined && { tool_choice }),\n      };\n\n      return triggerRequest(chatRequest);\n    }\n\n    const chatRequest: ChatRequest = {\n      messages: messagesSnapshot,\n      options: requestOptions,\n      headers: requestOptions.headers,\n      body: requestOptions.body,\n      data,\n    };\n\n    return triggerRequest(chatRequest);\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setMessages = (\n    messagesArg: Message[] | ((messages: Message[]) => Message[]),\n  ) => {\n    if (typeof messagesArg === 'function') {\n      messagesArg = messagesArg(get(messages));\n    }\n\n    mutate(messagesArg);\n  };\n\n  const setData = (\n    dataArg:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => {\n    if (typeof dataArg === 'function') {\n      dataArg = dataArg(get(streamData));\n    }\n\n    streamData.set(dataArg);\n  };\n\n  const input = writable(initialInput);\n\n  const handleSubmit = (\n    event?: { preventDefault?: () => void },\n    options: ChatRequestOptions = {},\n  ) => {\n    event?.preventDefault?.();\n    const inputValue = get(input);\n\n    if (!inputValue && !options.allowEmptySubmit) return;\n\n    const requestOptions = {\n      headers: options.headers ?? options.options?.headers,\n      body: options.body ?? options.options?.body,\n    };\n\n    const chatRequest: ChatRequest = {\n      messages:\n        !inputValue && options.allowEmptySubmit\n          ? get(messages)\n          : get(messages).concat({\n              id: generateId(),\n              content: inputValue,\n              role: 'user',\n              createdAt: new Date(),\n            } as Message),\n      options: requestOptions,\n      body: requestOptions.body,\n      headers: requestOptions.headers,\n      data: options.data,\n    };\n\n    triggerRequest(chatRequest);\n\n    input.set('');\n  };\n\n  const isLoading = derived(\n    [isSWRLoading, loading],\n    ([$isSWRLoading, $loading]) => {\n      return $isSWRLoading || $loading;\n    },\n  );\n\n  const addToolResult = ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => {\n    const messagesSnapshot = get(messages) ?? [];\n    const updatedMessages = messagesSnapshot.map((message, index, arr) =>\n      // update the tool calls in the last assistant message:\n      index === arr.length - 1 &&\n      message.role === 'assistant' &&\n      message.toolInvocations\n        ? {\n            ...message,\n            toolInvocations: message.toolInvocations.map(toolInvocation =>\n              toolInvocation.toolCallId === toolCallId\n                ? { ...toolInvocation, result }\n                : toolInvocation,\n            ),\n          }\n        : message,\n    );\n\n    messages.set(updatedMessages);\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const lastMessage = updatedMessages[updatedMessages.length - 1];\n\n    if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n      triggerRequest({ messages: updatedMessages });\n    }\n  };\n\n  return {\n    messages,\n    error,\n    append,\n    reload,\n    stop,\n    setMessages,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n    setData,\n    addToolResult,\n  };\n}\n", "import type {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n} from '@ai-sdk/ui-utils';\nimport { callCompletionApi } from '@ai-sdk/ui-utils';\nimport { useSWR } from 'sswr';\nimport { Readable, Writable, derived, get, writable } from 'svelte/store';\n\nexport type { UseCompletionOptions };\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: Readable<string>;\n  /** The error object of the API request */\n  error: Readable<undefined | Error>;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: Writable<string>;\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form onSubmit={handleSubmit}>\n   *  <input onChange={handleInputChange} value={input} />\n   * </form>\n   * ```\n   */\n  handleSubmit: (event?: { preventDefault?: () => void }) => void;\n  /** Whether the API request is in progress */\n  isLoading: Readable<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData */\n  data: Readable<JSONValue[] | undefined>;\n};\n\nlet uniqueId = 0;\n\nconst store: Record<string, any> = {};\n\nexport function useCompletion({\n  api = '/api/completion',\n  id,\n  initialCompletion = '',\n  initialInput = '',\n  credentials,\n  headers,\n  body,\n  streamMode,\n  streamProtocol,\n  onResponse,\n  onFinish,\n  onError,\n  fetch,\n}: UseCompletionOptions = {}): UseCompletionHelpers {\n  // streamMode is deprecated, use streamProtocol instead.\n  if (streamMode) {\n    streamProtocol ??= streamMode === 'text' ? 'text' : undefined;\n  }\n\n  // Generate an unique id for the completion if not provided.\n  const completionId = id || `completion-${uniqueId++}`;\n\n  const key = `${api}|${completionId}`;\n  const {\n    data,\n    mutate: originalMutate,\n    isLoading: isSWRLoading,\n  } = useSWR<string>(key, {\n    fetcher: () => store[key] || initialCompletion,\n    fallbackData: initialCompletion,\n  });\n\n  const streamData = writable<JSONValue[] | undefined>(undefined);\n\n  const loading = writable<boolean>(false);\n\n  // Force the `data` to be `initialCompletion` if it's `undefined`.\n  data.set(initialCompletion);\n\n  const mutate = (data: string) => {\n    store[key] = data;\n    return originalMutate(data);\n  };\n\n  // Because of the `fallbackData` option, the `data` will never be `undefined`.\n  const completion = data as Writable<string>;\n\n  const error = writable<undefined | Error>(undefined);\n\n  let abortController: AbortController | null = null;\n\n  const complete: UseCompletionHelpers['complete'] = async (\n    prompt: string,\n    options?: RequestOptions,\n  ) => {\n    const existingData = get(streamData);\n    return callCompletionApi({\n      api,\n      prompt,\n      credentials,\n      headers: {\n        ...headers,\n        ...options?.headers,\n      },\n      body: {\n        ...body,\n        ...options?.body,\n      },\n      streamProtocol,\n      setCompletion: mutate,\n      setLoading: loadingState => loading.set(loadingState),\n      setError: err => error.set(err),\n      setAbortController: controller => {\n        abortController = controller;\n      },\n      onResponse,\n      onFinish,\n      onError,\n      onData(data) {\n        streamData.set([...(existingData || []), ...(data || [])]);\n      },\n      fetch,\n    });\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setCompletion = (completion: string) => {\n    mutate(completion);\n  };\n\n  const input = writable(initialInput);\n\n  const handleSubmit = (event?: { preventDefault?: () => void }) => {\n    event?.preventDefault?.();\n\n    const inputValue = get(input);\n    return inputValue ? complete(inputValue) : undefined;\n  };\n\n  const isLoading = derived(\n    [isSWRLoading, loading],\n    ([$isSWRLoading, $loading]) => {\n      return $isSWRLoading || $loading;\n    },\n  );\n\n  return {\n    completion,\n    complete,\n    error,\n    stop,\n    setCompletion,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "import { isAbortError } from '@ai-sdk/provider-utils';\nimport type {\n  AssistantStatus,\n  CreateMessage,\n  Message,\n  UseAssistantOptions,\n} from '@ai-sdk/ui-utils';\nimport { generateId, readDataStream } from '@ai-sdk/ui-utils';\nimport { Readable, Writable, get, writable } from 'svelte/store';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nlet uniqueId = 0;\n\nconst store: Record<string, any> = {};\n\nexport type UseAssistantHelpers = {\n  /**\n   * The current array of chat messages.\n   */\n  messages: Readable<Message[]>;\n\n  /**\n   * Update the message store with a new array of messages.\n   */\n  setMessages: (messages: Message[]) => void;\n\n  /**\n   * The current thread ID.\n   */\n  threadId: Readable<string | undefined>;\n\n  /**\n   * The current value of the input field.\n   */\n  input: Writable<string>;\n\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param requestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    requestOptions?: { data?: Record<string, string> },\n  ) => Promise<void>;\n\n  /**\nAbort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n\n  /**\n   * Form submission handler that automatically resets the input field and appends a user message.\n   */\n  submitMessage: (\n    event?: { preventDefault?: () => void },\n    requestOptions?: { data?: Record<string, string> },\n  ) => Promise<void>;\n\n  /**\n   * The current status of the assistant. This can be used to show a loading indicator.\n   */\n  status: Readable<AssistantStatus>;\n\n  /**\n   * The error thrown during the assistant message processing, if any.\n   */\n  error: Readable<undefined | Error>;\n};\n\nexport function useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch,\n}: UseAssistantOptions): UseAssistantHelpers {\n  // Generate a unique thread ID\n  const threadIdStore = writable<string | undefined>(threadIdParam);\n\n  // Initialize message, input, status, and error stores\n  const key = `${api}|${threadIdParam ?? `completion-${uniqueId++}`}`;\n  const messages = writable<Message[]>(store[key] || []);\n  const input = writable('');\n  const status = writable<AssistantStatus>('awaiting_message');\n  const error = writable<undefined | Error>(undefined);\n\n  // To manage aborting the current fetch request\n  let abortController: AbortController | null = null;\n\n  // Update the message store\n  const mutateMessages = (newMessages: Message[]) => {\n    store[key] = newMessages;\n    messages.set(newMessages);\n  };\n\n  // Function to handle API calls and state management\n  async function append(\n    message: Message | CreateMessage,\n    requestOptions?: { data?: Record<string, string> },\n  ) {\n    status.set('in_progress');\n    abortController = new AbortController(); // Initialize a new AbortController\n\n    // Add the new message to the existing array\n    mutateMessages([\n      ...get(messages),\n      { ...message, id: message.id ?? generateId() },\n    ]);\n\n    input.set('');\n\n    try {\n      const actualFetch = fetch ?? getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: 'POST',\n        credentials,\n        signal: abortController.signal,\n        headers: { 'Content-Type': 'application/json', ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: threadIdParam ?? get(threadIdStore) ?? null,\n          message: message.content,\n\n          // optional request data:\n          data: requestOptions?.data,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          (await response.text()) ?? 'Failed to fetch the assistant response.',\n        );\n      }\n\n      if (response.body == null) {\n        throw new Error('The response body is empty.');\n      }\n\n      // Read the streamed response data\n      for await (const { type, value } of readDataStream(\n        response.body.getReader(),\n      )) {\n        switch (type) {\n          case 'assistant_message': {\n            mutateMessages([\n              ...get(messages),\n              {\n                id: value.id,\n                role: value.role,\n                content: value.content[0].text.value,\n              },\n            ]);\n            break;\n          }\n\n          case 'text': {\n            // text delta - add to last message:\n            mutateMessages(\n              get(messages).map((msg, index, array) => {\n                if (index === array.length - 1) {\n                  return { ...msg, content: msg.content + value };\n                }\n                return msg;\n              }),\n            );\n            break;\n          }\n\n          case 'data_message': {\n            mutateMessages([\n              ...get(messages),\n              {\n                id: value.id ?? generateId(),\n                role: 'data',\n                content: '',\n                data: value.data,\n              },\n            ]);\n            break;\n          }\n\n          case 'assistant_control_data': {\n            threadIdStore.set(value.threadId);\n\n            mutateMessages(\n              get(messages).map((msg, index, array) => {\n                if (index === array.length - 1) {\n                  return { ...msg, id: value.messageId };\n                }\n                return msg;\n              }),\n            );\n\n            break;\n          }\n\n          case 'error': {\n            error.set(new Error(value));\n            break;\n          }\n        }\n      }\n    } catch (err) {\n      // Ignore abort errors as they are expected when the user cancels the request:\n      if (isAbortError(error) && abortController?.signal?.aborted) {\n        abortController = null;\n        return;\n      }\n\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      error.set(err as Error);\n    } finally {\n      abortController = null;\n      status.set('awaiting_message');\n    }\n  }\n\n  function setMessages(messages: Message[]) {\n    mutateMessages(messages);\n  }\n\n  function stop() {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  }\n\n  // Function to handle form submission\n  async function submitMessage(\n    event?: { preventDefault?: () => void },\n    requestOptions?: { data?: Record<string, string> },\n  ) {\n    event?.preventDefault?.();\n    const inputValue = get(input);\n    if (!inputValue) return;\n\n    await append({ role: 'user', content: inputValue }, requestOptions);\n  }\n\n  return {\n    messages,\n    error,\n    threadId: threadIdStore,\n    input,\n    append,\n    submitMessage,\n    status,\n    setMessages,\n    stop,\n  };\n}\n"], "mappings": ";AAUA;AAAA,EACE;AAAA,EACA,cAAc;AAAA,EACd;AAAA,OACK;AACP,SAAS,cAAc;AACvB,SAA6B,SAAS,KAAK,gBAAgB;AAwF3D,IAAM,sBAAsB,OAC1B,KACA,aACA,QACA,kBACA,cACA,eAKA,kBACA,oBACAA,aACA,gBACA,UACA,YACA,YACA,wBACAC,QACA,2BACG;AAGH,SAAO,YAAY,QAAQ;AAE3B,QAAM,6BAA6B,yBAC/B,YAAY,WACZ,YAAY,SAAS;AAAA,IACnB,CAAC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MAC/C,GAAI,oBAAoB,UAAa,EAAE,gBAAgB;AAAA;AAAA,MAEvD;AAAA,MACA,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,MACnD,GAAI,eAAe,UAAa,EAAE,WAAW;AAAA,IAC/C;AAAA,EACF;AAEJ,SAAO,MAAM,YAAY;AAAA,IACvB;AAAA,IACA,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,MAAM,YAAY;AAAA,MAClB,GAAG,cAAc;AAAA,MACjB,GAAG,YAAY;AAAA,MACf,GAAI,YAAY,cAAc,UAAa;AAAA,QACzC,WAAW,YAAY;AAAA,MACzB;AAAA,MACA,GAAI,YAAY,kBAAkB,UAAa;AAAA,QAC7C,eAAe,YAAY;AAAA,MAC7B;AAAA,MACA,GAAI,YAAY,UAAU,UAAa;AAAA,QACrC,OAAO,YAAY;AAAA,MACrB;AAAA,MACA,GAAI,YAAY,gBAAgB,UAAa;AAAA,QAC3C,aAAa,YAAY;AAAA,MAC3B;AAAA,IACF;AAAA,IACA;AAAA,IACA,aAAa,cAAc;AAAA,IAC3B,SAAS;AAAA,MACP,GAAG,cAAc;AAAA,MACjB,GAAG,YAAY;AAAA,IACjB;AAAA,IACA,iBAAiB,MAAM;AAAA,IACvB,2BAA2B;AACzB,UAAI,CAAC,wBAAwB;AAC3B,eAAO,gBAAgB;AAAA,MACzB;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,MAAM;AACrB,aAAO,CAAC,GAAG,YAAY,UAAU,GAAG,MAAM,CAAC;AAC3C,uBAAiB,CAAC,GAAI,gBAAgB,CAAC,GAAI,GAAI,QAAQ,CAAC,CAAE,CAAC;AAAA,IAC7D;AAAA,IACA;AAAA,IACA,YAAAD;AAAA,IACA;AAAA,IACA,OAAAC;AAAA,EACF,CAAC;AACH;AAEA,IAAI,WAAW;AAEf,IAAM,QAA+C,CAAC;AAOtD,SAAS,yCAAyC,SAAkB;AAClE,SACE,QAAQ,SAAS,eACjB,QAAQ,mBACR,QAAQ,gBAAgB,SAAS,KACjC,QAAQ,gBAAgB,MAAM,oBAAkB,YAAY,cAAc;AAE9E;AAKA,SAAS,+BAA+B,UAAqB;AAC3D,MAAI,QAAQ;AACZ,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AACpC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA,kBAAkB,CAAC;AAAA,EACnB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAD,cAAa;AAAA,EACb,OAAAC;AAAA,EACA,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,WAAW,qBAAqB,OAAO,oBAAoB,IAAI;AACjE,IAAoB,CAAC,GAQnB;AAEA,MAAI,YAAY;AACd,+DAAmB,eAAe,SAAS,SAAS;AAAA,EACtD;AAGA,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,QAAM,MAAM,GAAG,GAAG,IAAI,MAAM;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,IAAI,OAAkB,KAAK;AAAA,IACzB,SAAS,MAAM,MAAM,GAAG,KAAK;AAAA,IAC7B,cAAc;AAAA,EAChB,CAAC;AAED,QAAM,aAAa,SAAkC,MAAS;AAE9D,QAAM,UAAU,SAAkB,KAAK;AAGvC,OAAK,IAAI,eAAe;AAExB,QAAM,SAAS,CAACC,UAAoB;AAClC,UAAM,GAAG,IAAIA;AACb,WAAO,eAAeA,KAAI;AAAA,EAC5B;AAGA,QAAM,WAAW;AAGjB,MAAI,kBAA0C;AAE9C,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,QAAQ,SAA4B,MAAS;AAInD,iBAAe,eAAe,aAA0B;AACtD,UAAM,mBAAmB,IAAI,QAAQ;AACrC,UAAM,eAAe,iBAAiB;AAEtC,QAAI;AACF,YAAM,IAAI,MAAS;AACnB,cAAQ,IAAI,IAAI;AAChB,wBAAkB,IAAI,gBAAgB;AAEtC,YAAM,kBAAkB;AAAA,QACtB,qBAAqB,MACnB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAAA,UAAQ;AACN,uBAAW,IAAIA,KAAI;AAAA,UACrB;AAAA,UACA,IAAI,UAAU;AAAA,UACd;AAAA,UACA,IAAI,QAAQ;AAAA,UACZ;AAAA,UACAF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACAC;AAAA,UACA;AAAA,QACF;AAAA,QACF;AAAA,QACA;AAAA,QACA,mBAAmB,sBAAoB;AACrC,wBAAc;AAAA,QAChB;AAAA,QACA,oBAAoB,MAAM,IAAI,QAAQ;AAAA,MACxC,CAAC;AAED,wBAAkB;AAAA,IACpB,SAAS,KAAK;AAEZ,UAAK,IAAY,SAAS,cAAc;AACtC,0BAAkB;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;AAAA,MACb;AAEA,YAAM,IAAI,GAAY;AAAA,IACxB,UAAE;AACA,cAAQ,IAAI,KAAK;AAAA,IACnB;AAGA,UAAM,sBAAsB,IAAI,QAAQ;AAExC,UAAM,cAAc,oBAAoB,oBAAoB,SAAS,CAAC;AACtE;AAAA;AAAA,MAEE,oBAAoB,SAAS;AAAA,MAE7B,eAAe;AAAA,MAEf,WAAW;AAAA,MAEX,yCAAyC,WAAW;AAAA,MAEpD,+BAA+B,mBAAmB,IAAI;AAAA,MACtD;AACA,YAAM,eAAe,EAAE,UAAU,oBAAoB,CAAC;AAAA,IACxD;AAAA,EACF;AAEA,QAAM,SAAmC,OACvC,SACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAAC;AAAA,IACA,SAAAC;AAAA,IACA,MAAAC;AAAA,EACF,IAAwB,CAAC,MACtB;AACH,QAAI,CAAC,QAAQ,IAAI;AACf,cAAQ,KAAKJ,YAAW;AAAA,IAC1B;AAEA,UAAM,iBAAiB;AAAA,MACrB,SAASG,YAAA,OAAAA,WAAW,mCAAS;AAAA,MAC7B,MAAMC,SAAA,OAAAA,QAAQ,mCAAS;AAAA,IACzB;AAEA,UAAM,cAA2B;AAAA,MAC/B,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAkB;AAAA,MACjD,SAAS;AAAA,MACT,SAAS,eAAe;AAAA,MACxB,MAAM,eAAe;AAAA,MACrB,MAAAF;AAAA,MACA,GAAI,cAAc,UAAa,EAAE,UAAU;AAAA,MAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,MACnD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,MACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,IACjD;AACA,WAAO,eAAe,WAAW;AAAA,EACnC;AAEA,QAAM,SAAmC,OAAO;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAAA;AAAA,IACA,SAAAC;AAAA,IACA,MAAAC;AAAA,EACF,IAAwB,CAAC,MAAM;AAC7B,UAAM,mBAAmB,IAAI,QAAQ;AACrC,QAAI,iBAAiB,WAAW;AAAG,aAAO;AAE1C,UAAM,iBAAiB;AAAA,MACrB,SAASD,YAAA,OAAAA,WAAW,mCAAS;AAAA,MAC7B,MAAMC,SAAA,OAAAA,QAAQ,mCAAS;AAAA,IACzB;AAGA,UAAM,cAAc,iBAAiB,GAAG,EAAE;AAC1C,SAAI,2CAAa,UAAS,aAAa;AACrC,YAAMC,eAA2B;AAAA,QAC/B,UAAU,iBAAiB,MAAM,GAAG,EAAE;AAAA,QACtC,SAAS;AAAA,QACT,SAAS,eAAe;AAAA,QACxB,MAAM,eAAe;AAAA,QACrB,MAAAH;AAAA,QACA,GAAI,cAAc,UAAa,EAAE,UAAU;AAAA,QAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,QACnD,GAAI,UAAU,UAAa,EAAE,MAAM;AAAA,QACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MACjD;AAEA,aAAO,eAAeG,YAAW;AAAA,IACnC;AAEA,UAAM,cAA2B;AAAA,MAC/B,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS,eAAe;AAAA,MACxB,MAAM,eAAe;AAAA,MACrB,MAAAH;AAAA,IACF;AAEA,WAAO,eAAe,WAAW;AAAA,EACnC;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,cAAc,CAClB,gBACG;AACH,QAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAc,YAAY,IAAI,QAAQ,CAAC;AAAA,IACzC;AAEA,WAAO,WAAW;AAAA,EACpB;AAEA,QAAM,UAAU,CACd,YAIG;AACH,QAAI,OAAO,YAAY,YAAY;AACjC,gBAAU,QAAQ,IAAI,UAAU,CAAC;AAAA,IACnC;AAEA,eAAW,IAAI,OAAO;AAAA,EACxB;AAEA,QAAM,QAAQ,SAAS,YAAY;AAEnC,QAAM,eAAe,CACnB,OACA,UAA8B,CAAC,MAC5B;AA1fP;AA2fI,yCAAO,mBAAP;AACA,UAAM,aAAa,IAAI,KAAK;AAE5B,QAAI,CAAC,cAAc,CAAC,QAAQ;AAAkB;AAE9C,UAAM,iBAAiB;AAAA,MACrB,UAAS,aAAQ,YAAR,aAAmB,aAAQ,YAAR,mBAAiB;AAAA,MAC7C,OAAM,aAAQ,SAAR,aAAgB,aAAQ,YAAR,mBAAiB;AAAA,IACzC;AAEA,UAAM,cAA2B;AAAA,MAC/B,UACE,CAAC,cAAc,QAAQ,mBACnB,IAAI,QAAQ,IACZ,IAAI,QAAQ,EAAE,OAAO;AAAA,QACnB,IAAIF,YAAW;AAAA,QACf,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW,oBAAI,KAAK;AAAA,MACtB,CAAY;AAAA,MAClB,SAAS;AAAA,MACT,MAAM,eAAe;AAAA,MACrB,SAAS,eAAe;AAAA,MACxB,MAAM,QAAQ;AAAA,IAChB;AAEA,mBAAe,WAAW;AAE1B,UAAM,IAAI,EAAE;AAAA,EACd;AAEA,QAAM,YAAY;AAAA,IAChB,CAAC,cAAc,OAAO;AAAA,IACtB,CAAC,CAAC,eAAe,QAAQ,MAAM;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAAA,EACF;AAEA,QAAM,gBAAgB,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF,MAGM;AAviBR;AAwiBI,UAAM,oBAAmB,SAAI,QAAQ,MAAZ,YAAiB,CAAC;AAC3C,UAAM,kBAAkB,iBAAiB;AAAA,MAAI,CAAC,SAAS,OAAO;AAAA;AAAA,QAE5D,UAAU,IAAI,SAAS,KACvB,QAAQ,SAAS,eACjB,QAAQ,kBACJ;AAAA,UACE,GAAG;AAAA,UACH,iBAAiB,QAAQ,gBAAgB;AAAA,YAAI,oBAC3C,eAAe,eAAe,aAC1B,EAAE,GAAG,gBAAgB,OAAO,IAC5B;AAAA,UACN;AAAA,QACF,IACA;AAAA;AAAA,IACN;AAEA,aAAS,IAAI,eAAe;AAG5B,UAAM,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAE9D,QAAI,yCAAyC,WAAW,GAAG;AACzD,qBAAe,EAAE,UAAU,gBAAgB,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;;;AC5kBA,SAAS,yBAAyB;AAClC,SAAS,UAAAM,eAAc;AACvB,SAA6B,WAAAC,UAAS,OAAAC,MAAK,YAAAC,iBAAgB;AA2C3D,IAAIC,YAAW;AAEf,IAAMC,SAA6B,CAAC;AAE7B,SAAS,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN;AAAA,EACA,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC;AACF,IAA0B,CAAC,GAAyB;AAElD,MAAI,YAAY;AACd,+DAAmB,eAAe,SAAS,SAAS;AAAA,EACtD;AAGA,QAAM,eAAe,MAAM,cAAcF,WAAU;AAEnD,QAAM,MAAM,GAAG,GAAG,IAAI,YAAY;AAClC,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,IAAIJ,QAAe,KAAK;AAAA,IACtB,SAAS,MAAMK,OAAM,GAAG,KAAK;AAAA,IAC7B,cAAc;AAAA,EAChB,CAAC;AAED,QAAM,aAAaF,UAAkC,MAAS;AAE9D,QAAM,UAAUA,UAAkB,KAAK;AAGvC,OAAK,IAAI,iBAAiB;AAE1B,QAAM,SAAS,CAACI,UAAiB;AAC/B,IAAAF,OAAM,GAAG,IAAIE;AACb,WAAO,eAAeA,KAAI;AAAA,EAC5B;AAGA,QAAM,aAAa;AAEnB,QAAM,QAAQJ,UAA4B,MAAS;AAEnD,MAAI,kBAA0C;AAE9C,QAAM,WAA6C,OACjD,QACA,YACG;AACH,UAAM,eAAeD,KAAI,UAAU;AACnC,WAAO,kBAAkB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,mCAAS;AAAA,MACd;AAAA,MACA,MAAM;AAAA,QACJ,GAAG;AAAA,QACH,GAAG,mCAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,YAAY,kBAAgB,QAAQ,IAAI,YAAY;AAAA,MACpD,UAAU,SAAO,MAAM,IAAI,GAAG;AAAA,MAC9B,oBAAoB,gBAAc;AAChC,0BAAkB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAOK,OAAM;AACX,mBAAW,IAAI,CAAC,GAAI,gBAAgB,CAAC,GAAI,GAAIA,SAAQ,CAAC,CAAE,CAAC;AAAA,MAC3D;AAAA,MACA,OAAAD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,gBAAgB,CAACE,gBAAuB;AAC5C,WAAOA,WAAU;AAAA,EACnB;AAEA,QAAM,QAAQL,UAAS,YAAY;AAEnC,QAAM,eAAe,CAAC,UAA4C;AAzJpE;AA0JI,yCAAO,mBAAP;AAEA,UAAM,aAAaD,KAAI,KAAK;AAC5B,WAAO,aAAa,SAAS,UAAU,IAAI;AAAA,EAC7C;AAEA,QAAM,YAAYD;AAAA,IAChB,CAAC,cAAc,OAAO;AAAA,IACtB,CAAC,CAAC,eAAe,QAAQ,MAAM;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;;;AClLA,SAAS,oBAAoB;AAO7B,SAAS,YAAY,sBAAsB;AAC3C,SAA6B,OAAAQ,MAAK,YAAAC,iBAAgB;AAGlD,IAAM,mBAAmB,MAAM;AAE/B,IAAIC,YAAW;AAEf,IAAMC,SAA6B,CAAC;AA0D7B,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC;AACF,GAA6C;AAE3C,QAAM,gBAAgBH,UAA6B,aAAa;AAGhE,QAAM,MAAM,GAAG,GAAG,IAAI,wCAAiB,cAAcC,WAAU,EAAE;AACjE,QAAM,WAAWD,UAAoBE,OAAM,GAAG,KAAK,CAAC,CAAC;AACrD,QAAM,QAAQF,UAAS,EAAE;AACzB,QAAM,SAASA,UAA0B,kBAAkB;AAC3D,QAAM,QAAQA,UAA4B,MAAS;AAGnD,MAAI,kBAA0C;AAG9C,QAAM,iBAAiB,CAAC,gBAA2B;AACjD,IAAAE,OAAM,GAAG,IAAI;AACb,aAAS,IAAI,WAAW;AAAA,EAC1B;AAGA,iBAAe,OACb,SACA,gBACA;AAzGJ;AA0GI,WAAO,IAAI,aAAa;AACxB,sBAAkB,IAAI,gBAAgB;AAGtC,mBAAe;AAAA,MACb,GAAGH,KAAI,QAAQ;AAAA,MACf,EAAE,GAAG,SAAS,KAAI,aAAQ,OAAR,YAAc,WAAW,EAAE;AAAA,IAC/C,CAAC;AAED,UAAM,IAAI,EAAE;AAEZ,QAAI;AACF,YAAM,cAAcI,UAAA,OAAAA,SAAS,iBAAiB;AAC9C,YAAM,WAAW,MAAM,YAAY,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,SAAS,EAAE,gBAAgB,oBAAoB,GAAG,QAAQ;AAAA,QAC1D,MAAM,KAAK,UAAU;AAAA,UACnB,GAAG;AAAA;AAAA,UAEH,WAAU,6CAAiBJ,KAAI,aAAa,MAAlC,YAAuC;AAAA,UACjD,SAAS,QAAQ;AAAA;AAAA,UAGjB,MAAM,iDAAgB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,MAAM;AACzB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAGA,uBAAiB,EAAE,MAAM,MAAM,KAAK;AAAA,QAClC,SAAS,KAAK,UAAU;AAAA,MAC1B,GAAG;AACD,gBAAQ,MAAM;AAAA,UACZ,KAAK,qBAAqB;AACxB,2BAAe;AAAA,cACb,GAAGA,KAAI,QAAQ;AAAA,cACf;AAAA,gBACE,IAAI,MAAM;AAAA,gBACV,MAAM,MAAM;AAAA,gBACZ,SAAS,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,cACjC;AAAA,YACF,CAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,QAAQ;AAEX;AAAA,cACEA,KAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,OAAO,UAAU;AACvC,oBAAI,UAAU,MAAM,SAAS,GAAG;AAC9B,yBAAO,EAAE,GAAG,KAAK,SAAS,IAAI,UAAU,MAAM;AAAA,gBAChD;AACA,uBAAO;AAAA,cACT,CAAC;AAAA,YACH;AACA;AAAA,UACF;AAAA,UAEA,KAAK,gBAAgB;AACnB,2BAAe;AAAA,cACb,GAAGA,KAAI,QAAQ;AAAA,cACf;AAAA,gBACE,KAAI,WAAM,OAAN,YAAY,WAAW;AAAA,gBAC3B,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,MAAM,MAAM;AAAA,cACd;AAAA,YACF,CAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,0BAA0B;AAC7B,0BAAc,IAAI,MAAM,QAAQ;AAEhC;AAAA,cACEA,KAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,OAAO,UAAU;AACvC,oBAAI,UAAU,MAAM,SAAS,GAAG;AAC9B,yBAAO,EAAE,GAAG,KAAK,IAAI,MAAM,UAAU;AAAA,gBACvC;AACA,uBAAO;AAAA,cACT,CAAC;AAAA,YACH;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,SAAS;AACZ,kBAAM,IAAI,IAAI,MAAM,KAAK,CAAC;AAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AAEZ,UAAI,aAAa,KAAK,OAAK,wDAAiB,WAAjB,mBAAyB,UAAS;AAC3D,0BAAkB;AAClB;AAAA,MACF;AAEA,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;AAAA,MACb;AAEA,YAAM,IAAI,GAAY;AAAA,IACxB,UAAE;AACA,wBAAkB;AAClB,aAAO,IAAI,kBAAkB;AAAA,IAC/B;AAAA,EACF;AAEA,WAAS,YAAYK,WAAqB;AACxC,mBAAeA,SAAQ;AAAA,EACzB;AAEA,WAAS,OAAO;AACd,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAGA,iBAAe,cACb,OACA,gBACA;AAlPJ;AAmPI,yCAAO,mBAAP;AACA,UAAM,aAAaL,KAAI,KAAK;AAC5B,QAAI,CAAC;AAAY;AAEjB,UAAM,OAAO,EAAE,MAAM,QAAQ,SAAS,WAAW,GAAG,cAAc;AAAA,EACpE;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["generateId", "fetch", "data", "headers", "body", "chatRequest", "useSWR", "derived", "get", "writable", "uniqueId", "store", "fetch", "data", "completion", "get", "writable", "uniqueId", "store", "fetch", "messages"]}