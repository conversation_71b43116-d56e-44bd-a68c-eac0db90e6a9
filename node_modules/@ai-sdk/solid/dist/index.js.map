{"version": 3, "sources": ["../src/index.ts", "../src/use-chat.ts", "../src/use-completion.ts"], "sourcesContent": ["export * from './use-chat';\nexport * from './use-completion';\n", "import { FetchFunction } from '@ai-sdk/provider-utils';\nimport type {\n  ChatRequest,\n  ChatRequestOptions,\n  CreateMessage,\n  IdGenerator,\n  JSONValue,\n  Message,\n  UseChatOptions as SharedUseChatOptions,\n} from '@ai-sdk/ui-utils';\nimport {\n  callChatApi,\n  generateId as generateIdFunc,\n  processChatStream,\n} from '@ai-sdk/ui-utils';\nimport {\n  Accessor,\n  JSX,\n  Setter,\n  createEffect,\n  createMemo,\n  createSignal,\n  createUniqueId,\n} from 'solid-js';\nimport { createStore } from 'solid-js/store';\n\nexport type { CreateMessage, Message };\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: Accessor<Message[]>;\n  /** The error object of the API request */\n  error: Accessor<undefined | Error>;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param options Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (\n    messages: Message[] | ((messages: Message[]) => Message[]),\n  ) => void;\n  /** The current value of the input */\n  input: Accessor<string>;\n  /** Signal setter to update the input value */\n  setInput: Setter<string>;\n  /** An input/textarea-ready onChange handler to control the value of the input */\n  handleInputChange: JSX.ChangeEventHandlerUnion<\n    HTMLInputElement | HTMLTextAreaElement,\n    Event\n  >;\n  /** Form submission handler to automatically reset input and append a user message */\n  handleSubmit: (\n    event?: { preventDefault?: () => void },\n    chatRequestOptions?: ChatRequestOptions,\n  ) => void;\n  /** Whether the API request is in progress */\n  isLoading: Accessor<boolean>;\n\n  /** Additional data added on the server via StreamData */\n  data: Accessor<JSONValue[] | undefined>;\n  /** Set the data of the chat. You can use this to transform or clear the chat data. */\n  setData: (\n    data:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => void;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n};\n\nconst getStreamedResponse = async (\n  api: string,\n  chatRequest: ChatRequest,\n  mutate: (data: Message[]) => void,\n  setStreamData: Setter<JSONValue[] | undefined>,\n  streamData: Accessor<JSONValue[] | undefined>,\n  extraMetadata: any,\n  messagesRef: Message[],\n  abortController: AbortController | null,\n  generateId: IdGenerator,\n  streamProtocol: UseChatOptions['streamProtocol'],\n  onFinish: UseChatOptions['onFinish'],\n  onResponse: UseChatOptions['onResponse'] | undefined,\n  onToolCall: UseChatOptions['onToolCall'] | undefined,\n  sendExtraMessageFields: boolean | undefined,\n  fetch: FetchFunction | undefined,\n  keepLastMessageOnError: boolean,\n) => {\n  // Do an optimistic update to the chat state to show the updated messages\n  // immediately.\n  const previousMessages = messagesRef;\n\n  mutate(chatRequest.messages);\n\n  const existingStreamData = streamData() ?? [];\n\n  const constructedMessagesPayload = sendExtraMessageFields\n    ? chatRequest.messages\n    : chatRequest.messages.map(\n        ({ role, content, name, data, annotations, toolInvocations }) => ({\n          role,\n          content,\n          ...(name !== undefined && { name }),\n          ...(data !== undefined && { data }),\n          ...(annotations !== undefined && { annotations }),\n          ...(toolInvocations !== undefined && { toolInvocations }),\n        }),\n      );\n\n  return await callChatApi({\n    api,\n    body: {\n      messages: constructedMessagesPayload,\n      data: chatRequest.data,\n      ...extraMetadata.body,\n      ...chatRequest.body,\n    },\n    streamProtocol,\n    credentials: extraMetadata.credentials,\n    headers: {\n      ...extraMetadata.headers,\n      ...chatRequest.headers,\n    },\n    abortController: () => abortController,\n    restoreMessagesOnFailure() {\n      if (!keepLastMessageOnError) {\n        mutate(previousMessages);\n      }\n    },\n    onResponse,\n    onUpdate(merged, data) {\n      mutate([...chatRequest.messages, ...merged]);\n      setStreamData([...existingStreamData, ...(data ?? [])]);\n    },\n    onToolCall,\n    onFinish,\n    generateId,\n    fetch,\n  });\n};\n\n// This store saves the messages for each chat ID\nconst [store, setStore] = createStore<Record<string, Message[]>>({});\n\nexport type UseChatOptions = SharedUseChatOptions & {\n  /**\nMaximum number of automatic roundtrips for tool calls.\n\nAn automatic tool call roundtrip is a call to the server with the\ntool call results when all tool calls in the last assistant\nmessage have results.\n\nA maximum number is required to prevent infinite loops in the\ncase of misconfigured tools.\n\nBy default, it's set to 0, which will disable the feature.\n\n@deprecated Use `maxSteps` instead (which is `maxToolRoundtrips` + 1).\n     */\n  maxToolRoundtrips?: number;\n\n  /**\nMaximum number of sequential LLM calls (steps), e.g. when you use tool calls. Must be at least 1.\n\nA maximum number is required to prevent infinite loops in the case of misconfigured tools.\n\nBy default, it's set to 1, which means that only a single LLM call is made.\n*/\n  maxSteps?: number;\n};\n\nexport function useChat(\n  rawUseChatOptions: UseChatOptions | Accessor<UseChatOptions> = {},\n): UseChatHelpers & {\n  addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n} {\n  const useChatOptions = createMemo(() =>\n    convertToAccessorOptions(rawUseChatOptions),\n  );\n\n  const api = createMemo(() => useChatOptions().api?.() ?? '/api/chat');\n  const generateId = createMemo(\n    () => useChatOptions().generateId?.() ?? generateIdFunc,\n  );\n\n  const idKey = createMemo(\n    () => useChatOptions().id?.() ?? `chat-${createUniqueId()}`,\n  );\n  const chatKey = createMemo(() => `${api()}|${idKey()}|messages`);\n\n  const messages = createMemo(() => {\n    return store[chatKey()] ?? useChatOptions().initialMessages?.() ?? [];\n  });\n\n  const mutate = (data: Message[]) => {\n    setStore(chatKey(), data);\n  };\n\n  const [error, setError] = createSignal<undefined | Error>(undefined);\n  const [streamData, setStreamData] = createSignal<JSONValue[] | undefined>(\n    undefined,\n  );\n  const [isLoading, setIsLoading] = createSignal(false);\n\n  let messagesRef: Message[] = messages() || [];\n  createEffect(() => {\n    messagesRef = messages() || [];\n  });\n\n  let abortController: AbortController | null = null;\n\n  let extraMetadata = {\n    credentials: useChatOptions().credentials?.(),\n    headers: useChatOptions().headers?.(),\n    body: useChatOptions().body?.(),\n  };\n  createEffect(() => {\n    extraMetadata = {\n      credentials: useChatOptions().credentials?.(),\n      headers: useChatOptions().headers?.(),\n      body: useChatOptions().body?.(),\n    };\n  });\n\n  const triggerRequest = async (chatRequest: ChatRequest) => {\n    const messageCount = messagesRef.length;\n\n    try {\n      setError(undefined);\n      setIsLoading(true);\n\n      abortController = new AbortController();\n\n      await processChatStream({\n        getStreamedResponse: () =>\n          getStreamedResponse(\n            api(),\n            chatRequest,\n            mutate,\n            setStreamData,\n            streamData,\n            extraMetadata,\n            messagesRef,\n            abortController,\n            generateId(),\n            // streamMode is deprecated, use streamProtocol instead:\n            useChatOptions().streamProtocol?.() ??\n              useChatOptions().streamMode?.() === 'text'\n              ? 'text'\n              : undefined,\n            useChatOptions().onFinish?.(),\n            useChatOptions().onResponse?.(),\n            useChatOptions().onToolCall?.(),\n            useChatOptions().sendExtraMessageFields?.(),\n            useChatOptions().fetch?.(),\n            useChatOptions().keepLastMessageOnError?.() ?? false,\n          ),\n        experimental_onFunctionCall:\n          useChatOptions().experimental_onFunctionCall?.(),\n        updateChatRequest(newChatRequest) {\n          chatRequest = newChatRequest;\n        },\n        getCurrentMessages: () => messagesRef,\n      });\n\n      abortController = null;\n    } catch (err) {\n      // Ignore abort errors as they are expected.\n      if ((err as any).name === 'AbortError') {\n        abortController = null;\n        return null;\n      }\n\n      const onError = useChatOptions().onError?.();\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      setError(err as Error);\n    } finally {\n      setIsLoading(false);\n    }\n\n    const maxSteps =\n      useChatOptions().maxSteps?.() ??\n      (useChatOptions().maxToolRoundtrips?.() ?? 0) + 1;\n    // auto-submit when all tool calls in the last assistant message have results:\n    const messages = messagesRef;\n    const lastMessage = messages[messages.length - 1];\n    if (\n      // ensure we actually have new messages (to prevent infinite loops in case of errors):\n      messages.length > messageCount &&\n      // ensure there is a last message:\n      lastMessage != null &&\n      // check if the feature is enabled:\n      maxSteps > 1 &&\n      // check that next step is possible:\n      isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n      // limit the number of automatic steps:\n      countTrailingAssistantMessages(messages) < maxSteps\n    ) {\n      await triggerRequest({ messages });\n    }\n  };\n\n  const append: UseChatHelpers['append'] = async (\n    message,\n    { options, data, headers, body } = {},\n  ) => {\n    if (!message.id) {\n      message.id = generateId()();\n    }\n\n    const requestOptions = {\n      headers: headers ?? options?.headers,\n      body: body ?? options?.body,\n    };\n\n    const chatRequest: ChatRequest = {\n      messages: messagesRef.concat(message as Message),\n      options: requestOptions,\n      headers: requestOptions.headers,\n      body: requestOptions.body,\n      data,\n    };\n\n    return triggerRequest(chatRequest);\n  };\n\n  const reload: UseChatHelpers['reload'] = async ({\n    options,\n    data,\n    headers,\n    body,\n  } = {}) => {\n    if (messagesRef.length === 0) return null;\n\n    const requestOptions = {\n      headers: headers ?? options?.headers,\n      body: body ?? options?.body,\n    };\n\n    // Remove last assistant message and retry last user message.\n    const lastMessage = messagesRef[messagesRef.length - 1];\n    if (lastMessage.role === 'assistant') {\n      const chatRequest: ChatRequest = {\n        messages: messagesRef.slice(0, -1),\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n      };\n\n      return triggerRequest(chatRequest);\n    }\n\n    const chatRequest: ChatRequest = {\n      messages: messagesRef,\n      options: requestOptions,\n      headers: requestOptions.headers,\n      body: requestOptions.body,\n      data,\n    };\n\n    return triggerRequest(chatRequest);\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setMessages = (\n    messagesArg: Message[] | ((messages: Message[]) => Message[]),\n  ) => {\n    if (typeof messagesArg === 'function') {\n      messagesArg = messagesArg(messagesRef);\n    }\n\n    mutate(messagesArg);\n    messagesRef = messagesArg;\n  };\n\n  const setData = (\n    dataArg:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => {\n    if (typeof dataArg === 'function') {\n      dataArg = dataArg(streamData());\n    }\n\n    setStreamData(dataArg);\n  };\n\n  const [input, setInput] = createSignal(\n    useChatOptions().initialInput?.() || '',\n  );\n\n  const handleSubmit: UseChatHelpers['handleSubmit'] = (\n    event,\n    options = {},\n    metadata?: Object,\n  ) => {\n    event?.preventDefault?.();\n    const inputValue = input();\n\n    if (!inputValue && !options.allowEmptySubmit) return;\n\n    if (metadata) {\n      extraMetadata = {\n        ...extraMetadata,\n        ...metadata,\n      };\n    }\n\n    const requestOptions = {\n      headers: options.headers ?? options.options?.headers,\n      body: options.body ?? options.options?.body,\n    };\n\n    const chatRequest: ChatRequest = {\n      messages:\n        !inputValue && options.allowEmptySubmit\n          ? messagesRef\n          : messagesRef.concat({\n              id: generateId()(),\n              role: 'user',\n              content: inputValue,\n              createdAt: new Date(),\n            }),\n      options: requestOptions,\n      body: requestOptions.body,\n      headers: requestOptions.headers,\n      data: options.data,\n    };\n\n    triggerRequest(chatRequest);\n\n    setInput('');\n  };\n\n  const handleInputChange: UseChatHelpers['handleInputChange'] = e => {\n    setInput(e.target.value);\n  };\n\n  const addToolResult = ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => {\n    const messagesSnapshot = messages() ?? [];\n\n    const updatedMessages = messagesSnapshot.map((message, index, arr) =>\n      // update the tool calls in the last assistant message:\n      index === arr.length - 1 &&\n      message.role === 'assistant' &&\n      message.toolInvocations\n        ? {\n            ...message,\n            toolInvocations: message.toolInvocations.map(toolInvocation =>\n              toolInvocation.toolCallId === toolCallId\n                ? { ...toolInvocation, result }\n                : toolInvocation,\n            ),\n          }\n        : message,\n    );\n\n    mutate(updatedMessages);\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const lastMessage = updatedMessages[updatedMessages.length - 1];\n    if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n      triggerRequest({ messages: updatedMessages });\n    }\n  };\n\n  return {\n    messages,\n    append,\n    error,\n    reload,\n    stop,\n    setMessages,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n    setData,\n    addToolResult,\n  };\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe message must have at least one tool invocation and all tool invocations\nmust have a result.\n */\nfunction isAssistantMessageWithCompletedToolCalls(message: Message) {\n  return (\n    message.role === 'assistant' &&\n    message.toolInvocations &&\n    message.toolInvocations.length > 0 &&\n    message.toolInvocations.every(toolInvocation => 'result' in toolInvocation)\n  );\n}\n\n/**\nReturns the number of trailing assistant messages in the array.\n */\nfunction countTrailingAssistantMessages(messages: Message[]) {\n  let count = 0;\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === 'assistant') {\n      count++;\n    } else {\n      break;\n    }\n  }\n  return count;\n}\n\n/**\n * Handle reactive and non-reactive useChatOptions\n */\nfunction convertToAccessorOptions(\n  options: UseChatOptions | Accessor<UseChatOptions>,\n) {\n  const resolvedOptions = typeof options === 'function' ? options() : options;\n\n  return Object.entries(resolvedOptions).reduce(\n    (reactiveOptions, [key, value]) => {\n      reactiveOptions[key as keyof UseChatOptions] = createMemo(\n        () => value,\n      ) as any;\n      return reactiveOptions;\n    },\n    {} as {\n      [K in keyof UseChatOptions]: Accessor<UseChatOptions[K]>;\n    },\n  );\n}\n", "import { FetchFunction } from '@ai-sdk/provider-utils';\nimport type {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n} from '@ai-sdk/ui-utils';\nimport { callCompletionApi } from '@ai-sdk/ui-utils';\nimport {\n  Accessor,\n  JS<PERSON>,\n  Setter,\n  createEffect,\n  createMemo,\n  createSignal,\n  createUniqueId,\n} from 'solid-js';\nimport { createStore } from 'solid-js/store';\n\nexport type { UseCompletionOptions };\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: Accessor<string>;\n  /** The error object of the API request */\n  error: Accessor<undefined | Error>;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: Accessor<string>;\n  /** Signal Setter to update the input value */\n  setInput: Setter<string>;\n\n  /** An input/textarea-ready onChange handler to control the value of the input */\n  handleInputChange: JSX.ChangeEventHandlerUnion<\n    HTMLInputElement | HTMLTextAreaElement,\n    Event\n  >;\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form onSubmit={handleSubmit}>\n   *  <input value={input()} />\n   * </form>\n   * ```\n   */\n  handleSubmit: (event?: { preventDefault?: () => void }) => void;\n  /** Whether the API request is in progress */\n  isLoading: Accessor<boolean>;\n  /** Additional data added on the server via StreamData */\n  data: Accessor<JSONValue[] | undefined>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n};\n\nconst [store, setStore] = createStore<Record<string, string>>({});\n\nexport function useCompletion(\n  rawUseCompletionOptions:\n    | UseCompletionOptions\n    | Accessor<UseCompletionOptions> = {},\n): UseCompletionHelpers {\n  const useCompletionOptions = createMemo(() =>\n    convertToAccessorOptions(rawUseCompletionOptions),\n  );\n\n  const api = createMemo(\n    () => useCompletionOptions().api?.() ?? '/api/completion',\n  );\n  // Generate an unique id for the completion if not provided.\n  const idKey = createMemo(\n    () => useCompletionOptions().id?.() ?? `completion-${createUniqueId()}`,\n  );\n  const completionKey = createMemo(() => `${api()}|${idKey()}|completion`);\n\n  const completion = createMemo(\n    () =>\n      store[completionKey()] ?? useCompletionOptions().initialCompletion?.(),\n  );\n\n  const mutate = (data: string) => {\n    setStore(completionKey(), data);\n  };\n\n  const [error, setError] = createSignal<undefined | Error>(undefined);\n  const [streamData, setStreamData] = createSignal<JSONValue[] | undefined>(\n    undefined,\n  );\n  const [isLoading, setIsLoading] = createSignal(false);\n\n  const [abortController, setAbortController] =\n    createSignal<AbortController | null>(null);\n\n  let extraMetadata = {\n    credentials: useCompletionOptions().credentials?.(),\n    headers: useCompletionOptions().headers?.(),\n    body: useCompletionOptions().body?.(),\n  };\n  createEffect(() => {\n    extraMetadata = {\n      credentials: useCompletionOptions().credentials?.(),\n      headers: useCompletionOptions().headers?.(),\n      body: useCompletionOptions().body?.(),\n    };\n  });\n\n  const complete: UseCompletionHelpers['complete'] = async (\n    prompt: string,\n    options?: RequestOptions,\n  ) => {\n    const existingData = streamData() ?? [];\n    return callCompletionApi({\n      api: api(),\n      prompt,\n      credentials: useCompletionOptions().credentials?.(),\n      headers: { ...extraMetadata.headers, ...options?.headers },\n      body: {\n        ...extraMetadata.body,\n        ...options?.body,\n      },\n      // streamMode is deprecated, use streamProtocol instead:\n      streamProtocol:\n        useCompletionOptions().streamProtocol?.() ??\n        useCompletionOptions().streamMode?.() === 'text'\n          ? 'text'\n          : undefined,\n      setCompletion: mutate,\n      setLoading: setIsLoading,\n      setError,\n      setAbortController,\n      onResponse: useCompletionOptions().onResponse?.(),\n      onFinish: useCompletionOptions().onFinish?.(),\n      onError: useCompletionOptions().onError?.(),\n      onData: data => {\n        setStreamData([...existingData, ...(data ?? [])]);\n      },\n      fetch: useCompletionOptions().fetch?.(),\n    });\n  };\n\n  const stop = () => {\n    if (abortController()) {\n      abortController()!.abort();\n    }\n  };\n\n  const setCompletion = (completion: string) => {\n    mutate(completion);\n  };\n\n  const [input, setInput] = createSignal(\n    useCompletionOptions().initialInput?.() ?? '',\n  );\n\n  const handleInputChange: UseCompletionHelpers['handleInputChange'] =\n    event => {\n      setInput(event.target.value);\n    };\n\n  const handleSubmit: UseCompletionHelpers['handleSubmit'] = event => {\n    event?.preventDefault?.();\n\n    const inputValue = input();\n    return inputValue ? complete(inputValue) : undefined;\n  };\n\n  return {\n    completion,\n    complete,\n    error,\n    stop,\n    setCompletion,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n\n/**\n * Handle reactive and non-reactive useChatOptions\n */\nfunction convertToAccessorOptions(\n  options: UseCompletionOptions | Accessor<UseCompletionOptions>,\n) {\n  const resolvedOptions = typeof options === 'function' ? options() : options;\n\n  return Object.entries(resolvedOptions).reduce(\n    (reactiveOptions, [key, value]) => {\n      reactiveOptions[key as keyof UseCompletionOptions] = createMemo(\n        () => value,\n      ) as any;\n      return reactiveOptions;\n    },\n    {} as {\n      [K in keyof UseCompletionOptions]: Accessor<UseCompletionOptions[K]>;\n    },\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACUA,sBAIO;AACP,sBAQO;AACP,mBAA4B;AAyE5B,IAAM,sBAAsB,OAC1B,KACA,aACA,QACA,eACA,YACA,eACA,aACA,iBACA,YACA,gBACA,UACA,YACA,YACA,wBACA,OACA,2BACG;AAlHL;AAqHE,QAAM,mBAAmB;AAEzB,SAAO,YAAY,QAAQ;AAE3B,QAAM,sBAAqB,gBAAW,MAAX,YAAgB,CAAC;AAE5C,QAAM,6BAA6B,yBAC/B,YAAY,WACZ,YAAY,SAAS;AAAA,IACnB,CAAC,EAAE,MAAM,SAAS,MAAM,MAAM,aAAa,gBAAgB,OAAO;AAAA,MAChE;AAAA,MACA;AAAA,MACA,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,MACjC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,MAC/C,GAAI,oBAAoB,UAAa,EAAE,gBAAgB;AAAA,IACzD;AAAA,EACF;AAEJ,SAAO,UAAM,6BAAY;AAAA,IACvB;AAAA,IACA,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,MAAM,YAAY;AAAA,MAClB,GAAG,cAAc;AAAA,MACjB,GAAG,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,IACA,aAAa,cAAc;AAAA,IAC3B,SAAS;AAAA,MACP,GAAG,cAAc;AAAA,MACjB,GAAG,YAAY;AAAA,IACjB;AAAA,IACA,iBAAiB,MAAM;AAAA,IACvB,2BAA2B;AACzB,UAAI,CAAC,wBAAwB;AAC3B,eAAO,gBAAgB;AAAA,MACzB;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,MAAM;AACrB,aAAO,CAAC,GAAG,YAAY,UAAU,GAAG,MAAM,CAAC;AAC3C,oBAAc,CAAC,GAAG,oBAAoB,GAAI,sBAAQ,CAAC,CAAE,CAAC;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGA,IAAM,CAAC,OAAO,QAAQ,QAAI,0BAAuC,CAAC,CAAC;AA6B5D,SAAS,QACd,oBAA+D,CAAC,GAShE;AAhNF;AAiNE,QAAM,qBAAiB;AAAA,IAAW,MAChC,yBAAyB,iBAAiB;AAAA,EAC5C;AAEA,QAAM,UAAM,4BAAW,MAAG;AArN5B,QAAAA,KAAAC,KAAAC;AAqN+B,YAAAA,OAAAD,OAAAD,MAAA,eAAe,GAAE,QAAjB,gBAAAC,IAAA,KAAAD,SAAA,OAAAE,MAA4B;AAAA,GAAW;AACpE,QAAM,iBAAa;AAAA,IACjB,MAAG;AAvNP,UAAAF,KAAAC,KAAAC;AAuNU,cAAAA,OAAAD,OAAAD,MAAA,eAAe,GAAE,eAAjB,gBAAAC,IAAA,KAAAD,SAAA,OAAAE,MAAmC,gBAAAC;AAAA;AAAA,EAC3C;AAEA,QAAM,YAAQ;AAAA,IACZ,MAAG;AA3NP,UAAAH,KAAAC,KAAAC;AA2NU,cAAAA,OAAAD,OAAAD,MAAA,eAAe,GAAE,OAAjB,gBAAAC,IAAA,KAAAD,SAAA,OAAAE,MAA2B,YAAQ,gCAAe,CAAC;AAAA;AAAA,EAC3D;AACA,QAAM,cAAU,4BAAW,MAAM,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,WAAW;AAE/D,QAAM,eAAW,4BAAW,MAAM;AA/NpC,QAAAF,KAAAC,KAAAC,KAAAE;AAgOI,YAAOA,OAAAF,MAAA,MAAM,QAAQ,CAAC,MAAf,OAAAA,OAAoBD,OAAAD,MAAA,eAAe,GAAE,oBAAjB,gBAAAC,IAAA,KAAAD,SAApB,OAAAI,MAA4D,CAAC;AAAA,EACtE,CAAC;AAED,QAAM,SAAS,CAAC,SAAoB;AAClC,aAAS,QAAQ,GAAG,IAAI;AAAA,EAC1B;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,8BAAgC,MAAS;AACnE,QAAM,CAAC,YAAY,aAAa,QAAI;AAAA,IAClC;AAAA,EACF;AACA,QAAM,CAAC,WAAW,YAAY,QAAI,8BAAa,KAAK;AAEpD,MAAI,cAAyB,SAAS,KAAK,CAAC;AAC5C,oCAAa,MAAM;AACjB,kBAAc,SAAS,KAAK,CAAC;AAAA,EAC/B,CAAC;AAED,MAAI,kBAA0C;AAE9C,MAAI,gBAAgB;AAAA,IAClB,cAAa,0BAAe,GAAE,gBAAjB;AAAA,IACb,UAAS,0BAAe,GAAE,YAAjB;AAAA,IACT,OAAM,0BAAe,GAAE,SAAjB;AAAA,EACR;AACA,oCAAa,MAAM;AAzPrB,QAAAJ,KAAAC,KAAAC,KAAAE,KAAAC,KAAAC;AA0PI,oBAAgB;AAAA,MACd,cAAaL,OAAAD,MAAA,eAAe,GAAE,gBAAjB,gBAAAC,IAAA,KAAAD;AAAA,MACb,UAASI,OAAAF,MAAA,eAAe,GAAE,YAAjB,gBAAAE,IAAA,KAAAF;AAAA,MACT,OAAMI,OAAAD,MAAA,eAAe,GAAE,SAAjB,gBAAAC,IAAA,KAAAD;AAAA,IACR;AAAA,EACF,CAAC;AAED,QAAM,iBAAiB,OAAO,gBAA6B;AAjQ7D,QAAAL,KAAAC,KAAAC,KAAAE,KAAAC,KAAAC,KAAAC,KAAAC,KAAA;AAkQI,UAAM,eAAe,YAAY;AAEjC,QAAI;AACF,eAAS,MAAS;AAClB,mBAAa,IAAI;AAEjB,wBAAkB,IAAI,gBAAgB;AAEtC,gBAAM,mCAAkB;AAAA,QACtB,qBAAqB,MAAG;AA3QhC,cAAAR,KAAAC,KAAAC,KAAAE,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAA;AA4QU;AAAA,YACE,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA;AAAA,cAEXL,OAAAJ,OAAAD,MAAA,eAAe,GAAE,mBAAjB,gBAAAC,IAAA,KAAAD,SAAA,OAAAK,QACED,OAAAF,MAAA,eAAe,GAAE,eAAjB,gBAAAE,IAAA,KAAAF,UAAoC,UAClC,SACA;AAAA,aACJK,OAAAD,MAAA,eAAe,GAAE,aAAjB,gBAAAC,IAAA,KAAAD;AAAA,aACAG,OAAAD,MAAA,eAAe,GAAE,eAAjB,gBAAAC,IAAA,KAAAD;AAAA,aACA,MAAAE,MAAA,eAAe,GAAE,eAAjB,wBAAAA;AAAA,aACA,0BAAe,GAAE,2BAAjB;AAAA,aACA,0BAAe,GAAE,UAAjB;AAAA,aACA,gCAAe,GAAE,2BAAjB,4CAA+C;AAAA,UACjD;AAAA;AAAA,QACF,8BACET,OAAAD,MAAA,eAAe,GAAE,gCAAjB,gBAAAC,IAAA,KAAAD;AAAA,QACF,kBAAkB,gBAAgB;AAChC,wBAAc;AAAA,QAChB;AAAA,QACA,oBAAoB,MAAM;AAAA,MAC5B,CAAC;AAED,wBAAkB;AAAA,IACpB,SAAS,KAAK;AAEZ,UAAK,IAAY,SAAS,cAAc;AACtC,0BAAkB;AAClB,eAAO;AAAA,MACT;AAEA,YAAM,WAAUI,OAAAF,MAAA,eAAe,GAAE,YAAjB,gBAAAE,IAAA,KAAAF;AAChB,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;AAAA,MACb;AAEA,eAAS,GAAY;AAAA,IACvB,UAAE;AACA,mBAAa,KAAK;AAAA,IACpB;AAEA,UAAM,YACJ,MAAAI,OAAAD,MAAA,eAAe,GAAE,aAAjB,gBAAAC,IAAA,KAAAD,SAAA,cACC,MAAAG,OAAAD,MAAA,eAAe,GAAE,sBAAjB,gBAAAC,IAAA,KAAAD,SAAA,YAA0C,KAAK;AAElD,UAAMI,YAAW;AACjB,UAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD;AAAA;AAAA,MAEEA,UAAS,SAAS;AAAA,MAElB,eAAe;AAAA,MAEf,WAAW;AAAA,MAEX,yCAAyC,WAAW;AAAA,MAEpD,+BAA+BA,SAAQ,IAAI;AAAA,MAC3C;AACA,YAAM,eAAe,EAAE,UAAAA,UAAS,CAAC;AAAA,IACnC;AAAA,EACF;AAEA,QAAM,SAAmC,OACvC,SACA,EAAE,SAAS,MAAM,SAAS,KAAK,IAAI,CAAC,MACjC;AACH,QAAI,CAAC,QAAQ,IAAI;AACf,cAAQ,KAAK,WAAW,EAAE;AAAA,IAC5B;AAEA,UAAM,iBAAiB;AAAA,MACrB,SAAS,4BAAW,mCAAS;AAAA,MAC7B,MAAM,sBAAQ,mCAAS;AAAA,IACzB;AAEA,UAAM,cAA2B;AAAA,MAC/B,UAAU,YAAY,OAAO,OAAkB;AAAA,MAC/C,SAAS;AAAA,MACT,SAAS,eAAe;AAAA,MACxB,MAAM,eAAe;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,eAAe,WAAW;AAAA,EACnC;AAEA,QAAM,SAAmC,OAAO;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM;AACT,QAAI,YAAY,WAAW;AAAG,aAAO;AAErC,UAAM,iBAAiB;AAAA,MACrB,SAAS,4BAAW,mCAAS;AAAA,MAC7B,MAAM,sBAAQ,mCAAS;AAAA,IACzB;AAGA,UAAM,cAAc,YAAY,YAAY,SAAS,CAAC;AACtD,QAAI,YAAY,SAAS,aAAa;AACpC,YAAMC,eAA2B;AAAA,QAC/B,UAAU,YAAY,MAAM,GAAG,EAAE;AAAA,QACjC,SAAS;AAAA,QACT,SAAS,eAAe;AAAA,QACxB,MAAM,eAAe;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,eAAeA,YAAW;AAAA,IACnC;AAEA,UAAM,cAA2B;AAAA,MAC/B,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS,eAAe;AAAA,MACxB,MAAM,eAAe;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,eAAe,WAAW;AAAA,EACnC;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,cAAc,CAClB,gBACG;AACH,QAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAc,YAAY,WAAW;AAAA,IACvC;AAEA,WAAO,WAAW;AAClB,kBAAc;AAAA,EAChB;AAEA,QAAM,UAAU,CACd,YAIG;AACH,QAAI,OAAO,YAAY,YAAY;AACjC,gBAAU,QAAQ,WAAW,CAAC;AAAA,IAChC;AAEA,kBAAc,OAAO;AAAA,EACvB;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI;AAAA,MACxB,0BAAe,GAAE,iBAAjB,gCAAqC;AAAA,EACvC;AAEA,QAAM,eAA+C,CACnD,OACA,UAAU,CAAC,GACX,aACG;AAvbP,QAAAZ,KAAAC,KAAAC,KAAAE,KAAAC;AAwbI,KAAAL,MAAA,+BAAO,mBAAP,gBAAAA,IAAA;AACA,UAAM,aAAa,MAAM;AAEzB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAAkB;AAE9C,QAAI,UAAU;AACZ,sBAAgB;AAAA,QACd,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAEA,UAAM,iBAAiB;AAAA,MACrB,UAASE,MAAA,QAAQ,YAAR,OAAAA,OAAmBD,MAAA,QAAQ,YAAR,gBAAAA,IAAiB;AAAA,MAC7C,OAAMI,MAAA,QAAQ,SAAR,OAAAA,OAAgBD,MAAA,QAAQ,YAAR,gBAAAA,IAAiB;AAAA,IACzC;AAEA,UAAM,cAA2B;AAAA,MAC/B,UACE,CAAC,cAAc,QAAQ,mBACnB,cACA,YAAY,OAAO;AAAA,QACjB,IAAI,WAAW,EAAE;AAAA,QACjB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW,oBAAI,KAAK;AAAA,MACtB,CAAC;AAAA,MACP,SAAS;AAAA,MACT,MAAM,eAAe;AAAA,MACrB,SAAS,eAAe;AAAA,MACxB,MAAM,QAAQ;AAAA,IAChB;AAEA,mBAAe,WAAW;AAE1B,aAAS,EAAE;AAAA,EACb;AAEA,QAAM,oBAAyD,OAAK;AAClE,aAAS,EAAE,OAAO,KAAK;AAAA,EACzB;AAEA,QAAM,gBAAgB,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF,MAGM;AAxeR,QAAAJ;AAyeI,UAAM,oBAAmBA,MAAA,SAAS,MAAT,OAAAA,MAAc,CAAC;AAExC,UAAM,kBAAkB,iBAAiB;AAAA,MAAI,CAAC,SAAS,OAAO;AAAA;AAAA,QAE5D,UAAU,IAAI,SAAS,KACvB,QAAQ,SAAS,eACjB,QAAQ,kBACJ;AAAA,UACE,GAAG;AAAA,UACH,iBAAiB,QAAQ,gBAAgB;AAAA,YAAI,oBAC3C,eAAe,eAAe,aAC1B,EAAE,GAAG,gBAAgB,OAAO,IAC5B;AAAA,UACN;AAAA,QACF,IACA;AAAA;AAAA,IACN;AAEA,WAAO,eAAe;AAGtB,UAAM,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAC9D,QAAI,yCAAyC,WAAW,GAAG;AACzD,qBAAe,EAAE,UAAU,gBAAgB,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,yCAAyC,SAAkB;AAClE,SACE,QAAQ,SAAS,eACjB,QAAQ,mBACR,QAAQ,gBAAgB,SAAS,KACjC,QAAQ,gBAAgB,MAAM,oBAAkB,YAAY,cAAc;AAE9E;AAKA,SAAS,+BAA+B,UAAqB;AAC3D,MAAI,QAAQ;AACZ,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AACpC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,yBACP,SACA;AACA,QAAM,kBAAkB,OAAO,YAAY,aAAa,QAAQ,IAAI;AAEpE,SAAO,OAAO,QAAQ,eAAe,EAAE;AAAA,IACrC,CAAC,iBAAiB,CAAC,KAAK,KAAK,MAAM;AACjC,sBAAgB,GAA2B,QAAI;AAAA,QAC7C,MAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EAGH;AACF;;;AChkBA,IAAAa,mBAAkC;AAClC,IAAAC,mBAQO;AACP,IAAAC,gBAA4B;AAwD5B,IAAM,CAACC,QAAOC,SAAQ,QAAI,2BAAoC,CAAC,CAAC;AAEzD,SAAS,cACd,0BAEqC,CAAC,GAChB;AA9ExB;AA+EE,QAAM,2BAAuB;AAAA,IAAW,MACtCC,0BAAyB,uBAAuB;AAAA,EAClD;AAEA,QAAM,UAAM;AAAA,IACV,MAAG;AApFP,UAAAC,KAAAC,KAAAC;AAoFU,cAAAA,OAAAD,OAAAD,MAAA,qBAAqB,GAAE,QAAvB,gBAAAC,IAAA,KAAAD,SAAA,OAAAE,MAAkC;AAAA;AAAA,EAC1C;AAEA,QAAM,YAAQ;AAAA,IACZ,MAAG;AAxFP,UAAAF,KAAAC,KAAAC;AAwFU,cAAAA,OAAAD,OAAAD,MAAA,qBAAqB,GAAE,OAAvB,gBAAAC,IAAA,KAAAD,SAAA,OAAAE,MAAiC,kBAAc,iCAAe,CAAC;AAAA;AAAA,EACvE;AACA,QAAM,oBAAgB,6BAAW,MAAM,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,aAAa;AAEvE,QAAM,iBAAa;AAAA,IACjB,MAAG;AA7FP,UAAAF,KAAAC,KAAAC;AA8FM,cAAAA,MAAAL,OAAM,cAAc,CAAC,MAArB,OAAAK,OAA0BD,OAAAD,MAAA,qBAAqB,GAAE,sBAAvB,gBAAAC,IAAA,KAAAD;AAAA;AAAA,EAC9B;AAEA,QAAM,SAAS,CAAC,SAAiB;AAC/B,IAAAF,UAAS,cAAc,GAAG,IAAI;AAAA,EAChC;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,+BAAgC,MAAS;AACnE,QAAM,CAAC,YAAY,aAAa,QAAI;AAAA,IAClC;AAAA,EACF;AACA,QAAM,CAAC,WAAW,YAAY,QAAI,+BAAa,KAAK;AAEpD,QAAM,CAAC,iBAAiB,kBAAkB,QACxC,+BAAqC,IAAI;AAE3C,MAAI,gBAAgB;AAAA,IAClB,cAAa,gCAAqB,GAAE,gBAAvB;AAAA,IACb,UAAS,gCAAqB,GAAE,YAAvB;AAAA,IACT,OAAM,gCAAqB,GAAE,SAAvB;AAAA,EACR;AACA,qCAAa,MAAM;AAnHrB,QAAAE,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC;AAoHI,oBAAgB;AAAA,MACd,cAAaJ,OAAAD,MAAA,qBAAqB,GAAE,gBAAvB,gBAAAC,IAAA,KAAAD;AAAA,MACb,UAASG,OAAAD,MAAA,qBAAqB,GAAE,YAAvB,gBAAAC,IAAA,KAAAD;AAAA,MACT,OAAMG,OAAAD,MAAA,qBAAqB,GAAE,SAAvB,gBAAAC,IAAA,KAAAD;AAAA,IACR;AAAA,EACF,CAAC;AAED,QAAM,WAA6C,OACjD,QACA,YACG;AA9HP,QAAAJ,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC,KAAA;AA+HI,UAAM,gBAAeR,MAAA,WAAW,MAAX,OAAAA,MAAgB,CAAC;AACtC,eAAO,oCAAkB;AAAA,MACvB,KAAK,IAAI;AAAA,MACT;AAAA,MACA,cAAaE,OAAAD,MAAA,qBAAqB,GAAE,gBAAvB,gBAAAC,IAAA,KAAAD;AAAA,MACb,SAAS,EAAE,GAAG,cAAc,SAAS,GAAG,mCAAS,QAAQ;AAAA,MACzD,MAAM;AAAA,QACJ,GAAG,cAAc;AAAA,QACjB,GAAG,mCAAS;AAAA,MACd;AAAA;AAAA,MAEA,kBACEM,OAAAH,OAAAD,MAAA,qBAAqB,GAAE,mBAAvB,gBAAAC,IAAA,KAAAD,SAAA,OAAAI,QACAD,OAAAD,MAAA,qBAAqB,GAAE,eAAvB,gBAAAC,IAAA,KAAAD,UAA0C,UACtC,SACA;AAAA,MACN,eAAe;AAAA,MACf,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,aAAY,MAAAG,MAAA,qBAAqB,GAAE,eAAvB,wBAAAA;AAAA,MACZ,WAAU,gCAAqB,GAAE,aAAvB;AAAA,MACV,UAAS,gCAAqB,GAAE,YAAvB;AAAA,MACT,QAAQ,UAAQ;AACd,sBAAc,CAAC,GAAG,cAAc,GAAI,sBAAQ,CAAC,CAAE,CAAC;AAAA,MAClD;AAAA,MACA,QAAO,gCAAqB,GAAE,UAAvB;AAAA,IACT,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,gBAAgB,GAAG;AACrB,sBAAgB,EAAG,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,QAAM,gBAAgB,CAACC,gBAAuB;AAC5C,WAAOA,WAAU;AAAA,EACnB;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI;AAAA,KACxB,sCAAqB,GAAE,iBAAvB,4CAA2C;AAAA,EAC7C;AAEA,QAAM,oBACJ,WAAS;AACP,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AAEF,QAAM,eAAqD,WAAS;AAhLtE,QAAAT;AAiLI,KAAAA,MAAA,+BAAO,mBAAP,gBAAAA,IAAA;AAEA,UAAM,aAAa,MAAM;AACzB,WAAO,aAAa,SAAS,UAAU,IAAI;AAAA,EAC7C;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AAKA,SAASD,0BACP,SACA;AACA,QAAM,kBAAkB,OAAO,YAAY,aAAa,QAAQ,IAAI;AAEpE,SAAO,OAAO,QAAQ,eAAe,EAAE;AAAA,IACrC,CAAC,iBAAiB,CAAC,KAAK,KAAK,MAAM;AACjC,sBAAgB,GAAiC,QAAI;AAAA,QACnD,MAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EAGH;AACF;", "names": ["_a", "_b", "_c", "generateIdFunc", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "messages", "chatRequest", "import_ui_utils", "import_solid_js", "import_store", "store", "setStore", "convertToAccessorOptions", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "completion"]}