{"name": "@ai-sdk/solid", "version": "0.0.54", "license": "Apache-2.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist/**/*", "CHANGELOG.md"], "dependencies": {"@ai-sdk/provider-utils": "1.0.22", "@ai-sdk/ui-utils": "0.0.50"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.5", "@testing-library/user-event": "^14.5.1", "@solidjs/testing-library": "0.8.8", "@types/node": "^18", "@vitejs/plugin-vue": "4.5.0", "eslint": "^7.32.0", "jsdom": "^24.0.0", "msw": "2.3.1", "tsup": "^7.2.0", "typescript": "5.5.4", "vite-plugin-solid": "2.7.2", "@vercel/ai-tsconfig": "0.0.0", "eslint-config-vercel-ai": "0.0.0"}, "peerDependencies": {"solid-js": "^1.7.7"}, "peerDependenciesMeta": {"solid-js": {"optional": true}}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "homepage": "https://sdk.vercel.ai/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai", "solid"], "scripts": {"build": "tsup", "clean": "rm -rf dist", "dev": "tsup --watch", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --noEmit", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "vitest --config vitest.config.js --run", "test:watch": "vitest --config vitest.config.js"}}