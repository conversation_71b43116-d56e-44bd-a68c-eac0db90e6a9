{"version": 3, "sources": ["../src/index.ts", "../src/use-chat.ts", "../src/use-completion.ts", "../src/use-assistant.ts"], "sourcesContent": ["export * from './use-chat';\nexport * from './use-completion';\nexport * from './use-assistant';\n", "import type {\n  ChatRe<PERSON>,\n  ChatRequestOptions,\n  CreateMessage,\n  JSONValue,\n  Message,\n  UseChatOptions,\n} from '@ai-sdk/ui-utils';\nimport {\n  callChatApi,\n  generateId as generateIdFunc,\n  processChatStream,\n} from '@ai-sdk/ui-utils';\nimport swrv from 'swrv';\nimport type { Ref } from 'vue';\nimport { ref, unref } from 'vue';\n\nexport type { CreateMessage, Message, UseChatOptions };\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: Ref<Message[]>;\n  /** The error object of the API request */\n  error: Ref<undefined | Error>;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (\n    messages: Message[] | ((messages: Message[]) => Message[]),\n  ) => void;\n  /** The current value of the input */\n  input: Ref<string>;\n  /** Form submission handler to automatically reset input and append a user message  */\n  handleSubmit: (\n    event?: { preventDefault?: () => void },\n    chatRequestOptions?: ChatRequestOptions,\n  ) => void;\n  /** Whether the API request is in progress */\n  isLoading: Ref<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData. */\n  data: Ref<JSONValue[] | undefined>;\n  /** Set the data of the chat. You can use this to transform or clear the chat data. */\n  setData: (\n    data:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => void;\n\n  addToolResult: ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => void;\n};\n\nlet uniqueId = 0;\n\n// @ts-expect-error - some issues with the default export of useSWRV\nconst useSWRV = (swrv.default as typeof import('swrv')['default']) || swrv;\nconst store: Record<string, Message[] | undefined> = {};\n\nexport function useChat(\n  {\n    api = '/api/chat',\n    id,\n    initialMessages = [],\n    initialInput = '',\n    sendExtraMessageFields,\n    experimental_onFunctionCall,\n    streamMode,\n    streamProtocol,\n    onResponse,\n    onFinish,\n    onError,\n    credentials,\n    headers: metadataHeaders,\n    body: metadataBody,\n    generateId = generateIdFunc,\n    onToolCall,\n    fetch,\n    keepLastMessageOnError = false,\n    maxSteps,\n  }: UseChatOptions & {\n    /**\n     * Maximum number of sequential LLM calls (steps), e.g. when you use tool calls. Must be at least 1.\n     * A maximum number is required to prevent infinite loops in the case of misconfigured tools.\n     * By default, it's set to 1, which means that only a single LLM call is made.\n     */\n    maxSteps?: number;\n  } = {\n    maxSteps: 1,\n  },\n): UseChatHelpers {\n  // streamMode is deprecated, use streamProtocol instead.\n  if (streamMode) {\n    streamProtocol ??= streamMode === 'text' ? 'text' : undefined;\n  }\n\n  // Generate a unique ID for the chat if not provided.\n  const chatId = id || `chat-${uniqueId++}`;\n\n  const key = `${api}|${chatId}`;\n  const { data: messagesData, mutate: originalMutate } = useSWRV<Message[]>(\n    key,\n    () => store[key] || initialMessages,\n  );\n\n  const { data: isLoading, mutate: mutateLoading } = useSWRV<boolean>(\n    `${chatId}-loading`,\n    null,\n  );\n\n  isLoading.value ??= false;\n\n  // Force the `data` to be `initialMessages` if it's `undefined`.\n  messagesData.value ??= initialMessages;\n\n  const mutate = (data?: Message[]) => {\n    store[key] = data;\n    return originalMutate();\n  };\n\n  // Because of the `initialData` option, the `data` will never be `undefined`.\n  const messages = messagesData as Ref<Message[]>;\n\n  const error = ref<undefined | Error>(undefined);\n  // cannot use JSONValue[] in ref because of infinite Typescript recursion:\n  const streamData = ref<undefined | unknown[]>(undefined);\n\n  let abortController: AbortController | null = null;\n\n  async function triggerRequest(\n    messagesSnapshot: Message[],\n    { options, data, headers, body }: ChatRequestOptions = {},\n  ) {\n    const messageCount = messages.value.length;\n\n    try {\n      error.value = undefined;\n      mutateLoading(() => true);\n\n      abortController = new AbortController();\n\n      // Do an optimistic update to the chat state to show the updated messages\n      // immediately.\n      const previousMessages = messagesSnapshot;\n      mutate(messagesSnapshot);\n\n      const requestOptions = {\n        headers: headers ?? options?.headers,\n        body: body ?? options?.body,\n      };\n\n      let chatRequest: ChatRequest = {\n        messages: messagesSnapshot,\n        options: requestOptions,\n        body: requestOptions.body,\n        headers: requestOptions.headers,\n        data,\n      };\n\n      await processChatStream({\n        getStreamedResponse: async () => {\n          const existingData = (streamData.value ?? []) as JSONValue[];\n\n          const constructedMessagesPayload = sendExtraMessageFields\n            ? chatRequest.messages\n            : chatRequest.messages.map(\n                ({\n                  role,\n                  content,\n                  name,\n                  data,\n                  annotations,\n                  toolInvocations,\n                  function_call,\n                }) => ({\n                  role,\n                  content,\n                  ...(name !== undefined && { name }),\n                  ...(data !== undefined && { data }),\n                  ...(annotations !== undefined && { annotations }),\n                  ...(toolInvocations !== undefined && { toolInvocations }),\n                  // outdated function/tool call handling (TODO deprecate):\n                  ...(function_call !== undefined && { function_call }),\n                }),\n              );\n\n          return await callChatApi({\n            api,\n            body: {\n              messages: constructedMessagesPayload,\n              data: chatRequest.data,\n              ...unref(metadataBody), // Use unref to unwrap the ref value\n              ...requestOptions.body,\n            },\n            streamProtocol,\n            headers: {\n              ...metadataHeaders,\n              ...requestOptions.headers,\n            },\n            abortController: () => abortController,\n            credentials,\n            onResponse,\n            onUpdate(merged, data) {\n              mutate([...chatRequest.messages, ...merged]);\n              streamData.value = [...existingData, ...(data ?? [])];\n            },\n            onFinish,\n            restoreMessagesOnFailure() {\n              // Restore the previous messages if the request fails.\n              if (!keepLastMessageOnError) {\n                mutate(previousMessages);\n              }\n            },\n            generateId,\n            onToolCall,\n            fetch,\n          });\n        },\n        experimental_onFunctionCall,\n        updateChatRequest(newChatRequest) {\n          chatRequest = newChatRequest;\n        },\n        getCurrentMessages: () => messages.value,\n      });\n\n      abortController = null;\n    } catch (err) {\n      // Ignore abort errors as they are expected.\n      if ((err as any).name === 'AbortError') {\n        abortController = null;\n        return null;\n      }\n\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      error.value = err as Error;\n    } finally {\n      mutateLoading(() => false);\n    }\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const lastMessage = messages.value[messages.value.length - 1];\n    if (\n      // ensure we actually have new messages (to prevent infinite loops in case of errors):\n      messages.value.length > messageCount &&\n      // ensure there is a last message:\n      lastMessage != null &&\n      // check if the feature is enabled:\n      maxSteps &&\n      maxSteps > 1 &&\n      // check that next step is possible:\n      isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n      // limit the number of automatic steps:\n      countTrailingAssistantMessages(messages.value) <= maxSteps\n    ) {\n      await triggerRequest(messages.value);\n    }\n  }\n\n  const append: UseChatHelpers['append'] = async (message, options) => {\n    if (!message.id) {\n      message.id = generateId();\n    }\n\n    return triggerRequest(messages.value.concat(message as Message), options);\n  };\n\n  const reload: UseChatHelpers['reload'] = async options => {\n    const messagesSnapshot = messages.value;\n    if (messagesSnapshot.length === 0) return null;\n\n    const lastMessage = messagesSnapshot[messagesSnapshot.length - 1];\n    if (lastMessage.role === 'assistant') {\n      return triggerRequest(messagesSnapshot.slice(0, -1), options);\n    }\n\n    return triggerRequest(messagesSnapshot, options);\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setMessages = (\n    messagesArg: Message[] | ((messages: Message[]) => Message[]),\n  ) => {\n    if (typeof messagesArg === 'function') {\n      messagesArg = messagesArg(messages.value);\n    }\n\n    mutate(messagesArg);\n  };\n\n  const setData = (\n    dataArg:\n      | JSONValue[]\n      | undefined\n      | ((data: JSONValue[] | undefined) => JSONValue[] | undefined),\n  ) => {\n    if (typeof dataArg === 'function') {\n      dataArg = dataArg(streamData.value as JSONValue[] | undefined);\n    }\n\n    streamData.value = dataArg;\n  };\n\n  const input = ref(initialInput);\n\n  const handleSubmit = (\n    event?: { preventDefault?: () => void },\n    options: ChatRequestOptions = {},\n  ) => {\n    event?.preventDefault?.();\n\n    const inputValue = input.value;\n\n    if (!inputValue && !options.allowEmptySubmit) return;\n\n    triggerRequest(\n      !inputValue && options.allowEmptySubmit\n        ? messages.value\n        : messages.value.concat({\n            id: generateId(),\n            createdAt: new Date(),\n            content: inputValue,\n            role: 'user',\n          }),\n      options,\n    );\n\n    input.value = '';\n  };\n\n  const addToolResult = ({\n    toolCallId,\n    result,\n  }: {\n    toolCallId: string;\n    result: any;\n  }) => {\n    const updatedMessages = messages.value.map((message, index, arr) =>\n      // update the tool calls in the last assistant message:\n      index === arr.length - 1 &&\n      message.role === 'assistant' &&\n      message.toolInvocations\n        ? {\n            ...message,\n            toolInvocations: message.toolInvocations.map(toolInvocation =>\n              toolInvocation.toolCallId === toolCallId\n                ? { ...toolInvocation, result }\n                : toolInvocation,\n            ),\n          }\n        : message,\n    );\n\n    mutate(updatedMessages);\n\n    // auto-submit when all tool calls in the last assistant message have results:\n    const lastMessage = updatedMessages[updatedMessages.length - 1];\n\n    if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n      triggerRequest(updatedMessages);\n    }\n  };\n\n  return {\n    messages,\n    append,\n    error,\n    reload,\n    stop,\n    setMessages,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData as Ref<undefined | JSONValue[]>,\n    setData,\n    addToolResult,\n  };\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe message must have at least one tool invocation and all tool invocations\nmust have a result.\n */\nfunction isAssistantMessageWithCompletedToolCalls(message: Message) {\n  return (\n    message.role === 'assistant' &&\n    message.toolInvocations &&\n    message.toolInvocations.length > 0 &&\n    message.toolInvocations.every(toolInvocation => 'result' in toolInvocation)\n  );\n}\n\n/**\nReturns the number of trailing assistant messages in the array.\n */\nfunction countTrailingAssistantMessages(messages: Message[]) {\n  let count = 0;\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === 'assistant') {\n      count++;\n    } else {\n      break;\n    }\n  }\n  return count;\n}\n", "import type {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n} from '@ai-sdk/ui-utils';\nimport { callCompletionApi } from '@ai-sdk/ui-utils';\nimport swrv from 'swrv';\nimport type { Ref } from 'vue';\nimport { ref, unref } from 'vue';\n\nexport type { UseCompletionOptions };\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: Ref<string>;\n  /** The error object of the API request */\n  error: Ref<undefined | Error>;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: Ref<string>;\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form @submit=\"handleSubmit\">\n   *  <input @change=\"handleInputChange\" v-model=\"input\" />\n   * </form>\n   * ```\n   */\n  handleSubmit: (event?: { preventDefault?: () => void }) => void;\n  /** Whether the API request is in progress */\n  isLoading: Ref<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData */\n  data: Ref<JSONValue[] | undefined>;\n};\n\nlet uniqueId = 0;\n\n// @ts-expect-error - some issues with the default export of useSWRV\nconst useSWRV = (swrv.default as typeof import('swrv')['default']) || swrv;\nconst store: Record<string, any> = {};\n\nexport function useCompletion({\n  api = '/api/completion',\n  id,\n  initialCompletion = '',\n  initialInput = '',\n  credentials,\n  headers,\n  body,\n  streamProtocol,\n  onResponse,\n  onFinish,\n  onError,\n  fetch,\n}: UseCompletionOptions = {}): UseCompletionHelpers {\n  // Generate an unique id for the completion if not provided.\n  const completionId = id || `completion-${uniqueId++}`;\n\n  const key = `${api}|${completionId}`;\n  const { data, mutate: originalMutate } = useSWRV<string>(\n    key,\n    () => store[key] || initialCompletion,\n  );\n\n  const { data: isLoading, mutate: mutateLoading } = useSWRV<boolean>(\n    `${completionId}-loading`,\n    null,\n  );\n\n  isLoading.value ??= false;\n\n  const { data: streamData, mutate: mutateStreamData } = useSWRV<\n    JSONValue[] | undefined\n  >(`${completionId}-data`, null);\n\n  // Force the `data` to be `initialCompletion` if it's `undefined`.\n  data.value ||= initialCompletion;\n\n  const mutate = (data: string) => {\n    store[key] = data;\n    return originalMutate();\n  };\n\n  // Because of the `initialData` option, the `data` will never be `undefined`.\n  const completion = data as Ref<string>;\n\n  const error = ref<undefined | Error>(undefined);\n\n  let abortController: AbortController | null = null;\n\n  async function triggerRequest(prompt: string, options?: RequestOptions) {\n    const existingData = (streamData.value ?? []) as JSONValue[];\n    return callCompletionApi({\n      api,\n      prompt,\n      credentials,\n      headers: {\n        ...headers,\n        ...options?.headers,\n      },\n      body: {\n        ...unref(body),\n        ...options?.body,\n      },\n      streamProtocol,\n      setCompletion: mutate,\n      setLoading: loading => mutateLoading(() => loading),\n      setError: err => {\n        error.value = err;\n      },\n      setAbortController: controller => {\n        abortController = controller;\n      },\n      onResponse,\n      onFinish,\n      onError,\n      onData: data => {\n        mutateStreamData(() => [...existingData, ...(data ?? [])]);\n      },\n      fetch,\n    });\n  }\n\n  const complete: UseCompletionHelpers['complete'] = async (\n    prompt,\n    options,\n  ) => {\n    return triggerRequest(prompt, options);\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setCompletion = (completion: string) => {\n    mutate(completion);\n  };\n\n  const input = ref(initialInput);\n\n  const handleSubmit = (event?: { preventDefault?: () => void }) => {\n    event?.preventDefault?.();\n    const inputValue = input.value;\n    return inputValue ? complete(inputValue) : undefined;\n  };\n\n  return {\n    completion,\n    complete,\n    error,\n    stop,\n    setCompletion,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "/**\n * A vue.js composable function to interact with the assistant API.\n */\n\nimport { isAbortError } from '@ai-sdk/provider-utils';\nimport { readDataStream, generateId } from '@ai-sdk/ui-utils';\nimport type {\n  AssistantStatus,\n  CreateMessage,\n  Message,\n  UseAssistantOptions,\n} from '@ai-sdk/ui-utils';\nimport { computed, readonly, ref } from 'vue';\nimport type { ComputedRef, Ref } from 'vue';\n\nexport type UseAssistantHelpers = {\n  /**\n   * The current array of chat messages.\n   */\n  messages: Ref<Message[]>;\n\n  /**\n   * Update the message store with a new array of messages.\n   */\n  setMessages: (messagesProcessor: (messages: Message[]) => Message[]) => void;\n\n  /**\n   * The current thread ID.\n   */\n  threadId: Ref<string | undefined>;\n\n  /**\n   * Set the current thread ID. Specifying a thread ID will switch to that thread, if it exists. If set to 'undefined', a new thread will be created. For both cases, `threadId` will be updated with the new value and `messages` will be cleared.\n   */\n  setThreadId: (threadId: string | undefined) => void;\n  /**\n   * The current value of the input field.\n   */\n  input: Ref<string>;\n\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param requestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => Promise<void>;\n\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: ComputedRef<() => void>;\n\n  /**\n   * Handler for the `onChange` event of the input field to control the input's value.\n   */\n  handleInputChange: (e: Event & { target: HTMLInputElement }) => void;\n\n  /**\n   * Handler for the `onSubmit` event of the form to append a user message and reset the input.\n   */\n  handleSubmit: (e: Event & { target: HTMLFormElement }) => void;\n\n  /**\n   * Whether the assistant is currently sending a message.\n   */\n  isSending: ComputedRef<boolean>;\n\n  /**\n   * The current status of the assistant.\n   */\n  status: Ref<AssistantStatus>;\n\n  /**\n   * The current error, if any.\n   */\n  error: Ref<Error | undefined>;\n};\n\nexport function useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n}: UseAssistantOptions): UseAssistantHelpers {\n  const messages: Ref<Message[]> = ref([]);\n  const input: Ref<string> = ref('');\n  const currentThreadId: Ref<string | undefined> = ref(undefined);\n  const status: Ref<AssistantStatus> = ref('awaiting_message');\n  const error: Ref<undefined | Error> = ref(undefined);\n\n  const setMessages = (messageFactory: (messages: Message[]) => Message[]) => {\n    messages.value = messageFactory(messages.value);\n  };\n\n  const setCurrentThreadId = (newThreadId: string | undefined) => {\n    currentThreadId.value = newThreadId;\n    messages.value = [];\n  };\n\n  const handleInputChange = (event: Event & { target: HTMLInputElement }) => {\n    input.value = event?.target?.value;\n  };\n\n  const isSending = computed(() => status.value === 'in_progress');\n\n  // Abort controller to cancel the current API call when required\n  const abortController = ref<AbortController | null>(null);\n\n  // memoized function to stop the current request when required\n  const stop = computed(() => {\n    return () => {\n      if (abortController.value) {\n        abortController.value.abort();\n        abortController.value = null;\n      }\n    };\n  });\n\n  const append = async (\n    message: Message | CreateMessage,\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    status.value = 'in_progress';\n\n    // Append the new message to the current list of messages\n    const newMessage: Message = {\n      ...message,\n      id: message.id ?? generateId(),\n    };\n\n    // Update the messages list with the new message\n    setMessages(messages => [...messages, newMessage]);\n\n    input.value = '';\n\n    const controller = new AbortController();\n\n    try {\n      // Assign the new controller to the abortController ref\n      abortController.value = controller;\n\n      const response = await fetch(api, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...headers,\n        },\n        body: JSON.stringify({\n          ...body,\n          // Message Content\n          message: message.content,\n\n          // Always Use User Provided Thread ID When Available\n          threadId: threadIdParam ?? currentThreadId.value ?? null,\n\n          // Optional Request Data\n          ...(requestOptions?.data && { data: requestOptions?.data }),\n        }),\n        signal: controller.signal,\n        credentials,\n      });\n\n      if (!response.ok) {\n        throw new Error(\n          response.statusText ?? 'An error occurred while sending the message',\n        );\n      }\n\n      if (!response.body) {\n        throw new Error('The response body is empty');\n      }\n\n      for await (const { type, value } of readDataStream(\n        response.body.getReader(),\n      )) {\n        switch (type) {\n          case 'assistant_message': {\n            messages.value = [\n              ...messages.value,\n              {\n                id: value.id,\n                content: value.content[0].text.value,\n                role: value.role,\n              },\n            ];\n            break;\n          }\n          case 'assistant_control_data': {\n            if (value.threadId) {\n              currentThreadId.value = value.threadId;\n            }\n\n            setMessages(messages => {\n              const lastMessage = messages[messages.length - 1];\n              lastMessage.id = value.messageId;\n\n              return [...messages.slice(0, -1), lastMessage];\n            });\n\n            break;\n          }\n\n          case 'text': {\n            setMessages(messages => {\n              const lastMessage = messages[messages.length - 1];\n              lastMessage.content += value;\n\n              return [...messages.slice(0, -1), lastMessage];\n            });\n\n            break;\n          }\n\n          case 'data_message': {\n            setMessages(messages => [\n              ...messages,\n              {\n                id: value.id ?? generateId(),\n                role: 'data',\n                content: '',\n                data: value.data,\n              },\n            ]);\n            break;\n          }\n\n          case 'error': {\n            error.value = new Error(value);\n          }\n\n          default: {\n            console.error('Unknown message type:', type);\n            break;\n          }\n        }\n      }\n    } catch (err) {\n      // If the error is an AbortError and the signal is aborted, reset the abortController and do nothing.\n      if (isAbortError(err) && abortController.value?.signal.aborted) {\n        abortController.value = null;\n        return;\n      }\n\n      // If an error handler is provided, call it with the error\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      error.value = err as Error;\n    } finally {\n      // Reset the status to 'awaiting_message' after the request is complete\n      abortController.value = null;\n      status.value = 'awaiting_message';\n    }\n  };\n\n  const submitMessage = async (\n    event: Event & { target: HTMLFormElement },\n    requestOptions?: {\n      data?: Record<string, string>;\n    },\n  ) => {\n    event?.preventDefault?.();\n\n    if (!input.value) return;\n\n    append(\n      {\n        role: 'user',\n        content: input.value,\n      },\n      requestOptions,\n    );\n  };\n\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: readonly(currentThreadId),\n    setThreadId: setCurrentThreadId,\n    input,\n    handleInputChange,\n    handleSubmit: submitMessage,\n    isSending,\n    status,\n    error,\n    stop,\n  };\n}\n\n/**\n * @deprecated Use `useAssistant` instead.\n */\nexport const experimental_useAssistant = useAssistant;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACQA,sBAIO;AACP,kBAAiB;AAEjB,iBAA2B;AAkE3B,IAAI,WAAW;AAGf,IAAM,UAAW,YAAAA,QAAK,WAAgD,YAAAA;AACtE,IAAM,QAA+C,CAAC;AAE/C,SAAS,QACd;AAAA,EACE,MAAM;AAAA,EACN;AAAA,EACA,kBAAkB,CAAC;AAAA,EACnB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,MAAM;AAAA,EACN,YAAAC,cAAa,gBAAAC;AAAA,EACb;AAAA,EACA,OAAAC;AAAA,EACA,yBAAyB;AAAA,EACzB;AACF,IAOI;AAAA,EACF,UAAU;AACZ,GACgB;AAtHlB;AAwHE,MAAI,YAAY;AACd,+DAAmB,eAAe,SAAS,SAAS;AAAA,EACtD;AAGA,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,QAAM,MAAM,GAAG,GAAG,IAAI,MAAM;AAC5B,QAAM,EAAE,MAAM,cAAc,QAAQ,eAAe,IAAI;AAAA,IACrD;AAAA,IACA,MAAM,MAAM,GAAG,KAAK;AAAA,EACtB;AAEA,QAAM,EAAE,MAAM,WAAW,QAAQ,cAAc,IAAI;AAAA,IACjD,GAAG,MAAM;AAAA,IACT;AAAA,EACF;AAEA,kBAAU,UAAV,sBAAU,QAAU;AAGpB,qBAAa,UAAb,yBAAa,QAAU;AAEvB,QAAM,SAAS,CAAC,SAAqB;AACnC,UAAM,GAAG,IAAI;AACb,WAAO,eAAe;AAAA,EACxB;AAGA,QAAM,WAAW;AAEjB,QAAM,YAAQ,gBAAuB,MAAS;AAE9C,QAAM,iBAAa,gBAA2B,MAAS;AAEvD,MAAI,kBAA0C;AAE9C,iBAAe,eACb,kBACA,EAAE,SAAS,MAAM,SAAS,KAAK,IAAwB,CAAC,GACxD;AACA,UAAM,eAAe,SAAS,MAAM;AAEpC,QAAI;AACF,YAAM,QAAQ;AACd,oBAAc,MAAM,IAAI;AAExB,wBAAkB,IAAI,gBAAgB;AAItC,YAAM,mBAAmB;AACzB,aAAO,gBAAgB;AAEvB,YAAM,iBAAiB;AAAA,QACrB,SAAS,4BAAW,mCAAS;AAAA,QAC7B,MAAM,sBAAQ,mCAAS;AAAA,MACzB;AAEA,UAAI,cAA2B;AAAA,QAC7B,UAAU;AAAA,QACV,SAAS;AAAA,QACT,MAAM,eAAe;AAAA,QACrB,SAAS,eAAe;AAAA,QACxB;AAAA,MACF;AAEA,gBAAM,mCAAkB;AAAA,QACtB,qBAAqB,YAAY;AA5LzC,cAAAC;AA6LU,gBAAM,gBAAgBA,MAAA,WAAW,UAAX,OAAAA,MAAoB,CAAC;AAE3C,gBAAM,6BAA6B,yBAC/B,YAAY,WACZ,YAAY,SAAS;AAAA,YACnB,CAAC;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA,MAAAC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,GAAI,SAAS,UAAa,EAAE,KAAK;AAAA,cACjC,GAAIA,UAAS,UAAa,EAAE,MAAAA,MAAK;AAAA,cACjC,GAAI,gBAAgB,UAAa,EAAE,YAAY;AAAA,cAC/C,GAAI,oBAAoB,UAAa,EAAE,gBAAgB;AAAA;AAAA,cAEvD,GAAI,kBAAkB,UAAa,EAAE,cAAc;AAAA,YACrD;AAAA,UACF;AAEJ,iBAAO,UAAM,6BAAY;AAAA,YACvB;AAAA,YACA,MAAM;AAAA,cACJ,UAAU;AAAA,cACV,MAAM,YAAY;AAAA,cAClB,OAAG,kBAAM,YAAY;AAAA;AAAA,cACrB,GAAG,eAAe;AAAA,YACpB;AAAA,YACA;AAAA,YACA,SAAS;AAAA,cACP,GAAG;AAAA,cACH,GAAG,eAAe;AAAA,YACpB;AAAA,YACA,iBAAiB,MAAM;AAAA,YACvB;AAAA,YACA;AAAA,YACA,SAAS,QAAQA,OAAM;AACrB,qBAAO,CAAC,GAAG,YAAY,UAAU,GAAG,MAAM,CAAC;AAC3C,yBAAW,QAAQ,CAAC,GAAG,cAAc,GAAIA,SAAA,OAAAA,QAAQ,CAAC,CAAE;AAAA,YACtD;AAAA,YACA;AAAA,YACA,2BAA2B;AAEzB,kBAAI,CAAC,wBAAwB;AAC3B,uBAAO,gBAAgB;AAAA,cACzB;AAAA,YACF;AAAA,YACA,YAAAJ;AAAA,YACA;AAAA,YACA,OAAAE;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA;AAAA,QACA,kBAAkB,gBAAgB;AAChC,wBAAc;AAAA,QAChB;AAAA,QACA,oBAAoB,MAAM,SAAS;AAAA,MACrC,CAAC;AAED,wBAAkB;AAAA,IACpB,SAAS,KAAK;AAEZ,UAAK,IAAY,SAAS,cAAc;AACtC,0BAAkB;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,IAChB,UAAE;AACA,oBAAc,MAAM,KAAK;AAAA,IAC3B;AAGA,UAAM,cAAc,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC;AAC5D;AAAA;AAAA,MAEE,SAAS,MAAM,SAAS;AAAA,MAExB,eAAe;AAAA,MAEf,YACA,WAAW;AAAA,MAEX,yCAAyC,WAAW;AAAA,MAEpD,+BAA+B,SAAS,KAAK,KAAK;AAAA,MAClD;AACA,YAAM,eAAe,SAAS,KAAK;AAAA,IACrC;AAAA,EACF;AAEA,QAAM,SAAmC,OAAO,SAAS,YAAY;AACnE,QAAI,CAAC,QAAQ,IAAI;AACf,cAAQ,KAAKF,YAAW;AAAA,IAC1B;AAEA,WAAO,eAAe,SAAS,MAAM,OAAO,OAAkB,GAAG,OAAO;AAAA,EAC1E;AAEA,QAAM,SAAmC,OAAM,YAAW;AACxD,UAAM,mBAAmB,SAAS;AAClC,QAAI,iBAAiB,WAAW;AAAG,aAAO;AAE1C,UAAM,cAAc,iBAAiB,iBAAiB,SAAS,CAAC;AAChE,QAAI,YAAY,SAAS,aAAa;AACpC,aAAO,eAAe,iBAAiB,MAAM,GAAG,EAAE,GAAG,OAAO;AAAA,IAC9D;AAEA,WAAO,eAAe,kBAAkB,OAAO;AAAA,EACjD;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,cAAc,CAClB,gBACG;AACH,QAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAc,YAAY,SAAS,KAAK;AAAA,IAC1C;AAEA,WAAO,WAAW;AAAA,EACpB;AAEA,QAAM,UAAU,CACd,YAIG;AACH,QAAI,OAAO,YAAY,YAAY;AACjC,gBAAU,QAAQ,WAAW,KAAgC;AAAA,IAC/D;AAEA,eAAW,QAAQ;AAAA,EACrB;AAEA,QAAM,YAAQ,gBAAI,YAAY;AAE9B,QAAM,eAAe,CACnB,OACA,UAA8B,CAAC,MAC5B;AAxVP,QAAAG;AAyVI,KAAAA,MAAA,+BAAO,mBAAP,gBAAAA,IAAA;AAEA,UAAM,aAAa,MAAM;AAEzB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAAkB;AAE9C;AAAA,MACE,CAAC,cAAc,QAAQ,mBACnB,SAAS,QACT,SAAS,MAAM,OAAO;AAAA,QACpB,IAAIH,YAAW;AAAA,QACf,WAAW,oBAAI,KAAK;AAAA,QACpB,SAAS;AAAA,QACT,MAAM;AAAA,MACR,CAAC;AAAA,MACL;AAAA,IACF;AAEA,UAAM,QAAQ;AAAA,EAChB;AAEA,QAAM,gBAAgB,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF,MAGM;AACJ,UAAM,kBAAkB,SAAS,MAAM;AAAA,MAAI,CAAC,SAAS,OAAO;AAAA;AAAA,QAE1D,UAAU,IAAI,SAAS,KACvB,QAAQ,SAAS,eACjB,QAAQ,kBACJ;AAAA,UACE,GAAG;AAAA,UACH,iBAAiB,QAAQ,gBAAgB;AAAA,YAAI,oBAC3C,eAAe,eAAe,aAC1B,EAAE,GAAG,gBAAgB,OAAO,IAC5B;AAAA,UACN;AAAA,QACF,IACA;AAAA;AAAA,IACN;AAEA,WAAO,eAAe;AAGtB,UAAM,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAE9D,QAAI,yCAAyC,WAAW,GAAG;AACzD,qBAAe,eAAe;AAAA,IAChC;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,yCAAyC,SAAkB;AAClE,SACE,QAAQ,SAAS,eACjB,QAAQ,mBACR,QAAQ,gBAAgB,SAAS,KACjC,QAAQ,gBAAgB,MAAM,oBAAkB,YAAY,cAAc;AAE9E;AAKA,SAAS,+BAA+B,UAAqB;AAC3D,MAAI,QAAQ;AACZ,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AACpC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACrbA,IAAAK,mBAAkC;AAClC,IAAAC,eAAiB;AAEjB,IAAAC,cAA2B;AA2C3B,IAAIC,YAAW;AAGf,IAAMC,WAAW,aAAAC,QAAK,WAAgD,aAAAA;AACtE,IAAMC,SAA6B,CAAC;AAE7B,SAAS,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN;AAAA,EACA,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC;AACF,IAA0B,CAAC,GAAyB;AAtEpD;AAwEE,QAAM,eAAe,MAAM,cAAcJ,WAAU;AAEnD,QAAM,MAAM,GAAG,GAAG,IAAI,YAAY;AAClC,QAAM,EAAE,MAAM,QAAQ,eAAe,IAAIC;AAAA,IACvC;AAAA,IACA,MAAME,OAAM,GAAG,KAAK;AAAA,EACtB;AAEA,QAAM,EAAE,MAAM,WAAW,QAAQ,cAAc,IAAIF;AAAA,IACjD,GAAG,YAAY;AAAA,IACf;AAAA,EACF;AAEA,kBAAU,UAAV,sBAAU,QAAU;AAEpB,QAAM,EAAE,MAAM,YAAY,QAAQ,iBAAiB,IAAIA,SAErD,GAAG,YAAY,SAAS,IAAI;AAG9B,OAAK,UAAL,KAAK,QAAU;AAEf,QAAM,SAAS,CAACI,UAAiB;AAC/B,IAAAF,OAAM,GAAG,IAAIE;AACb,WAAO,eAAe;AAAA,EACxB;AAGA,QAAM,aAAa;AAEnB,QAAM,YAAQ,iBAAuB,MAAS;AAE9C,MAAI,kBAA0C;AAE9C,iBAAe,eAAe,QAAgB,SAA0B;AA1G1E,QAAAC;AA2GI,UAAM,gBAAgBA,MAAA,WAAW,UAAX,OAAAA,MAAoB,CAAC;AAC3C,eAAO,oCAAkB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,mCAAS;AAAA,MACd;AAAA,MACA,MAAM;AAAA,QACJ,OAAG,mBAAM,IAAI;AAAA,QACb,GAAG,mCAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,YAAY,aAAW,cAAc,MAAM,OAAO;AAAA,MAClD,UAAU,SAAO;AACf,cAAM,QAAQ;AAAA,MAChB;AAAA,MACA,oBAAoB,gBAAc;AAChC,0BAAkB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,CAAAD,UAAQ;AACd,yBAAiB,MAAM,CAAC,GAAG,cAAc,GAAIA,SAAA,OAAAA,QAAQ,CAAC,CAAE,CAAC;AAAA,MAC3D;AAAA,MACA,OAAAD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,WAA6C,OACjD,QACA,YACG;AACH,WAAO,eAAe,QAAQ,OAAO;AAAA,EACvC;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,gBAAgB,CAACG,gBAAuB;AAC5C,WAAOA,WAAU;AAAA,EACnB;AAEA,QAAM,YAAQ,iBAAI,YAAY;AAE9B,QAAM,eAAe,CAAC,UAA4C;AA/JpE,QAAAD;AAgKI,KAAAA,MAAA,+BAAO,mBAAP,gBAAAA,IAAA;AACA,UAAM,aAAa,MAAM;AACzB,WAAO,aAAa,SAAS,UAAU,IAAI;AAAA,EAC7C;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;;;AC5KA,4BAA6B;AAC7B,IAAAE,mBAA2C;AAO3C,IAAAC,cAAwC;AAwEjC,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA6C;AAC3C,QAAM,eAA2B,iBAAI,CAAC,CAAC;AACvC,QAAM,YAAqB,iBAAI,EAAE;AACjC,QAAM,sBAA2C,iBAAI,MAAS;AAC9D,QAAM,aAA+B,iBAAI,kBAAkB;AAC3D,QAAM,YAAgC,iBAAI,MAAS;AAEnD,QAAM,cAAc,CAAC,mBAAuD;AAC1E,aAAS,QAAQ,eAAe,SAAS,KAAK;AAAA,EAChD;AAEA,QAAM,qBAAqB,CAAC,gBAAoC;AAC9D,oBAAgB,QAAQ;AACxB,aAAS,QAAQ,CAAC;AAAA,EACpB;AAEA,QAAM,oBAAoB,CAAC,UAAgD;AA3G7E;AA4GI,UAAM,SAAQ,oCAAO,WAAP,mBAAe;AAAA,EAC/B;AAEA,QAAM,gBAAY,sBAAS,MAAM,OAAO,UAAU,aAAa;AAG/D,QAAM,sBAAkB,iBAA4B,IAAI;AAGxD,QAAM,WAAO,sBAAS,MAAM;AAC1B,WAAO,MAAM;AACX,UAAI,gBAAgB,OAAO;AACzB,wBAAgB,MAAM,MAAM;AAC5B,wBAAgB,QAAQ;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,CAAC;AAED,QAAM,SAAS,OACb,SACA,mBAGG;AAnIP;AAoII,WAAO,QAAQ;AAGf,UAAM,aAAsB;AAAA,MAC1B,GAAG;AAAA,MACH,KAAI,aAAQ,OAAR,gBAAc,6BAAW;AAAA,IAC/B;AAGA,gBAAY,CAAAC,cAAY,CAAC,GAAGA,WAAU,UAAU,CAAC;AAEjD,UAAM,QAAQ;AAEd,UAAM,aAAa,IAAI,gBAAgB;AAEvC,QAAI;AAEF,sBAAgB,QAAQ;AAExB,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,GAAG;AAAA,QACL;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,GAAG;AAAA;AAAA,UAEH,SAAS,QAAQ;AAAA;AAAA,UAGjB,WAAU,6CAAiB,gBAAgB,UAAjC,YAA0C;AAAA;AAAA,UAGpD,IAAI,iDAAgB,SAAQ,EAAE,MAAM,iDAAgB,KAAK;AAAA,QAC3D,CAAC;AAAA,QACD,QAAQ,WAAW;AAAA,QACnB;AAAA,MACF,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,WACR,cAAS,eAAT,YAAuB;AAAA,QACzB;AAAA,MACF;AAEA,UAAI,CAAC,SAAS,MAAM;AAClB,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AAEA,uBAAiB,EAAE,MAAM,MAAM,SAAK;AAAA,QAClC,SAAS,KAAK,UAAU;AAAA,MAC1B,GAAG;AACD,gBAAQ,MAAM;AAAA,UACZ,KAAK,qBAAqB;AACxB,qBAAS,QAAQ;AAAA,cACf,GAAG,SAAS;AAAA,cACZ;AAAA,gBACE,IAAI,MAAM;AAAA,gBACV,SAAS,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,gBAC/B,MAAM,MAAM;AAAA,cACd;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,KAAK,0BAA0B;AAC7B,gBAAI,MAAM,UAAU;AAClB,8BAAgB,QAAQ,MAAM;AAAA,YAChC;AAEA,wBAAY,CAAAA,cAAY;AACtB,oBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,0BAAY,KAAK,MAAM;AAEvB,qBAAO,CAAC,GAAGA,UAAS,MAAM,GAAG,EAAE,GAAG,WAAW;AAAA,YAC/C,CAAC;AAED;AAAA,UACF;AAAA,UAEA,KAAK,QAAQ;AACX,wBAAY,CAAAA,cAAY;AACtB,oBAAM,cAAcA,UAASA,UAAS,SAAS,CAAC;AAChD,0BAAY,WAAW;AAEvB,qBAAO,CAAC,GAAGA,UAAS,MAAM,GAAG,EAAE,GAAG,WAAW;AAAA,YAC/C,CAAC;AAED;AAAA,UACF;AAAA,UAEA,KAAK,gBAAgB;AACnB,wBAAY,CAAAA,cAAS;AAhOjC,kBAAAC;AAgOoC;AAAA,gBACtB,GAAGD;AAAA,gBACH;AAAA,kBACE,KAAIC,MAAA,MAAM,OAAN,OAAAA,UAAY,6BAAW;AAAA,kBAC3B,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,MAAM,MAAM;AAAA,gBACd;AAAA,cACF;AAAA,aAAC;AACD;AAAA,UACF;AAAA,UAEA,KAAK,SAAS;AACZ,kBAAM,QAAQ,IAAI,MAAM,KAAK;AAAA,UAC/B;AAAA,UAEA,SAAS;AACP,oBAAQ,MAAM,yBAAyB,IAAI;AAC3C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AAEZ,cAAI,oCAAa,GAAG,OAAK,qBAAgB,UAAhB,mBAAuB,OAAO,UAAS;AAC9D,wBAAgB,QAAQ;AACxB;AAAA,MACF;AAGA,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,IAChB,UAAE;AAEA,sBAAgB,QAAQ;AACxB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,gBAAgB,OACpB,OACA,mBAGG;AA/QP;AAgRI,yCAAO,mBAAP;AAEA,QAAI,CAAC,MAAM;AAAO;AAElB;AAAA,MACE;AAAA,QACE,MAAM;AAAA,QACN,SAAS,MAAM;AAAA,MACjB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAU,sBAAS,eAAe;AAAA,IAClC,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKO,IAAM,4BAA4B;", "names": ["swrv", "generateId", "generateIdFunc", "fetch", "_a", "data", "import_ui_utils", "import_swrv", "import_vue", "uniqueId", "useSWRV", "swrv", "store", "fetch", "data", "_a", "completion", "import_ui_utils", "import_vue", "messages", "_a"]}