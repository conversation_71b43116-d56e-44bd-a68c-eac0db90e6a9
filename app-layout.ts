import type { Metadata } from 'next';
import { Inter, Fira_Code } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from './providers/theme-provider';
import { Toaster } from 'react-hot-toast';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const firaCode = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira-code',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Cerebras Studio - AI-Powered Creative Platform',
  description: 'Experience lightning-fast AI content generation with Cerebras ultra-high speed inference. Create, code, and collaborate with the world\'s fastest AI models.',
  keywords: [
    'AI',
    'Cerebras',
    'artificial intelligence',
    'content generation',
    'code generation',
    'creative writing',
    'fast inference',
    'machine learning'
  ],
  authors: [{ name: 'Cerebras Studio Team' }],
  creator: 'Cerebras Studio',
  publisher: 'Cerebras Studio',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Cerebras Studio - AI-Powered Creative Platform',
    description: 'Experience lightning-fast AI content generation with Cerebras ultra-high speed inference.',
    url: 'https://cerebras-studio.vercel.app',
    siteName: 'Cerebras Studio',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Cerebras Studio - AI-Powered Creative Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Cerebras Studio - AI-Powered Creative Platform',
    description: 'Experience lightning-fast AI content generation with Cerebras ultra-high speed inference.',
    images: ['/og-image.png'],
    creator: '@cerebras_studio',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/manifest.json',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#111827' },
  ],
  category: 'Technology',
  applicationName: 'Cerebras Studio',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Cerebras Studio',
  },
  formatDetection: {
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="/fonts/cal-sans.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//api.cerebras.ai" />
        
        {/* Additional meta tags */}
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        
        {/* Analytics placeholder */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Analytics initialization would go here
              console.log('Cerebras Studio loaded');
            `,
          }}
        />
      </head>
      <body 
        className={`${inter.variable} ${firaCode.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {/* Skip to main content for accessibility */}
          <a
            className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
            href="#main-content"
          >
            Skip to main content
          </a>
          
          {/* Main application */}
          <div id="main-content" className="min-h-screen">
            {children}
          </div>
          
          {/* Toast notifications */}
          <Toaster
            position="bottom-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--toast-bg)',
                color: 'var(--toast-color)',
                border: '1px solid var(--toast-border)',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#ffffff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#ffffff',
                },
              },
            }}
          />
          
          {/* Performance monitoring */}
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Performance monitoring
                if (typeof window !== 'undefined') {
                  window.addEventListener('load', () => {
                    // Log performance metrics
                    setTimeout(() => {
                      const navigation = performance.getEntriesByType('navigation')[0];
                      console.log('Page load time:', navigation.loadEventEnd - navigation.fetchStart, 'ms');
                    }, 0);
                  });
                }
              `,
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  );
}