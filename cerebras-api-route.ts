import { NextRequest, NextResponse } from 'next/server';
import { createCerebras } from '@ai-sdk/cerebras';
import { streamText, convertToCoreMessages } from 'ai';

// Initialize Cerebras client
const cerebras = createCerebras({
  apiKey: process.env.CEREBRAS_API_KEY || '',
});

export async function POST(req: NextRequest) {
  try {
    const { messages, model = 'llama-4-scout-17b-16e-instruct', temperature = 0.7, maxTokens = 2048 } = await req.json();

    if (!process.env.CEREBRAS_API_KEY) {
      return NextResponse.json(
        { error: 'Cerebras API key not configured' },
        { status: 500 }
      );
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    // Convert messages to the format expected by the AI SDK
    const coreMessages = convertToCoreMessages(messages);

    // Create streaming response using Cerebras
    const result = await streamText({
      model: cerebras(model),
      messages: coreMessages,
      temperature,
      maxTokens,
      async onFinish({ text, toolCalls, toolResults, usage, finishReason }) {
        // Log completion for monitoring
        console.log('Generation completed:', {
          model,
          usage,
          finishReason,
          textLength: text.length,
        });
      },
    });

    // Return streaming response
    return result.toAIStreamResponse();
  } catch (error) {
    console.error('Cerebras API error:', error);
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
      
      if (error.message.includes('unauthorized') || error.message.includes('invalid key')) {
        return NextResponse.json(
          { error: 'Invalid API key. Please check your Cerebras API key.' },
          { status: 401 }
        );
      }
      
      if (error.message.includes('model not found')) {
        return NextResponse.json(
          { error: 'The specified model is not available.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'An error occurred while generating content. Please try again.' },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// Health check endpoint
export async function GET(req: NextRequest) {
  try {
    const hasApiKey = !!process.env.CEREBRAS_API_KEY;
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      hasApiKey,
      availableModels: [
        'llama-4-scout-17b-16e-instruct',
        'llama-3.3-70b',
        'llama3.1-8b',
      ],
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Health check failed' },
      { status: 500 }
    );
  }
}