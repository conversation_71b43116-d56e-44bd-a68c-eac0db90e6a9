{"extends": ["next/core-web-vitals", "eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-console": "warn", "no-debugger": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "prefer-const": "error", "prefer-template": "error", "sort-imports": ["error", {"ignoreCase": false, "ignoreDeclarationSort": true, "ignoreMemberSort": false, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"], "allowSeparatedGroups": true}]}, "env": {"browser": true, "es2022": true, "node": true}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["*.js", "*.jsx"], "rules": {"@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, {"files": ["*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off"}}], "ignorePatterns": ["node_modules/", ".next/", "out/", "dist/", "build/", "public/", "*.config.js", "*.config.ts"]}